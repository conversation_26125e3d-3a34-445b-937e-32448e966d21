/**
 * CTA Button Fixes
 * This file ensures proper display of CTA buttons in service cards
 */

/* Remove border from service card footer */
.service-card-footer {
  border-top: none !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* Fix CTA button display */
.service-cta-button {
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-height: 48px !important;
  padding: 14px 20px !important;
  line-height: 1.2 !important;
  text-align: center !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  width: 100% !important;
  box-sizing: border-box !important;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%) !important;
  color: white !important;
  text-decoration: none !important;
  border: none !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

.service-cta-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15) !important;
}

/* Dark theme adjustments */
.dark-theme .service-cta-button {
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3) !important;
}

.dark-theme .service-cta-button:hover {
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .service-cta-button {
    padding: 12px 16px !important;
    font-size: 0.95rem !important;
  }
}
