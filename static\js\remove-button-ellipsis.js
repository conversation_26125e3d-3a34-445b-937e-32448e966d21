/**
 * Remove <PERSON>ton Ellipsis
 * This script ensures no ellipsis appears in button text
 */

document.addEventListener('DOMContentLoaded', function() {
    // Apply fixes immediately and after a short delay to ensure they take effect
    removeButtonEllipsis();
    setTimeout(removeButtonEllipsis, 100);
    setTimeout(removeButtonEllipsis, 500);
    setTimeout(removeButtonEllipsis, 1000);
});

function removeButtonEllipsis() {
    // Target all service CTA buttons
    const buttons = document.querySelectorAll('.service-cta-button');
    
    buttons.forEach(function(button) {
        // Get current text
        const buttonText = button.textContent;
        
        if (buttonText) {
            // Remove any ellipsis that might be in the text
            const cleanText = buttonText.trim().replace(/\s*\.{3,}\s*$/, '');
            
            // Only update if text has changed
            if (cleanText !== buttonText) {
                button.textContent = cleanText;
            }
            
            // Ensure proper styling
            button.style.whiteSpace = 'normal';
            button.style.overflow = 'visible';
            button.style.textOverflow = 'clip';
        }
    });
}
