/**
 * Service Button Width Fix
 * This script ensures all service buttons have the same width and display properly
 */

(function() {
    // Function to fix service button widths
    function fixServiceButtonWidths() {
        // Get all service CTA buttons
        const buttons = document.querySelectorAll('.service-cta-button');
        
        // Apply fixes to each button
        buttons.forEach(button => {
            // Remove any inline styles that might be overriding our CSS
            button.removeAttribute('style');
            
            // Apply our styles directly
            button.style.width = '100%';
            button.style.maxWidth = '100%';
            button.style.minWidth = '0';
            button.style.boxSizing = 'border-box';
            button.style.display = 'flex';
            button.style.justifyContent = 'center';
            button.style.alignItems = 'center';
            button.style.padding = '14px 20px';
            button.style.textAlign = 'center';
            button.style.whiteSpace = 'normal';
            button.style.overflow = 'visible';
            button.style.textOverflow = 'clip';
            button.style.wordBreak = 'normal';
            button.style.wordWrap = 'normal';
            button.style.hyphens = 'none';
            button.style.lineHeight = '1.2';
            button.style.minHeight = '48px';
            button.style.height = 'auto';
            button.style.fontSize = '1rem';
        });
        
        // Get all service cards
        const cards = document.querySelectorAll('.service-card');
        
        // Apply fixes to each card
        cards.forEach(card => {
            // Remove any inline styles that might be overriding our CSS
            card.removeAttribute('style');
            
            // Apply our styles directly
            card.style.width = '100%';
            card.style.maxWidth = 'none';
            card.style.minWidth = '0';
            card.style.boxSizing = 'border-box';
            card.style.height = '520px';
            card.style.minHeight = '520px';
            card.style.maxHeight = '520px';
            card.style.display = 'flex';
            card.style.flexDirection = 'column';
            card.style.position = 'relative';
            card.style.paddingBottom = '120px';
        });
        
        // Get all service card footers
        const footers = document.querySelectorAll('.service-card-footer');
        
        // Apply fixes to each footer
        footers.forEach(footer => {
            // Remove any inline styles that might be overriding our CSS
            footer.removeAttribute('style');
            
            // Apply our styles directly
            footer.style.width = '100%';
            footer.style.maxWidth = '100%';
            footer.style.minWidth = '0';
            footer.style.boxSizing = 'border-box';
            footer.style.padding = '0 24px 24px';
            footer.style.position = 'absolute';
            footer.style.bottom = '30px';
            footer.style.left = '0';
            footer.style.right = '0';
        });
    }
    
    // Run on page load
    document.addEventListener('DOMContentLoaded', fixServiceButtonWidths);
    
    // Run after a short delay to ensure all styles are loaded
    setTimeout(fixServiceButtonWidths, 100);
    setTimeout(fixServiceButtonWidths, 500);
    
    // Run when window is resized
    window.addEventListener('resize', fixServiceButtonWidths);
    
    // Run when theme changes
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('change', function() {
            setTimeout(fixServiceButtonWidths, 100);
        });
    }
})();
