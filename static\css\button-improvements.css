/* Button Improvements CSS */

/* CTA Button Alignment and Hover Effects */
.cta-buttons {
    display: flex !important;
    justify-content: center !important;
    align-items: stretch !important;
    gap: 1rem !important;
    flex-wrap: nowrap !important;
    margin-top: 2rem;
    width: 100% !important;
    max-width: 900px !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

.hero-cta {
    display: flex !important;
    justify-content: center !important;
    align-items: stretch !important;
    gap: 1rem !important;
    flex-wrap: nowrap !important;
    margin-top: 2rem;
    width: 100% !important;
    max-width: 900px !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* FORCE EXACT SAME HEIGHT FOR ALL CTA BUTTONS */
.cta-btn, .btn, .hero-cta .btn, .hero-section .btn, .cta-buttons .btn, .cta-buttons .cta-btn,
.hero-cta a, .cta-buttons a, .hero-section a {
    height: 60px !important;
    min-height: 60px !important;
    max-height: 60px !important;
    flex: 1 !important;
    max-width: 450px !important;
    padding: 0 24px !important;
    font-size: 0.95rem !important;
    text-align: center !important;
    white-space: normal !important;
    line-height: 1.3 !important;
    border-radius: 12px !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    cursor: pointer !important;
}

/* Primary button styling */
.btn-primary, .cta-btn.primary {
    background: #3b82f6 !important;
    background-image: none !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.btn-primary:hover, .cta-btn.primary:hover {
    background: #2563eb !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(59, 130, 246, 0.4) !important;
}

/* Secondary button styling */
.btn-secondary, .cta-btn.secondary {
    background: #8b5cf6 !important;
    background-image: none !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3) !important;
}

.btn-secondary:hover, .cta-btn.secondary:hover {
    background: #7c3aed !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4) !important;
}

/* Primary button styles - Blue */
.cta-btn.primary, .btn.btn-primary, .hero-cta .btn.btn-primary, .hero-section .btn.btn-primary {
    background: #3b82f6 !important;
    background-image: none !important;
    background-color: #3b82f6 !important;
    color: white !important;
}

/* Secondary button styles - Purple */
.cta-btn.secondary, .btn.btn-secondary, .hero-cta .btn.btn-secondary, .hero-section .btn.btn-secondary {
    background: #8b5cf6 !important;
    background-image: none !important;
    background-color: #8b5cf6 !important;
    color: white !important;
}

/* Hover effects */
.cta-btn.primary:hover, .btn.btn-primary:hover, .hero-cta .btn.btn-primary:hover, .hero-section .btn.btn-primary:hover {
    background: #2563eb !important;
    background-color: #2563eb !important;
    background-image: none !important;
    transform: scale(1.02) !important;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
}

.cta-btn.secondary:hover, .btn.btn-secondary:hover, .hero-cta .btn.btn-secondary:hover, .hero-section .btn.btn-secondary:hover {
    background: #7c3aed !important;
    background-color: #7c3aed !important;
    background-image: none !important;
    transform: scale(1.02) !important;
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4) !important;
}

/* Active/focus states */
.cta-btn:active, .btn:active {
    transform: scale(0.98) !important;
}

.cta-btn:focus, .btn:focus {
    outline: 2px solid rgba(79, 70, 229, 0.5) !important;
    outline-offset: 2px !important;
}

/* Form submit buttons */
.submit-btn {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    color: white !important;
    width: 100% !important;
    margin-top: 1rem !important;
}

.submit-btn:hover {
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3) !important;
}

/* Mobile responsiveness - Keep buttons on same line */
@media (max-width: 768px) {
    .cta-buttons, .hero-cta {
        flex-direction: row !important;
        gap: 0.5rem !important;
        flex-wrap: nowrap !important;
        justify-content: center !important;
    }

    .cta-btn, .btn {
        flex: 1 !important;
        max-width: 45% !important;
        font-size: 0.85rem !important;
        padding: 10px 16px !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
    }
}

@media (max-width: 480px) {
    .cta-btn, .btn {
        font-size: 0.8rem !important;
        padding: 8px 12px !important;
        max-width: 48% !important;
    }
}

/* Header center alignment */
.hero-content {
    text-align: center !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    justify-content: center !important;
}

.hero-content h1, .hero-title, h1 {
    text-align: center !important;
    margin: 0 auto 1rem auto !important;
    width: 100% !important;
    display: block !important;
}

.hero-content p, .hero-subtitle, .hero-description {
    text-align: center !important;
    margin: 0 auto 2rem auto !important;
    width: 100% !important;
    display: block !important;
}

/* Compact Form improvements */
.contact-form, .demo-form-container, .modal-content form, #demoForm, #loginForm {
    max-width: 400px !important;
    margin: 0 auto !important;
}

.form-group {
    margin-bottom: 0.75rem !important;
}

.form-group label {
    display: block !important;
    margin-bottom: 0.25rem !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    color: var(--color-text, #333) !important;
}

.form-group input, .form-group textarea {
    width: 100% !important;
    padding: 8px 12px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 4px !important;
    font-size: 0.875rem !important;
    transition: border-color 0.2s ease !important;
    box-sizing: border-box !important;
}

.form-group input:focus, .form-group textarea:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
}

.form-group textarea {
    resize: vertical !important;
    min-height: 60px !important;
}

.error-message {
    color: #ef4444 !important;
    font-size: 0.875rem !important;
    margin-top: 0.25rem !important;
    display: block !important;
}

/* Compact Modal improvements */
.demo-modal, .modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 1000 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

.demo-modal.active, .modal.active {
    opacity: 1 !important;
    visibility: visible !important;
}

.modal-content {
    background: white !important;
    border-radius: 8px !important;
    padding: 1.5rem !important;
    max-width: 400px !important;
    width: 90% !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
    position: relative !important;
}

.modal-content h2 {
    font-size: 1.25rem !important;
    margin-bottom: 1rem !important;
    text-align: center !important;
}

.close-modal {
    position: absolute !important;
    top: 1rem !important;
    right: 1rem !important;
    background: none !important;
    border: none !important;
    font-size: 1.5rem !important;
    cursor: pointer !important;
    color: #666 !important;
    transition: color 0.3s ease !important;
}

.close-modal:hover {
    color: #333 !important;
}

/* Login button fixes */
.login-btn, #nav-login-button {
    background: #3b82f6 !important;
    color: white !important;
    border: none !important;
    padding: 0.5rem 1rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: background-color 0.2s !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
}

.login-btn:hover, #nav-login-button:hover {
    background: #2563eb !important;
    color: white !important;
}

/* User menu button */
.user-btn {
    background: #3b82f6 !important;
    background-color: #3b82f6 !important;
    color: white !important;
    border: none !important;
    padding: 0.5rem 1rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: background-color 0.2s !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
    text-decoration: none !important;
}

.user-btn:hover {
    background: #2563eb !important;
    background-color: #2563eb !important;
    color: white !important;
}

.user-btn:focus {
    background: #2563eb !important;
    background-color: #2563eb !important;
    color: white !important;
    outline: none !important;
}

/* Dropdown chevron animation */
.user-btn i {
    transition: transform 0.2s ease !important;
}

/* User dropdown styling */
.user-dropdown {
    background: white !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
}

.user-dropdown a {
    color: #374151 !important;
    text-decoration: none !important;
    transition: background-color 0.2s !important;
}

.user-dropdown a:hover {
    background-color: #f9fafb !important;
    color: #374151 !important;
}

.user-dropdown a:last-child:hover {
    background-color: #fef2f2 !important;
    color: #dc2626 !important;
}

/* Fix all other CTA buttons throughout the site */
.enhanced-cta-btn, .enhanced-cta-btn-primary, .bubble-cta-btn, .cta-button, .service-cta-button {
    background: #3b82f6 !important;
    background-image: none !important;
    background-color: #3b82f6 !important;
    color: white !important;
    border: none !important;
    min-height: 50px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 12px 24px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    text-align: center !important;
    white-space: normal !important;
    line-height: 1.2 !important;
}

.enhanced-cta-btn-secondary, .bubble-cta-btn-secondary, .cta-button.secondary {
    background: #8b5cf6 !important;
    background-image: none !important;
    background-color: #8b5cf6 !important;
    color: white !important;
}

.enhanced-cta-btn:hover, .enhanced-cta-btn-primary:hover, .bubble-cta-btn:hover, .cta-button:hover, .service-cta-button:hover {
    background: #2563eb !important;
    background-color: #2563eb !important;
    background-image: none !important;
    transform: scale(1.02) !important;
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4) !important;
}

.enhanced-cta-btn-secondary:hover, .bubble-cta-btn-secondary:hover, .cta-button.secondary:hover {
    background: #7c3aed !important;
    background-color: #7c3aed !important;
    background-image: none !important;
    transform: scale(1.02) !important;
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4) !important;
}

/* Dark theme support */
.dark-theme .modal-content {
    background: #1f2937 !important;
    color: #f9fafb !important;
}

.dark-theme .form-group input,
.dark-theme .form-group textarea {
    background: #374151 !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
}

.dark-theme .form-group input:focus,
.dark-theme .form-group textarea:focus {
    border-color: #6366f1 !important;
}

.dark-theme .close-modal {
    color: #d1d5db !important;
}

.dark-theme .close-modal:hover {
    color: #f9fafb !important;
}
