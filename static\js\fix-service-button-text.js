/**
 * Fix Service Button Text
 * This script ensures service buttons display their full text without ellipsis
 * It directly sets the text content of specific buttons
 */

(function() {
    function fixServiceButtonText() {
        // Wait for DOM to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', applyFixes);
        } else {
            applyFixes();
        }
        
        // Apply fixes with delays to ensure they take effect
        setTimeout(applyFixes, 100);
        setTimeout(applyFixes, 500);
        setTimeout(applyFixes, 1000);
    }
    
    function applyFixes() {
        // Get all service CTA buttons
        const buttons = document.querySelectorAll('.service-cta-button');
        
        // Process each button
        buttons.forEach(function(button) {
            // Set styles to prevent text truncation
            button.style.whiteSpace = 'normal';
            button.style.overflow = 'visible';
            button.style.textOverflow = 'clip';
            
            // Get the button's text content
            const text = button.textContent.trim();
            
            // Check for specific buttons and set their text explicitly
            if (text.includes('Automate') || text.includes('Support') || text.includes('Tickets')) {
                button.textContent = 'Automate 85% of Support Tickets Now';
            } else if (text.includes('Transform') || text.includes('Customer') || text.includes('Experience')) {
                button.textContent = 'Transform Customer Experience in 14 Days';
            } else if (text.includes('Triple') || text.includes('Team') || text.includes('Efficiency')) {
                button.textContent = 'Triple Your Team\'s Efficiency Today';
            }
        });
    }
    
    // Run immediately
    fixServiceButtonText();
})();
