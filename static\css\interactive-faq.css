/**
 * Interactive FAQ Component
 * Modern, accessible accordion-style FAQ with search functionality
 */

/* FAQ Section Container */
.faq-section {
  padding: 5rem 1.5rem;
  background-color: var(--color-bg-secondary);
  position: relative;
  overflow: hidden;
}

.faq-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 10% 90%, rgba(99, 102, 241, 0.05) 0%, transparent 40%),
    radial-gradient(circle at 90% 10%, rgba(168, 85, 247, 0.05) 0%, transparent 40%);
  z-index: 0;
}

.dark-theme .faq-section::before {
  background-image:
    radial-gradient(circle at 10% 90%, rgba(99, 102, 241, 0.1) 0%, transparent 40%),
    radial-gradient(circle at 90% 10%, rgba(168, 85, 247, 0.1) 0%, transparent 40%);
}

.faq-container {
  max-width: 650px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* FAQ Search */
.faq-search-container {
  margin: 2rem 0;
}

.faq-search-wrapper {
  position: relative;
  max-width: 600px;
  margin: 0 auto;
}

.faq-search {
  width: 100%;
  padding: 1rem;
  padding-right: 6rem; /* Make room for the search button */
  border-radius: 0.5rem;
  border: 1px solid var(--color-border);
  background-color: var(--color-bg-primary);
  font-size: 1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
}

.faq-search:focus {
  outline: none;
  border-color: var(--color-primary-400);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-text-tertiary);
  font-size: 1rem;
  pointer-events: none; /* Ensure the icon doesn't interfere with input clicks */
  z-index: 1; /* Make sure the icon appears above the input */
}

.search-button {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--color-primary-600);
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  z-index: 2; /* Ensure button is clickable */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-button:hover {
  background-color: var(--color-primary-700);
}

.search-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.4);
}

.search-clear {
  position: absolute;
  right: 5.5rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-text-tertiary);
  cursor: pointer;
  font-size: 0.875rem;
  padding: 0.25rem;
  border-radius: 50%;
  display: none;
}

.search-clear:hover {
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
}

.faq-search:not(:placeholder-shown) + .search-clear {
  display: block;
}

.dark-theme .faq-search {
  background-color: var(--color-bg-secondary);
  border-color: var(--color-border);
}

.dark-theme .faq-search:focus {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.3);
}

/* FAQ Accordion */
.faq-accordion {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 2.5rem;
}

.faq-item {
  border-radius: 1.5rem; /* Much more rounded edges */
  background: rgba(255, 255, 255, 0.5); /* Subtle background */
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05); /* Very subtle border */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

.faq-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.dark-theme .faq-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.faq-question {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 1rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  color: var(--color-text-primary);
  transition: all 0.3s ease;
  position: relative;
}

.faq-question:hover {
  color: var(--color-primary-600);
  background-color: rgba(0, 0, 0, 0.02);
}

.dark-theme .faq-question:hover {
  color: var(--color-primary-400);
  background-color: rgba(255, 255, 255, 0.02);
}

.faq-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.5rem;
  margin-right: 0.75rem;
  background: none;
  color: var(--color-primary-600);
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.faq-question:hover .faq-icon {
  transform: scale(1.05);
}

.question-text {
  flex-grow: 1;
  padding-right: 2rem;
}

.faq-toggle {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border-radius: 50%;
  transition: all 0.3s ease;
  border: 1px solid var(--color-border);
  z-index: 2;
}

.faq-question:hover .faq-toggle {
  border-color: var(--color-primary-400);
}

.dark-theme .faq-toggle {
  border: 1px solid var(--color-border-medium);
}

.dark-theme .faq-question:hover .faq-toggle {
  border-color: var(--color-primary-500);
}

.faq-toggle i {
  transition: transform 0.3s ease;
  color: var(--color-text-secondary);
  font-size: 12px;
  display: block;
  line-height: 1;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.4s ease, padding 0.3s ease;
  background: none;
}

.faq-question[aria-expanded="true"] + .faq-answer {
  max-height: 800px;
}

.answer-content {
  padding: 0 1rem 1rem 3.5rem;
  color: var(--color-text-secondary);
  line-height: 1.6;
}

.answer-content p {
  margin-bottom: 1.25rem;
  font-size: 1rem;
}

.answer-content ul {
  margin: 1rem 0 1.5rem;
  padding-left: 1.75rem;
}

.answer-content li {
  margin-bottom: 0.75rem;
  padding-left: 0.5rem;
}

.answer-content strong {
  color: var(--color-primary-600);
  font-weight: 600;
}

.answer-content h4 {
  margin-bottom: 1rem;
  color: var(--color-text-primary);
  font-size: 1.2rem;
  font-weight: 600;
}

.answer-content h5 {
  margin: 1.5rem 0 0.75rem;
  color: var(--color-primary-600);
  font-size: 1.1rem;
  font-weight: 600;
}

.timeline-container {
  margin: 1.5rem 0;
  overflow-x: auto;
}

.implementation-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0.5rem 0 1.5rem;
  font-size: 0.95rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border-radius: 0.5rem;
  overflow: hidden;
}

.timeline-details {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.timeline-item {
  background-color: var(--color-bg-tertiary);
  border-radius: 0.5rem;
  padding: 1.25rem 1.5rem;
  border-left: 4px solid var(--color-primary-600);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.timeline-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.06);
}

.timeline-item h5 {
  margin: 0 0 0.75rem 0;
  color: var(--color-primary-600);
  font-size: 1.1rem;
  font-weight: 600;
}

.timeline-item p {
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.6;
  color: var(--color-text-secondary);
}

.dark-theme .timeline-item {
  background-color: var(--color-bg-secondary);
}

.implementation-table th,
.implementation-table td {
  padding: 1.25rem 1.5rem;
  border: 1px solid var(--color-border);
  text-align: left;
  vertical-align: middle;
}

.implementation-table th {
  background-color: var(--color-bg-secondary);
  font-weight: 600;
  color: var(--color-text-primary);
}

.implementation-table tr:nth-child(even) {
  background-color: var(--color-bg-tertiary);
}

.dark-theme .implementation-table th {
  background-color: var(--color-bg-tertiary);
}

.dark-theme .implementation-table tr:nth-child(even) {
  background-color: var(--color-bg-secondary);
}

/* Dark Theme Styles */
.dark-theme .faq-item {
  background-color: var(--color-bg-secondary);
  border-color: var(--color-border);
}

.dark-theme .faq-question:hover {
  color: var(--color-primary-400);
}

.dark-theme .faq-icon {
  background: linear-gradient(135deg, var(--color-primary-900), var(--color-primary-800));
  color: var(--color-primary-400);
}

/* Removed toggle icon styling */

.dark-theme .faq-question:hover .faq-icon {
  background: linear-gradient(135deg, var(--color-primary-800), var(--color-primary-700));
}

.dark-theme .answer-content strong {
  color: var(--color-primary-400);
}

/* FAQ CTA Section */
.faq-cta {
  text-align: center;
  margin-top: 3rem;
  padding: 2rem;
  background-color: var(--color-bg-primary);
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.025);
  border: 1px solid var(--color-border);
}

.faq-cta p {
  margin-bottom: 1.5rem;
  font-size: 1.125rem;
  color: var(--color-text-primary);
}

.faq-cta .cta-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  color: white;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.3);
}

.faq-cta .cta-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(99, 102, 241, 0.4);
}

.dark-theme .faq-cta {
  background-color: var(--color-bg-secondary);
  border-color: var(--color-border);
}

.dark-theme .faq-cta .cta-btn {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
}

.dark-theme .faq-cta .cta-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
}

/* No Results Message */
.no-results {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--color-text-secondary);
  display: none;
}

.no-results.visible {
  display: block;
}

.no-results-icon {
  font-size: 2.5rem;
  color: var(--color-primary-300);
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
  align-items: center;
}

.no-results h3 {
  margin-bottom: 0.75rem;
  color: var(--color-text-primary);
  font-size: 1.25rem;
  font-weight: 600;
}

.no-results p {
  margin-bottom: 1.5rem;
  color: var(--color-text-secondary);
}

.reset-button {
  padding: 0.75rem 1.5rem;
  background-color: var(--color-primary-600);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.875rem;
}

.reset-button:hover {
  background-color: var(--color-primary-700);
  transform: translateY(-1px);
}

.reset-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.4);
}

/* Highlight search matches */
.highlight {
  background-color: rgba(99, 102, 241, 0.2);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  color: var(--color-primary-700);
  font-weight: 500;
}

.dark-theme .highlight {
  background-color: rgba(99, 102, 241, 0.3);
  color: var(--color-primary-300);
}

/* Responsive Styles */
@media (max-width: 768px) {
  .faq-section {
    padding: 3rem 1rem;
  }

  .faq-container {
    max-width: 600px;
  }

  .faq-question {
    font-size: 0.95rem;
    padding: 0.9rem;
  }

  .faq-icon {
    width: 1.75rem;
    height: 1.75rem;
    margin-right: 0.75rem;
  }

  .faq-toggle {
    width: 20px;
    height: 20px;
    right: 0.9rem;
  }

  .answer-content {
    padding: 0 0.9rem 0.9rem 3.25rem;
  }

  .faq-cta {
    padding: 1.5rem;
  }

  .search-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }

  .search-clear {
    right: 4.5rem;
  }

  .faq-search {
    padding-right: 6rem;
  }
}

@media (max-width: 480px) {
  .faq-section {
    padding: 2rem 0.75rem;
  }

  .faq-container {
    max-width: 100%;
  }

  .faq-icon {
    width: 1.5rem;
    height: 1.5rem;
    margin-right: 0.5rem;
  }

  .faq-toggle {
    width: 18px;
    height: 18px;
    right: 0.75rem;
  }

  .faq-toggle i {
    font-size: 10px;
  }

  .answer-content {
    padding: 0 0.75rem 0.75rem 2.75rem;
  }

  .search-button {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
  }

  .search-clear {
    right: 4rem;
  }

  .faq-search {
    padding-right: 5.5rem;
    font-size: 0.875rem;
  }

  .reset-button {
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
  }
}
