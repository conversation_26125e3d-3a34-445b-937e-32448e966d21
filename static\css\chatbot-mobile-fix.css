/**
 * Chatbot Mobile Fix
 * Improves chatbot functionality and appearance on mobile devices
 */

/* Base chatbot improvements */
.chatbot-widget {
    /* Ensure proper z-index */
    z-index: 9999 !important;

    /* Improve transitions */
    transition: transform 0.3s ease, opacity 0.3s ease !important;

    /* Ensure proper overflow handling */
    overflow: hidden !important;

    /* Ensure proper positioning */
    position: fixed !important;
}

.chatbot-launcher {
    /* Ensure proper z-index */
    z-index: 9998 !important;

    /* Improve transitions */
    transition: transform 0.3s ease, opacity 0.3s ease !important;

    /* Ensure proper positioning */
    position: fixed !important;

    /* Ensure proper display */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Mobile-specific chatbot improvements */
@media (max-width: 768px) {
    /* Adjust chatbot launcher position and size */
    .chatbot-launcher {
        bottom: 35px !important;
        right: 15px !important;
        width: 55px !important;
        height: 55px !important;

        /* Ensure proper background */
        background-size: cover !important;
        background-position: center !important;

        /* Improve touch target */
        padding: 0 !important;
        margin: 0 !important;
    }

    /* Adjust chatbot widget size and position */
    .chatbot-widget {
        width: calc(100% - 30px) !important;
        height: 70vh !important;
        max-height: 500px !important;
        bottom: 35px !important;
        right: 15px !important;
        left: 15px !important;
        max-width: none !important;

        /* Ensure proper border radius */
        border-radius: 12px !important;

        /* Ensure proper box shadow */
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2) !important;
    }

    /* Ensure chatbot header is properly sized */
    .chatbot-header {
        height: 50px !important;
        padding: 0 15px !important;
    }

    /* Ensure chatbot title is properly sized */
    .chatbot-title {
        font-size: 16px !important;
    }

    /* Ensure chatbot controls are easily tappable */
    .chatbot-controls button {
        width: 40px !important;
        height: 40px !important;
        margin-left: 5px !important;
        font-size: 18px !important;
    }

    /* Ensure chatbot body is properly sized */
    .chatbot-body {
        height: calc(100% - 50px) !important;
    }

    /* Ensure chatbot messages container is properly sized */
    .chatbot-messages {
        padding: 10px !important;
        -webkit-overflow-scrolling: touch !important;
    }

    /* Ensure chatbot input is usable on mobile */
    .chatbot-input {
        height: 55px !important;
        padding: 10px !important;
    }

    .chatbot-input input {
        font-size: 16px !important; /* Prevent zoom on focus in iOS */
        padding: 10px 15px !important;
        height: 40px !important;
    }

    .chatbot-input button {
        width: 40px !important;
        height: 40px !important;
    }

    /* Ensure chatbot messages are readable */
    .message-content {
        padding: 10px 15px !important;
        max-width: 85% !important;
    }

    .message-content p {
        font-size: 15px !important;
        line-height: 1.5 !important;
    }

    /* Ensure chatbot avatar is properly sized */
    .message-avatar {
        width: 32px !important;
        height: 32px !important;
        min-width: 32px !important;
    }

    /* Ensure chatbot buttons are properly sized */
    .chatbot-button {
        padding: 8px 12px !important;
        font-size: 14px !important;
        margin: 5px !important;
    }
}

/* Tablet-specific chatbot improvements */
@media (min-width: 769px) and (max-width: 1024px) {
    /* Adjust chatbot widget size and position */
    .chatbot-widget {
        width: 350px !important;
        height: 500px !important;
        bottom: 20px !important;
        right: 20px !important;
    }
}

/* Landscape mode adjustments for mobile */
@media (max-width: 768px) and (orientation: landscape) {
    /* Adjust chatbot height in landscape */
    .chatbot-widget {
        height: 85vh !important;
        max-height: 350px !important;
    }
}

/* Fix for iOS fixed positioning issues */
@supports (-webkit-touch-callout: none) {
    .chatbot-launcher,
    .chatbot-widget {
        /* Add padding to account for iOS safe areas */
        padding-bottom: env(safe-area-inset-bottom, 0);
        padding-right: env(safe-area-inset-right, 0);
    }
}

/* Ensure proper dark mode styling */
.dark-theme .chatbot-widget {
    background-color: #1a202c !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.dark-theme .chatbot-header {
    background-color: #0f172a !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.dark-theme .chatbot-input {
    background-color: #1a202c !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.dark-theme .chatbot-input input {
    background-color: #2d3748 !important;
    color: #f7fafc !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Fix for chatbot launcher in dark mode */
.dark-theme .chatbot-launcher {
    background-image: url('../img/chatbot-launcher-dark.svg') !important;
    background-color: transparent !important;
}
