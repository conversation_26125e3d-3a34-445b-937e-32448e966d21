<!-- Navigation Structure -->
<div class="navbar-sticky">
    <nav>
        <!-- <PERSON><PERSON> moved inside nav-container -->
        <div class="menu-toggle">
            <span></span>
            <span></span>
            <span></span>
        </div>
        <div class="nav-container">
            <div class="logo-container">
                <a href="/" class="logo">Ziantrix</a>
            </div>
            <ul class="nav-menu">
                <li><a href="/#features" class="nav-item">Our Edge</a></li>
                <li class="services-dropdown">
                    <a href="javascript:void(0);" class="nav-item">Services <i class="fas fa-chevron-down"></i></a>
                    <div class="dropdown-menu">
                        <a href="/#chatbot-services">Chatbot Services</a>
                        <a href="/#ai-services">AI Services</a>
                        <a href="/#ai-agents">AI Agents</a>
                    </div>
                </li>
                <li><a href="/#solutions" class="nav-item">Industries</a></li>

                <li><a href="/#faq" class="nav-item">FAQ</a></li>
                <li><a href="/#about" class="nav-item">About</a></li>
            </ul>
            <div class="nav-right">
                <a href="/#contact" class="nav-item support-btn">Support</a>
                {% if session.get('logged_in') %}
                    <div class="user-menu">
                        <a href="javascript:void(0);" class="nav-item user-btn">{{ session.get('user_name', 'User') }} <i class="fas fa-chevron-down"></i></a>
                        <div class="user-dropdown">
                            <a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                            <a href="/profile"><i class="fas fa-user"></i> Profile</a>
                            <a href="/logout"><i class="fas fa-sign-out-alt"></i> Logout</a>
                        </div>
                    </div>
                {% else %}
                    <button onclick="openLoginModal()" class="login-btn" id="nav-login-button">Log In</button>
                {% endif %}
            </div>
        </div>
    </nav>
</div>

<!-- Register Modal -->
<div id="registerModal" class="modal">
    <div class="modal-content login-modal-content">
        <span class="close-modal" onclick="closeRegisterModal()">&times;</span>
        <h2>Register</h2>
        <form id="registerForm" action="/register" method="POST">
            <div class="form-group">
                <label for="register-name">Full Name</label>
                <input type="text" id="register-name" name="name" class="login-input" placeholder="Enter your full name" required>
            </div>
            <div class="form-group">
                <label for="register-email">Email Address</label>
                <input type="email" id="register-email" name="email" class="login-input" placeholder="Enter your email" required>
            </div>
            <div class="form-group">
                <label for="register-password">Password</label>
                <input type="password" id="register-password" name="password" class="login-input" placeholder="Create a password" required>
            </div>
            <div class="form-group">
                <label for="register-company">Company Name</label>
                <input type="text" id="register-company" name="company" class="login-input" placeholder="Enter your company name">
            </div>
            <button type="submit" class="submit-btn login-btn">Register</button>
            <div class="login-footer">
                <p>Already have an account? <a href="javascript:void(0);" onclick="openLoginModal(); closeRegisterModal();">Log In</a></p>
            </div>
        </form>
    </div>
</div>
