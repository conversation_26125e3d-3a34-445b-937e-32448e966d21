/**
 * Optimized <PERSON>roll Handler
 * High-performance scroll event handling with requestAnimationFrame throttling
 */

(function() {
    'use strict';

    // Performance configuration
    const CONFIG = {
        throttleDelay: 16, // ~60fps
        debounceDelay: 100,
        intersectionThreshold: 0.1,
        rootMargin: '50px'
    };

    // State management
    let isScrolling = false;
    let scrollTimeout = null;
    let rafId = null;
    let lastScrollY = 0;
    let scrollDirection = 'down';

    // Cache DOM elements
    const cachedElements = {
        navbar: null,
        sections: [],
        navLinks: [],
        scrollIndicator: null
    };

    // Initialize cached elements
    function initializeCache() {
        cachedElements.navbar = document.querySelector('.navbar');
        cachedElements.sections = Array.from(document.querySelectorAll('section[id]'));
        cachedElements.navLinks = Array.from(document.querySelectorAll('.navbar-link[data-target]'));
        cachedElements.scrollIndicator = document.querySelector('.scroll-indicator');
    }

    // Optimized scroll handler using requestAnimationFrame
    function handleScroll() {
        if (rafId) {
            cancelAnimationFrame(rafId);
        }

        rafId = requestAnimationFrame(() => {
            const currentScrollY = window.pageYOffset;
            scrollDirection = currentScrollY > lastScrollY ? 'down' : 'up';
            lastScrollY = currentScrollY;

            // Only update navigation if necessary
            updateNavigation();
            
            // Update scroll indicator if it exists
            updateScrollIndicator();

            rafId = null;
        });

        // Set scrolling state
        if (!isScrolling) {
            isScrolling = true;
            document.body.classList.add('scrolling');
        }

        // Clear existing timeout
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }

        // Set timeout to end scrolling state
        scrollTimeout = setTimeout(() => {
            isScrolling = false;
            document.body.classList.remove('scrolling');
        }, CONFIG.debounceDelay);
    }

    // Optimized navigation update
    function updateNavigation() {
        if (!cachedElements.sections.length || !cachedElements.navLinks.length) {
            return;
        }

        const scrollPosition = lastScrollY + 100; // Offset for header
        let activeSection = null;

        // Find active section efficiently
        for (let i = cachedElements.sections.length - 1; i >= 0; i--) {
            const section = cachedElements.sections[i];
            const sectionTop = section.offsetTop;
            
            if (scrollPosition >= sectionTop) {
                activeSection = section.id;
                break;
            }
        }

        // Update navigation highlighting
        if (activeSection) {
            updateNavHighlight(activeSection);
        }
    }

    // Efficient navigation highlighting
    function updateNavHighlight(activeSection) {
        cachedElements.navLinks.forEach(link => {
            const isActive = link.getAttribute('data-target') === activeSection;
            link.classList.toggle('active', isActive);
        });
    }

    // Update scroll indicator
    function updateScrollIndicator() {
        if (!cachedElements.scrollIndicator) return;

        const scrollPercent = (lastScrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
        cachedElements.scrollIndicator.style.transform = `scaleX(${scrollPercent / 100})`;
    }

    // Intersection Observer for lazy loading and animations
    function setupIntersectionObserver() {
        if (!('IntersectionObserver' in window)) {
            return; // Fallback for older browsers
        }

        const observerOptions = {
            threshold: CONFIG.intersectionThreshold,
            rootMargin: CONFIG.rootMargin
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    
                    // Add visible class for animations
                    element.classList.add('in-view');
                    
                    // Lazy load images
                    lazyLoadImages(element);
                    
                    // Start counters if needed
                    startCounters(element);
                    
                    // Unobserve after first intersection
                    observer.unobserve(element);
                }
            });
        }, observerOptions);

        // Observe sections and cards
        const elementsToObserve = document.querySelectorAll(
            'section, .feature-card, .service-card, .solution-card, .testimonial-card'
        );
        
        elementsToObserve.forEach(el => observer.observe(el));
    }

    // Optimized lazy loading
    function lazyLoadImages(container) {
        const images = container.querySelectorAll('img[data-src]');
        images.forEach(img => {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
        });
    }

    // Optimized counter animation
    function startCounters(container) {
        const counters = container.querySelectorAll('.stat-number[data-count]');
        counters.forEach(counter => {
            const target = parseInt(counter.dataset.count);
            const duration = 1000; // 1 second
            const startTime = performance.now();
            
            function updateCounter(currentTime) {
                const elapsed = currentTime - startTime;
                const progress = Math.min(elapsed / duration, 1);
                const current = Math.floor(progress * target);
                
                counter.textContent = current + '%';
                
                if (progress < 1) {
                    requestAnimationFrame(updateCounter);
                }
            }
            
            requestAnimationFrame(updateCounter);
        });
    }

    // Passive event listeners for better performance
    function setupEventListeners() {
        window.addEventListener('scroll', handleScroll, { passive: true });
        
        // Handle resize efficiently
        let resizeTimeout;
        window.addEventListener('resize', () => {
            if (resizeTimeout) {
                clearTimeout(resizeTimeout);
            }
            resizeTimeout = setTimeout(() => {
                initializeCache(); // Recache elements on resize
            }, 250);
        }, { passive: true });
    }

    // Optimize page load performance
    function optimizePageLoad() {
        // Preload critical resources
        const criticalImages = document.querySelectorAll('img[data-critical]');
        criticalImages.forEach(img => {
            if (img.dataset.src) {
                img.src = img.dataset.src;
                img.removeAttribute('data-src');
            }
        });

        // Add performance classes
        document.body.classList.add('performance-optimized');
    }

    // Initialize when DOM is ready
    function initialize() {
        initializeCache();
        setupEventListeners();
        setupIntersectionObserver();
        optimizePageLoad();
        
        // Initial navigation update
        updateNavigation();
    }

    // Start when DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // Cleanup function for SPA navigation
    window.optimizedScrollCleanup = function() {
        if (rafId) {
            cancelAnimationFrame(rafId);
        }
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }
        window.removeEventListener('scroll', handleScroll);
    };

    // Export for debugging
    window.scrollPerformance = {
        getScrollDirection: () => scrollDirection,
        getScrollPosition: () => lastScrollY,
        isScrolling: () => isScrolling,
        forceUpdate: updateNavigation
    };

})();
