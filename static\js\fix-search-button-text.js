/**
 * Fix Search Button Text
 * This script ensures the search button text is correct (no duplicates)
 */

(function() {
    function fixSearchButtonText() {
        const searchButton = document.getElementById('searchButton');
        if (searchButton) {
            // Fix duplicate text
            if (searchButton.textContent.includes('SearchSearch')) {
                searchButton.textContent = 'Search';
            }
            
            // Make sure text is visible
            if (searchButton.textContent.trim() === '') {
                searchButton.textContent = 'Search';
            }
            
            // Remove any ::after pseudo-element that might be adding text
            const style = document.createElement('style');
            style.textContent = `
                .search-button::after {
                    content: none !important;
                    display: none !important;
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    // Run immediately
    fixSearchButtonText();
    
    // Also run when DOM is loaded
    document.addEventListener('DOMContentLoaded', fixSearchButtonText);
    
    // And run after a delay to catch any late changes
    setTimeout(fixSearchButtonText, 100);
    setTimeout(fixSearchButtonText, 500);
    setTimeout(fixSearchButtonText, 1000);
})();
