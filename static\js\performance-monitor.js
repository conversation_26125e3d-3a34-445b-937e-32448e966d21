/**
 * Performance Monitor
 * Real-time performance monitoring and optimization
 */

(function() {
    'use strict';

    // Performance configuration
    const CONFIG = {
        targetFPS: 60,
        frameTimeThreshold: 16.67, // 60fps = 16.67ms per frame
        longTaskThreshold: 50, // Long task threshold in ms
        enableLogging: window.location.hostname === 'localhost',
        enableOptimizations: true
    };

    // Performance metrics
    const metrics = {
        fps: 0,
        frameTime: 0,
        longTasks: 0,
        scrollPerformance: [],
        renderTime: 0,
        memoryUsage: 0
    };

    // Frame rate monitoring
    let frameCount = 0;
    let lastTime = performance.now();
    let lastFrameTime = performance.now();

    function measureFPS() {
        const now = performance.now();
        const delta = now - lastTime;
        
        frameCount++;
        
        if (delta >= 1000) { // Update every second
            metrics.fps = Math.round((frameCount * 1000) / delta);
            frameCount = 0;
            lastTime = now;
            
            if (CONFIG.enableLogging) {
                console.log(`FPS: ${metrics.fps}`);
            }
        }
        
        // Measure frame time
        metrics.frameTime = now - lastFrameTime;
        lastFrameTime = now;
        
        // Detect performance issues
        if (metrics.frameTime > CONFIG.frameTimeThreshold * 2) {
            handlePerformanceIssue('high-frame-time', metrics.frameTime);
        }
        
        requestAnimationFrame(measureFPS);
    }

    // Long task monitoring
    function setupLongTaskObserver() {
        if ('PerformanceObserver' in window) {
            try {
                const observer = new PerformanceObserver((list) => {
                    list.getEntries().forEach(entry => {
                        if (entry.duration > CONFIG.longTaskThreshold) {
                            metrics.longTasks++;
                            handlePerformanceIssue('long-task', entry.duration);
                        }
                    });
                });
                
                observer.observe({ entryTypes: ['longtask'] });
            } catch (e) {
                console.warn('Long task observer not supported');
            }
        }
    }

    // Memory monitoring
    function monitorMemory() {
        if ('memory' in performance) {
            metrics.memoryUsage = performance.memory.usedJSHeapSize / 1048576; // MB
            
            if (CONFIG.enableLogging) {
                console.log(`Memory usage: ${metrics.memoryUsage.toFixed(2)} MB`);
            }
        }
        
        setTimeout(monitorMemory, 5000); // Check every 5 seconds
    }

    // Scroll performance monitoring
    let scrollStartTime = 0;
    let scrollFrames = 0;

    function monitorScrollPerformance() {
        let isScrolling = false;
        let scrollTimeout;
        
        function startScrollMonitoring() {
            if (!isScrolling) {
                isScrolling = true;
                scrollStartTime = performance.now();
                scrollFrames = 0;
            }
        }
        
        function endScrollMonitoring() {
            if (isScrolling) {
                isScrolling = false;
                const scrollDuration = performance.now() - scrollStartTime;
                const avgFrameTime = scrollDuration / scrollFrames;
                
                metrics.scrollPerformance.push({
                    duration: scrollDuration,
                    frames: scrollFrames,
                    avgFrameTime: avgFrameTime
                });
                
                // Keep only last 10 scroll sessions
                if (metrics.scrollPerformance.length > 10) {
                    metrics.scrollPerformance.shift();
                }
                
                if (CONFIG.enableLogging) {
                    console.log(`Scroll performance: ${avgFrameTime.toFixed(2)}ms avg frame time`);
                }
            }
        }
        
        function onScroll() {
            startScrollMonitoring();
            scrollFrames++;
            
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(endScrollMonitoring, 150);
        }
        
        window.addEventListener('scroll', onScroll, { passive: true });
    }

    // Performance issue handler
    function handlePerformanceIssue(type, value) {
        if (!CONFIG.enableOptimizations) return;
        
        switch (type) {
            case 'high-frame-time':
                optimizeForFrameRate();
                break;
            case 'long-task':
                optimizeForLongTasks();
                break;
            case 'memory-pressure':
                optimizeMemoryUsage();
                break;
        }
        
        if (CONFIG.enableLogging) {
            console.warn(`Performance issue detected: ${type} (${value})`);
        }
    }

    // Frame rate optimization
    function optimizeForFrameRate() {
        // Disable expensive animations
        document.body.classList.add('performance-mode');
        
        // Reduce animation complexity
        const expensiveElements = document.querySelectorAll('.expensive-animation');
        expensiveElements.forEach(el => {
            el.style.animationPlayState = 'paused';
        });
        
        // Simplify hover effects
        const hoverElements = document.querySelectorAll('.service-card, .feature-card, .solution-card');
        hoverElements.forEach(el => {
            el.classList.add('simplified-hover');
        });
    }

    // Long task optimization
    function optimizeForLongTasks() {
        // Break up heavy operations
        if (window.heavyOperations) {
            window.heavyOperations.forEach(op => {
                if (typeof op.pause === 'function') {
                    op.pause();
                }
            });
        }
        
        // Defer non-critical scripts
        const nonCriticalScripts = document.querySelectorAll('script[data-defer]');
        nonCriticalScripts.forEach(script => {
            script.remove();
        });
    }

    // Memory optimization
    function optimizeMemoryUsage() {
        // Clear cached data
        if (window.imageCache) {
            window.imageCache.clear();
        }
        
        // Remove unused event listeners
        if (window.unusedListeners) {
            window.unusedListeners.forEach(listener => {
                listener.remove();
            });
        }
        
        // Trigger garbage collection if available
        if (window.gc) {
            window.gc();
        }
    }

    // Resource timing monitoring
    function monitorResourceTiming() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    if (entry.duration > 1000) { // Resources taking more than 1s
                        console.warn(`Slow resource: ${entry.name} (${entry.duration}ms)`);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['resource'] });
        }
    }

    // Paint timing monitoring
    function monitorPaintTiming() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    if (entry.name === 'first-contentful-paint') {
                        console.log(`First Contentful Paint: ${entry.startTime}ms`);
                    }
                    if (entry.name === 'largest-contentful-paint') {
                        console.log(`Largest Contentful Paint: ${entry.startTime}ms`);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['paint', 'largest-contentful-paint'] });
        }
    }

    // Layout shift monitoring
    function monitorLayoutShift() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                let totalShift = 0;
                list.getEntries().forEach(entry => {
                    totalShift += entry.value;
                });
                
                if (totalShift > 0.1) { // CLS threshold
                    console.warn(`High Cumulative Layout Shift: ${totalShift}`);
                }
            });
            
            observer.observe({ entryTypes: ['layout-shift'] });
        }
    }

    // Performance dashboard
    function createPerformanceDashboard() {
        if (!CONFIG.enableLogging) return;
        
        const dashboard = document.createElement('div');
        dashboard.id = 'performance-dashboard';
        dashboard.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 10000;
            min-width: 200px;
        `;
        
        document.body.appendChild(dashboard);
        
        function updateDashboard() {
            dashboard.innerHTML = `
                <div>FPS: ${metrics.fps}</div>
                <div>Frame Time: ${metrics.frameTime.toFixed(2)}ms</div>
                <div>Long Tasks: ${metrics.longTasks}</div>
                <div>Memory: ${metrics.memoryUsage.toFixed(2)}MB</div>
            `;
        }
        
        setInterval(updateDashboard, 1000);
    }

    // Initialize performance monitoring
    function initialize() {
        // Start FPS monitoring
        requestAnimationFrame(measureFPS);
        
        // Setup observers
        setupLongTaskObserver();
        monitorResourceTiming();
        monitorPaintTiming();
        monitorLayoutShift();
        
        // Start memory monitoring
        setTimeout(monitorMemory, 1000);
        
        // Monitor scroll performance
        monitorScrollPerformance();
        
        // Create dashboard in development
        if (CONFIG.enableLogging) {
            createPerformanceDashboard();
        }
        
        console.log('Performance monitoring initialized');
    }

    // Auto-initialize
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // Export metrics for external access
    window.performanceMetrics = {
        get: () => ({ ...metrics }),
        config: CONFIG,
        optimize: {
            frameRate: optimizeForFrameRate,
            longTasks: optimizeForLongTasks,
            memory: optimizeMemoryUsage
        }
    };

})();
