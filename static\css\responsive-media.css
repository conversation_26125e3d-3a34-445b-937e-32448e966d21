/**
 * Responsive Media CSS
 * Ensures all images, videos, and media scale properly across devices
 */

/* ===== BASE RESPONSIVE MEDIA RULES ===== */

/* Make all images responsive by default */
img {
    max-width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
}

/* Make all videos responsive by default */
video {
    max-width: 100%;
    height: auto;
    display: block;
    object-fit: cover;
}

/* Make all iframes responsive by default */
iframe {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Make all embedded content responsive */
embed, object {
    max-width: 100%;
    height: auto;
    display: block;
}

/* ===== RESPONSIVE IMAGE UTILITIES ===== */

.responsive-img {
    width: 100%;
    height: auto;
    object-fit: cover;
    display: block;
}

.responsive-img-contain {
    width: 100%;
    height: auto;
    object-fit: contain;
    display: block;
}

.responsive-img-fill {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* ===== RESPONSIVE VIDEO CONTAINERS ===== */

.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    overflow: hidden;
    border-radius: 8px;
}

.video-container video,
.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Alternative aspect ratios */
.video-container-4-3 {
    padding-bottom: 75%; /* 4:3 aspect ratio */
}

.video-container-1-1 {
    padding-bottom: 100%; /* 1:1 aspect ratio */
}

.video-container-21-9 {
    padding-bottom: 42.86%; /* 21:9 aspect ratio */
}

/* ===== HERO VIDEO RESPONSIVE STYLES ===== */

.hero-video {
    position: relative;
    width: 100%;
    max-width: 800px;
    margin: 3rem auto 0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.hero-video video {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 12px;
}

.video-play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 2;
}

.video-play-button:hover {
    background: rgba(255, 255, 255, 1);
    transform: translate(-50%, -50%) scale(1.1);
}

.video-play-button i {
    font-size: 1.5rem;
    color: var(--color-primary, #4f46e5);
    margin-left: 3px; /* Optical alignment for play icon */
}

/* ===== LOGO RESPONSIVE STYLES ===== */

.navbar-logo img {
    height: auto;
    width: auto;
    max-height: 40px;
    max-width: 200px;
    object-fit: contain;
}

.footer-logo img {
    height: auto;
    width: auto;
    max-height: 60px;
    max-width: 150px;
    object-fit: contain;
}

/* ===== ICON RESPONSIVE STYLES ===== */

.feature-icon,
.service-icon,
.solution-icon {
    width: auto;
    height: auto;
    font-size: clamp(2rem, 5vw, 3rem);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.contact-icon {
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

/* ===== BACKGROUND IMAGE RESPONSIVE STYLES ===== */

.hero-bg,
.section-bg {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: scroll; /* Better for mobile performance */
}

/* ===== RESPONSIVE MEDIA QUERIES ===== */

/* Mobile devices (up to 767px) */
@media (max-width: 767px) {
    .hero-video {
        margin: 2rem auto 0;
        border-radius: 8px;
        max-width: 100%;
    }
    
    .video-play-button {
        width: 50px;
        height: 50px;
    }
    
    .video-play-button i {
        font-size: 1.25rem;
    }
    
    .navbar-logo img {
        max-height: 32px;
        max-width: 150px;
    }
    
    .feature-icon,
    .service-icon,
    .solution-icon {
        font-size: 2rem;
    }
    
    .contact-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    /* Optimize background images for mobile */
    .hero-bg,
    .section-bg {
        background-attachment: scroll;
        background-size: cover;
    }
}

/* Tablets (768px to 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .hero-video {
        max-width: 600px;
        margin: 2.5rem auto 0;
    }
    
    .navbar-logo img {
        max-height: 36px;
        max-width: 180px;
    }
    
    .feature-icon,
    .service-icon,
    .solution-icon {
        font-size: 2.5rem;
    }
}

/* Desktop (1024px and up) */
@media (min-width: 1024px) {
    .hero-video {
        max-width: 800px;
        margin: 3rem auto 0;
    }
    
    .navbar-logo img {
        max-height: 40px;
        max-width: 200px;
    }
    
    .feature-icon,
    .service-icon,
    .solution-icon {
        font-size: 3rem;
    }
    
    /* Enable fixed background on desktop for better visual effect */
    .hero-bg,
    .section-bg {
        background-attachment: fixed;
    }
}

/* ===== HIGH DPI DISPLAYS ===== */

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .navbar-logo img,
    .footer-logo img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* ===== PRINT STYLES ===== */

@media print {
    .hero-video,
    .video-container,
    .video-play-button {
        display: none;
    }
    
    .hero-bg,
    .section-bg {
        background: none !important;
    }
    
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid;
    }
}

/* ===== ACCESSIBILITY IMPROVEMENTS ===== */

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    .video-play-button {
        transition: none;
    }
    
    .video-play-button:hover {
        transform: translate(-50%, -50%);
    }
}

/* ===== LAZY LOADING SUPPORT ===== */

img[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s ease;
}

img[loading="lazy"].loaded {
    opacity: 1;
}

/* ===== WEBP SUPPORT ===== */

.webp .hero-bg {
    background-image: url('data:image/webp;base64,...'); /* Fallback for WebP */
}

/* ===== RESPONSIVE IFRAME EMBEDS ===== */

.embed-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%;
    overflow: hidden;
    border-radius: 8px;
}

.embed-container iframe,
.embed-container object,
.embed-container embed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
}

/* ===== RESPONSIVE GALLERY STYLES ===== */

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    width: 100%;
}

.image-gallery img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.image-gallery img:hover {
    transform: scale(1.05);
}

@media (max-width: 767px) {
    .image-gallery {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 0.5rem;
    }
    
    .image-gallery img {
        height: 150px;
    }
}
