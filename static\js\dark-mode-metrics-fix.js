/**
 * Direct JavaScript fix for feature card metrics in dark mode
 */
document.addEventListener('DOMContentLoaded', function() {
    // Function to apply styles based on theme
    function applyMetricStyles() {
        const isDarkMode = document.body.classList.contains('dark-theme');
        const metricWrappers = document.querySelectorAll('.feature-card .metric-wrapper');
        const featureMetrics = document.querySelectorAll('.feature-card .feature-metric');
        const metricLabels = document.querySelectorAll('.feature-card .feature-metric-label');
        
        if (isDarkMode) {
            // Dark mode styles
            metricWrappers.forEach(wrapper => {
                wrapper.style.backgroundColor = '#1e1e1e';
                wrapper.style.background = '#1e1e1e';
                wrapper.style.boxShadow = '0 0 0 4px #1e1e1e';
                wrapper.style.opacity = '1';
                wrapper.style.position = 'relative';
                wrapper.style.zIndex = '10';
            });
            
            featureMetrics.forEach(metric => {
                metric.style.backgroundColor = '#1e1e1e';
                metric.style.background = '#1e1e1e';
                metric.style.color = '#64b5f6';
                metric.style.opacity = '1';
                metric.style.textShadow = '0 0 10px rgba(100, 181, 246, 0.4)';
            });
            
            metricLabels.forEach(label => {
                label.style.backgroundColor = '#1e1e1e';
                label.style.background = '#1e1e1e';
                label.style.color = '#aaa';
                label.style.opacity = '1';
            });
        } else {
            // Light mode styles
            metricWrappers.forEach(wrapper => {
                wrapper.style.backgroundColor = '#ffffff';
                wrapper.style.background = '#ffffff';
                wrapper.style.boxShadow = '0 0 0 4px #ffffff';
                wrapper.style.opacity = '1';
                wrapper.style.position = 'relative';
                wrapper.style.zIndex = '10';
            });
            
            featureMetrics.forEach(metric => {
                metric.style.backgroundColor = '#ffffff';
                metric.style.background = '#ffffff';
                metric.style.color = '#1976d2';
                metric.style.opacity = '1';
                metric.style.textShadow = 'none';
            });
            
            metricLabels.forEach(label => {
                label.style.backgroundColor = '#ffffff';
                label.style.background = '#ffffff';
                label.style.color = '#666';
                label.style.opacity = '1';
            });
        }
    }
    
    // Apply styles on page load
    applyMetricStyles();
    
    // Apply styles when theme changes
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('change', function() {
            // Small delay to ensure the theme class has been updated
            setTimeout(applyMetricStyles, 50);
        });
    }
    
    // Create a MutationObserver to watch for class changes on the body
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.attributeName === 'class') {
                applyMetricStyles();
            }
        });
    });
    
    // Start observing the body for class changes
    observer.observe(document.body, { attributes: true });
});
