/**
 * Service Card Width Fix
 * This CSS specifically targets the service cards to ensure they all have the same width
 */

/* Force all service cards to have the same width */
.services-grid {
  display: grid !important;
  grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  gap: 30px !important;
  width: 100% !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* Ensure all service cards have the same width and height */
.service-card {
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
  height: 520px !important;
  min-height: 520px !important;
  max-height: 520px !important;
  display: flex !important;
  flex-direction: column !important;
  position: relative !important;
  padding-bottom: 120px !important;
}

/* Ensure all service card footers have the same width */
.service-card-footer {
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
  padding: 0 24px 24px !important;
  position: absolute !important;
  bottom: 30px !important;
  left: 0 !important;
  right: 0 !important;
}

/* Ensure all service CTA buttons have the same width */
.service-cta-button {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 14px 20px !important;
  text-align: center !important;
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

/* Target the third service card specifically */
.services-grid .service-card:nth-child(3) {
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
  height: 520px !important;
  min-height: 520px !important;
  max-height: 520px !important;
  flex: 1 1 0 !important; /* This ensures equal width in flex/grid layouts */
}

/* Target the third service card button specifically */
.services-grid .service-card:nth-child(3) .service-cta-button {
  width: 100% !important;
  min-width: 0 !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
  padding: 14px 20px !important;
  text-align: center !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .services-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }

  .service-card,
  .services-grid .service-card:nth-child(3) {
    width: 100% !important;
    flex: 1 1 0 !important;
  }
}

@media (max-width: 992px) {
  .services-grid {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .service-card,
  .services-grid .service-card:nth-child(3) {
    width: 100% !important;
    flex: 1 1 0 !important;
  }
}

@media (max-width: 768px) {
  .services-grid {
    grid-template-columns: minmax(0, 1fr) !important;
  }

  .service-card,
  .services-grid .service-card:nth-child(3) {
    width: 100% !important;
    flex: 1 1 0 !important;
  }
}
