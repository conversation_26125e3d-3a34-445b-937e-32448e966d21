/**
 * Modal Enhancements and Form Submission Handler
 * - Handles AJAX form submissions
 * - Clears localStorage after successful submission
 * - Enhanced modal functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Demo Form AJAX Submission
    const demoForm = document.getElementById('demoForm');
    if (demoForm) {
        demoForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading state
            const submitButton = demoForm.querySelector('button[type="submit"]');
            const originalButtonText = submitButton.innerHTML;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';
            submitButton.disabled = true;

            // Get form data
            const formData = new FormData(demoForm);

            // Send AJAX request
            fetch('/submit_demo', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Show success message
                    showFormMessage(demoForm, data.message, 'success');

                    // Clear form data from localStorage
                    clearDemoFormData();

                    // Reset form
                    demoForm.reset();

                    // Close modal after delay
                    setTimeout(() => {
                        closeDemoModal();
                    }, 3000);
                } else {
                    // Show error message
                    showFormMessage(demoForm, data.message || 'An error occurred. Please try again.', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showFormMessage(demoForm, 'An error occurred while submitting the form. Please try again later or contact support.', 'error');
            })
            .finally(() => {
                // Restore button state
                submitButton.innerHTML = originalButtonText;
                submitButton.disabled = false;
            });
        });
    }

    // Login Form AJAX Submission
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            // We'll use regular form submission for login
            // But we'll clear localStorage on successful login
            // This will be handled by checking for success flash messages after page load

            // Save the email for potential restoration if login fails
            const email = document.getElementById('login-email').value;
            sessionStorage.setItem('lastLoginAttempt', email);
        });
    }

    // Register Form AJAX Submission
    const registerForm = document.getElementById('registerForm');
    if (registerForm) {
        registerForm.addEventListener('submit', function(e) {
            // We'll use regular form submission for registration
            // But we'll clear localStorage on successful registration
            // This will be handled by checking for success flash messages after page load

            // Save the email and name for potential restoration if registration fails
            const email = document.getElementById('register-email').value;
            const name = document.getElementById('register-name').value;
            sessionStorage.setItem('lastRegisterAttempt', JSON.stringify({
                email: email,
                name: name
            }));
        });
    }

    // Check for flash messages indicating successful login/registration
    const flashMessages = document.querySelectorAll('.flash-message');
    flashMessages.forEach(message => {
        if (message.classList.contains('success')) {
            const messageText = message.textContent.toLowerCase();

            if (messageText.includes('logged in')) {
                clearLoginFormData();
                sessionStorage.removeItem('lastLoginAttempt');
            } else if (messageText.includes('account') && messageText.includes('created')) {
                clearRegisterFormData();
                sessionStorage.removeItem('lastRegisterAttempt');
            }
        }
    });

    // Restore last login/register attempt if available
    const lastLoginAttempt = sessionStorage.getItem('lastLoginAttempt');
    if (lastLoginAttempt && document.getElementById('login-email')) {
        document.getElementById('login-email').value = lastLoginAttempt;
    }

    const lastRegisterAttempt = sessionStorage.getItem('lastRegisterAttempt');
    if (lastRegisterAttempt) {
        try {
            const data = JSON.parse(lastRegisterAttempt);
            if (document.getElementById('register-email')) {
                document.getElementById('register-email').value = data.email || '';
            }
            if (document.getElementById('register-name')) {
                document.getElementById('register-name').value = data.name || '';
            }
        } catch (e) {
            console.error('Error parsing last register attempt:', e);
        }
    }
});

/**
 * Show a message in the form
 * @param {HTMLElement} form - The form element
 * @param {string} message - The message to display
 * @param {string} type - The message type ('success' or 'error')
 */
function showFormMessage(form, message, type) {
    // Check if message container already exists
    let messageContainer = form.querySelector('.form-message');

    if (!messageContainer) {
        // Create message container
        messageContainer = document.createElement('div');
        messageContainer.className = 'form-message';
        form.appendChild(messageContainer);
    }

    // Set message content and style
    messageContainer.textContent = message;
    messageContainer.className = `form-message ${type}`;

    // Scroll to message
    messageContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}

// Add CSS for form messages
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        .form-message {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
            font-weight: 500;
            animation: fadeIn 0.3s ease-in-out;
        }

        .form-message.success {
            background-color: rgba(76, 175, 80, 0.1);
            color: #4CAF50;
            border: 1px solid rgba(76, 175, 80, 0.3);
        }

        .form-message.error {
            background-color: rgba(244, 67, 54, 0.1);
            color: #F44336;
            border: 1px solid rgba(244, 67, 54, 0.3);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    `;
    document.head.appendChild(style);
});
