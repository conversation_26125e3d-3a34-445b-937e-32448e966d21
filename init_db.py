#!/usr/bin/env python3
"""
Database initialization script for Ziantrix Dynamic App
"""
import os
import sys
import logging
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, FormSubmission, ChatConversation, ChatMessage, Analytics
from database import DatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def init_database():
    """Initialize the database with tables and sample data"""
    logger.info("🚀 Starting database initialization...")

    with app.app_context():
        try:
            # Test database connection
            logger.info("Testing database connection...")
            db.session.execute(db.text('SELECT 1'))
            logger.info("✅ Database connection successful")

            # Create all tables
            logger.info("Creating database tables...")
            db.create_all()
            logger.info("✅ Database tables created successfully")

            # Check if we need to migrate existing JSON data
            data_dir = Path("data")
            users_file = data_dir / "users.json"
            profiles_file = data_dir / "user_profiles.json"

            if users_file.exists() or profiles_file.exists():
                logger.info("Found existing JSON data files. Starting migration...")
                migrate_json_data(users_file, profiles_file)

            # Note: User authentication has been removed from this app
            # No admin user creation needed

            logger.info("🎉 Database initialization completed successfully!")
            return True

        except Exception as e:
            logger.error(f"❌ Database initialization failed: {str(e)}")
            return False

def migrate_json_data(users_file, profiles_file):
    """Note: JSON data migration removed - authentication disabled"""
    logger.info("JSON data migration skipped - authentication has been removed from this app")

    # Backup original files if they exist
    if users_file.exists():
        backup_file = users_file.with_suffix('.json.backup')
        users_file.rename(backup_file)
        logger.info(f"📦 Backed up users.json to {backup_file}")

    if profiles_file.exists():
        backup_file = profiles_file.with_suffix('.json.backup')
        profiles_file.rename(backup_file)
        logger.info(f"📦 Backed up user_profiles.json to {backup_file}")

def create_sample_admin():
    """Note: Admin user creation removed - authentication disabled"""
    logger.info("Admin user creation skipped - authentication has been removed from this app")

def reset_database():
    """Reset the database (drop and recreate all tables)"""
    logger.warning("⚠️ Resetting database - this will delete all data!")

    with app.app_context():
        try:
            # Drop all tables
            db.drop_all()
            logger.info("🗑️ Dropped all database tables")

            # Recreate tables
            db.create_all()
            logger.info("✅ Recreated database tables")

            # Note: No admin user creation needed - authentication disabled

            logger.info("🎉 Database reset completed successfully!")
            return True

        except Exception as e:
            logger.error(f"❌ Database reset failed: {str(e)}")
            return False

def check_database_status():
    """Check database connection and table status"""
    logger.info("🔍 Checking database status...")

    with app.app_context():
        try:
            # Test connection
            db.session.execute(db.text('SELECT 1'))
            logger.info("✅ Database connection: OK")

            # Check tables
            from sqlalchemy import inspect
            inspector = inspect(db.engine)
            tables = inspector.get_table_names()
            expected_tables = ['form_submissions', 'chat_conversations', 'chat_messages', 'analytics']

            logger.info(f"📊 Found tables: {tables}")

            missing_tables = [table for table in expected_tables if table not in tables]
            if missing_tables:
                logger.warning(f"⚠️ Missing tables: {missing_tables}")
            else:
                logger.info("✅ All expected tables found")

            # Check record counts
            form_count = FormSubmission.query.count()
            chat_count = ChatConversation.query.count()

            logger.info(f"📈 Record counts:")
            logger.info(f"   Form submissions: {form_count}")
            logger.info(f"   Chat conversations: {chat_count}")

            return True

        except Exception as e:
            logger.error(f"❌ Database status check failed: {str(e)}")
            return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Database management for Ziantrix Dynamic App")
    parser.add_argument("--init", action="store_true", help="Initialize database")
    parser.add_argument("--reset", action="store_true", help="Reset database (WARNING: deletes all data)")
    parser.add_argument("--status", action="store_true", help="Check database status")
    parser.add_argument("--migrate", action="store_true", help="Migrate JSON data only")

    args = parser.parse_args()

    if args.reset:
        confirm = input("⚠️ This will delete ALL data. Type 'yes' to confirm: ")
        if confirm.lower() == 'yes':
            reset_database()
        else:
            logger.info("Database reset cancelled")
    elif args.init:
        init_database()
    elif args.status:
        check_database_status()
    elif args.migrate:
        with app.app_context():
            data_dir = Path("data")
            users_file = data_dir / "users.json"
            profiles_file = data_dir / "user_profiles.json"
            migrate_json_data(users_file, profiles_file)
    else:
        parser.print_help()
