/* Simple Services Dropdown Styling */

/* Dropdown container */
.navbar-dropdown {
    position: relative;
    height: 100%;
    margin: 0 8px;
}

/* Dropdown toggle */
.navbar-dropdown .navbar-link.dropdown-toggle {
    padding: 4px 8px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

/* Dropdown menu */
.navbar-dropdown .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background-color: #ffffff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    z-index: 1000;
    padding: 5px 0;
}

/* Show dropdown on hover */
.navbar-dropdown:hover .dropdown-menu {
    display: block;
}

/* Dropdown items */
.navbar-dropdown .dropdown-item {
    display: block;
    padding: 10px 15px;
    color: #333333;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.2s ease;
}

/* Dropdown item hover */
.navbar-dropdown .dropdown-item:hover {
    background-color: rgba(25, 118, 210, 0.1);
    color: #1976d2;
    font-weight: 600;
}

/* Active dropdown toggle */
.navbar-dropdown .navbar-link.dropdown-toggle.active,
.navbar-dropdown .navbar-link.dropdown-toggle:hover {
    color: #1976d2;
    border-bottom: 2px solid #1976d2;
}

/* Dark theme styles */
.dark-theme .navbar-dropdown .dropdown-menu {
    background-color: #1e293b;
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .navbar-dropdown .dropdown-item {
    color: #e2e8f0;
}

.dark-theme .navbar-dropdown .dropdown-item:hover {
    background-color: rgba(100, 181, 246, 0.1);
    color: #64b5f6;
}

.dark-theme .navbar-dropdown .navbar-link.dropdown-toggle.active,
.dark-theme .navbar-dropdown .navbar-link.dropdown-toggle:hover {
    color: #64b5f6;
    border-bottom-color: #64b5f6;
}
