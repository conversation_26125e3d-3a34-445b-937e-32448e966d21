/**
 * Feature Card Alignment
 * This file ensures proper alignment of all headers and content in feature cards
 * without any highlighting or background colors
 */

/* Feature card base styles */
.feature-card {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  text-align: center !important;
  padding: 2rem !important;
  border: 1px solid var(--color-border) !important;
  border-radius: 12px !important;
  background: transparent !important;
  height: 100% !important;
  min-height: 320px !important;
  max-width: 360px !important;
  margin: 0 auto !important;
  position: relative !important;
  overflow: hidden !important;
  contain: layout style paint !important;
  will-change: transform !important;
  transform: translateZ(0) !important;
}

/* Feature card icon */
.feature-card .feature-icon {
  width: 48px !important;
  height: 48px !important;
  min-height: 48px !important;
  border-radius: 8px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 auto 1.5rem auto !important;
  color: var(--color-primary) !important;
  background-color: rgba(var(--color-primary-rgb), 0.1) !important;
  contain: layout style paint !important;
}

/* Feature card title */
.feature-card .feature-title {
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  letter-spacing: 0.05em !important;
  text-transform: uppercase !important;
  margin-bottom: 0.75rem !important;
  color: var(--color-text) !important;
  text-align: center !important;
  background: transparent !important;
  min-height: 1.5rem !important;
  contain: layout style !important;
}

/* Feature card description */
.feature-card .feature-description {
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  color: var(--color-text-light) !important;
  margin-bottom: 1rem !important;
  text-align: center !important;
  max-width: 90% !important;
  background: transparent !important;
  min-height: 3rem !important;
  contain: layout style !important;
}

/* Feature card metric */
.feature-card .feature-metric {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  color: var(--color-primary) !important;
  margin: 1rem 0 0.25rem !important;
  text-align: center !important;
  background: transparent !important;
  display: block !important;
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  min-height: 3rem !important;
  contain: layout style !important;
}

/* Feature card metric label */
.feature-card .feature-metric-label {
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  letter-spacing: 0.05em !important;
  text-transform: uppercase !important;
  color: var(--color-text-light) !important;
  margin-bottom: 0.5rem !important;
  text-align: center !important;
  background: transparent !important;
  display: block !important;
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  min-height: 1.5rem !important;
  contain: layout style !important;
}

/* Feature card proof point */
.feature-card .feature-proof-point {
  font-size: 0.75rem !important;
  font-style: italic !important;
  color: var(--color-text-light) !important;
  text-align: center !important;
  margin-bottom: 1.5rem !important;
  background: transparent !important;
  display: block !important;
  padding: 0 !important;
  border: none !important;
  box-shadow: none !important;
  min-height: 1.5rem !important;
  contain: layout style !important;
}

/* Feature card link */
.feature-card .feature-link {
  display: inline-flex !important;
  align-items: center !important;
  color: var(--color-primary) !important;
  font-weight: 500 !important;
  font-size: 0.875rem !important;
  text-decoration: none !important;
  margin-top: auto !important;
  background: transparent !important;
  min-height: 2rem !important;
  contain: layout style !important;
}

.feature-card .feature-link i {
  margin-left: 0.5rem !important;
  width: 1rem !important;
  height: 1rem !important;
  contain: layout style !important;
}

/* Dark mode adjustments */
.dark-theme .feature-card {
  border-color: var(--color-border-dark) !important;
}

.dark-theme .feature-card .feature-title {
  color: var(--color-text-dark) !important;
}

.dark-theme .feature-card .feature-description {
  color: var(--color-text-light-dark) !important;
}

.dark-theme .feature-card .feature-metric {
  color: var(--color-primary-light) !important;
}

.dark-theme .feature-card .feature-metric-label,
.dark-theme .feature-card .feature-proof-point {
  color: var(--color-text-light-dark) !important;
}

.dark-theme .feature-card .feature-link {
  color: var(--color-primary-light) !important;
}

/* Remove all backgrounds and borders from feature card elements */
.feature-card *,
.feature-card *::before,
.feature-card *::after,
.feature-card .feature-metric,
.feature-card .feature-metric-label,
.feature-card .feature-proof-point,
.feature-card [class*="metric"],
.feature-card *[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Ensure consistent spacing between elements */
.feature-card > * {
  margin-bottom: 0.75rem !important;
}

.feature-card > *:last-child {
  margin-bottom: 0 !important;
}

/* Ensure consistent width for all feature cards */
.features-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
  gap: 2rem !important;
}

/* Ensure all feature cards have the same height */
.features-grid .feature-card {
  height: 100% !important;
}

/* Loading state */
.feature-card.loading {
  opacity: 0.7 !important;
  pointer-events: none !important;
}

.feature-card.loading::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) !important;
  background-size: 200% 100% !important;
  animation: shimmer 1.5s infinite !important;
  z-index: 1 !important;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}
