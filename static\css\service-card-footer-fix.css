/**
 * Service Card Footer Fix
 * This CSS ensures the service card footer has a background that hides any text behind it
 */

/* Add a background to the service card footer */
.service-card-footer {
  position: absolute !important;
  bottom: 24px !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  box-sizing: border-box !important;
  padding: 0 24px 24px !important;
  margin-top: auto !important;
  border-top: none !important;
  background: transparent !important;
  z-index: 10 !important;
}

/* Add a background to the service card footer */
.service-card-footer::before {
  content: "" !important;
  position: absolute !important;
  top: -20px !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: var(--color-card-bg) !important;
  z-index: -1 !important;
}

/* Dark theme adjustments */
.dark-theme .service-card-footer::before {
  background-color: var(--color-card-bg-dark) !important;
}

/* Ensure the service CTA button is above the background */
.service-cta-button {
  position: relative !important;
  z-index: 20 !important;
}
