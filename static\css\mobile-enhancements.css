/**
 * Mobile-Specific Enhancements
 * Touch-friendly interactions and mobile optimizations
 */

/* ===== TOUCH-FRIENDLY INTERFACE ===== */

/* Ensure all interactive elements meet minimum touch target size */
button,
.btn,
.cta-btn,
a,
input[type="button"],
input[type="submit"],
input[type="reset"],
.navbar-link,
.dropdown-item,
.chatbot-launcher,
.close-btn,
.minimize-btn {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem;
    touch-action: manipulation;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
}

/* Remove default touch highlights and add custom ones */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Allow text selection for content */
p, h1, h2, h3, h4, h5, h6, span, div.content, .text-content {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* Allow text selection for form inputs */
input, textarea, select {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
}

/* ===== MOBILE NAVIGATION ENHANCEMENTS ===== */

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    justify-content: space-around;
    width: 30px;
    height: 30px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--color-text-primary, #333);
    border-radius: 2px;
    transition: all 0.3s ease;
    transform-origin: center;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* ===== MOBILE FORM ENHANCEMENTS ===== */

/* Larger form inputs for mobile */
@media (max-width: 768px) {
    input, textarea, select {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 1rem;
        border-radius: 8px;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
    
    label {
        font-size: 1rem;
        margin-bottom: 0.5rem;
        display: block;
        font-weight: 500;
    }
}

/* ===== MOBILE BUTTON ENHANCEMENTS ===== */

@media (max-width: 768px) {
    .btn, .cta-btn {
        width: 100%;
        padding: 1rem 1.5rem;
        font-size: 1rem;
        font-weight: 600;
        border-radius: 8px;
        margin-bottom: 1rem;
    }
    
    .hero-cta {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
        max-width: 320px;
        margin: 0 auto;
    }
    
    .hero-cta .btn {
        width: 100%;
        margin-bottom: 0;
    }
}

/* ===== MOBILE CARD LAYOUTS ===== */

@media (max-width: 768px) {
    .feature-card,
    .service-card,
    .solution-card {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .feature-icon,
    .service-icon,
    .solution-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }
    
    .feature-title,
    .service-title,
    .solution-title {
        font-size: 1.25rem;
        margin-bottom: 0.75rem;
    }
}

/* ===== MOBILE CHATBOT ENHANCEMENTS ===== */

@media (max-width: 768px) {
    .chatbot-launcher {
        width: 60px !important;
        height: 60px !important;
        bottom: 20px !important;
        right: 20px !important;
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        font-size: 1.5rem;
    }
    
    .chatbot-widget {
        width: calc(100vw - 20px) !important;
        height: 70vh !important;
        bottom: 10px !important;
        left: 10px !important;
        right: 10px !important;
        border-radius: 12px 12px 0 0;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    }
    
    .chatbot-header {
        padding: 1rem;
        border-radius: 12px 12px 0 0;
    }
    
    .chatbot-controls button {
        width: 44px !important;
        height: 44px !important;
        font-size: 1.2rem;
    }
    
    .chatbot-input input {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.75rem;
    }
    
    .send-btn {
        width: 44px !important;
        height: 44px !important;
    }
}

/* ===== MOBILE MODAL ENHANCEMENTS ===== */

@media (max-width: 768px) {
    .modal {
        padding: 1rem;
    }
    
    .modal-content {
        width: 100%;
        max-width: none;
        margin: 0;
        border-radius: 12px;
        padding: 1.5rem;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .close-modal {
        width: 44px;
        height: 44px;
        font-size: 1.5rem;
        top: 1rem;
        right: 1rem;
    }
}

/* ===== MOBILE FOOTER ENHANCEMENTS ===== */

@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
        padding: 2rem 1rem;
    }
    
    .footer-section {
        width: 100%;
        margin-bottom: 1.5rem;
    }
    
    .footer-contact-icons {
        justify-content: center !important;
        gap: 2rem !important;
    }
    
    .footer-contact-icon {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .footer-contact-icon i {
        font-size: 1.5rem !important;
    }
}

/* ===== MOBILE TYPOGRAPHY ADJUSTMENTS ===== */

@media (max-width: 768px) {
    .hero-title {
        font-size: clamp(1.8rem, 6vw, 2.5rem);
        line-height: 1.2;
        margin-bottom: 1rem;
    }
    
    .hero-subtitle {
        font-size: clamp(1rem, 4vw, 1.2rem);
        line-height: 1.5;
        margin-bottom: 2rem;
    }
    
    .section-title {
        font-size: clamp(1.5rem, 5vw, 2rem);
        margin-bottom: 1rem;
    }
    
    .section-description {
        font-size: clamp(0.9rem, 3vw, 1rem);
        margin-bottom: 2rem;
    }
}

/* ===== MOBILE SPACING OPTIMIZATIONS ===== */

@media (max-width: 768px) {
    section {
        padding: 3rem 1rem;
    }
    
    .container {
        padding: 0 1rem;
    }
    
    .section-header {
        margin-bottom: 2rem;
    }
    
    .services-grid,
    .features-grid,
    .solutions-grid {
        gap: 1.5rem;
        margin-top: 2rem;
    }
}

/* ===== MOBILE PERFORMANCE OPTIMIZATIONS ===== */

@media (max-width: 768px) {
    /* Reduce animations on mobile for better performance */
    .feature-card:hover,
    .service-card:hover,
    .solution-card:hover {
        transform: none;
    }
    
    /* Simplify transitions on mobile */
    * {
        transition-duration: 0.2s;
    }
    
    /* Optimize scroll behavior */
    body {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }
}

/* ===== MOBILE ACCESSIBILITY ENHANCEMENTS ===== */

@media (max-width: 768px) {
    /* Ensure focus indicators are visible */
    button:focus,
    .btn:focus,
    input:focus,
    textarea:focus,
    select:focus,
    a:focus {
        outline: 3px solid var(--color-primary, #4f46e5);
        outline-offset: 2px;
    }
    
    /* Improve contrast for mobile */
    .navbar-link {
        color: var(--color-text-primary, #333);
        font-weight: 500;
    }
    
    .navbar-link:hover,
    .navbar-link:focus {
        color: var(--color-primary, #4f46e5);
        background-color: rgba(79, 70, 229, 0.1);
        border-radius: 4px;
    }
}
