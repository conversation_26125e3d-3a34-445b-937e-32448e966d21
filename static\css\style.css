:root {
    /* Light theme colors */
    --color-primary: #1976d2;
    --color-primary-dark: #0d47a1;
    --color-primary-light: #64b5f6;
    --color-secondary: #ff5722;
    --color-secondary-dark: #e64a19;
    --color-secondary-light: #ffab91;
    --color-accent: #7c4dff; /* New accent color - violet */
    --color-accent-dark: #5e35b1;
    --color-accent-light: #b39ddb;
    --color-text: #333333;
    --color-text-light: #757575;
    --color-background: #ffffff;
    --color-background-alt: #f5f7fa;
    --color-border: #e0e0e0;
    --color-success: #4caf50;
    --color-error: #f44336;
    --color-warning: #ff9800;
    --color-info: #2196f3;

    /* Spacing - 8px grid system */
    --spacing-2: 0.125rem; /* 2px */
    --spacing-4: 0.25rem;  /* 4px */
    --spacing-8: 0.5rem;   /* 8px */
    --spacing-12: 0.75rem; /* 12px */
    --spacing-16: 1rem;    /* 16px */
    --spacing-24: 1.5rem;  /* 24px */
    --spacing-32: 2rem;    /* 32px */
    --spacing-40: 2.5rem;  /* 40px */
    --spacing-48: 3rem;    /* 48px */
    --spacing-64: 4rem;    /* 64px */

    /* Legacy spacing (for backward compatibility) */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem; /* Reduced from 4rem to 3rem (25% tighter) */

    /* Typography */
    --font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
    --font-size-xs: 0.75rem;   /* 12px */
    --font-size-sm: 0.875rem;  /* 14px */
    --font-size-md: 0.9375rem; /* 15px - reduced from 16px */
    --font-size-lg: 1.125rem;  /* 18px */
    --font-size-xl: 1.375rem;  /* 22px */
    --font-size-2xl: 1.75rem;  /* 28px */
    --font-size-3xl: 2.5rem;   /* 40px */

    /* Line heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;

    /* Border radius */
    --border-radius-sm: 4px;
    --border-radius-md: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    --border-radius-full: 9999px;

    /* Shadows */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05), 0 1px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 2px 10px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.04), 0 4px 6px rgba(0, 0, 0, 0.08);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.05), 0 10px 10px rgba(0, 0, 0, 0.04);
    --shadow-accent: 0 4px 12px rgba(124, 77, 255, 0.2);
    --shadow-primary: 0 4px 12px rgba(25, 118, 210, 0.2);

    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.25s ease;
    --transition-slow: 0.4s ease;

    /* Z-index layers */
    --z-below: -1;
    --z-normal: 1;
    --z-above: 10;
    --z-modal: 100;
    --z-overlay: 1000;
    --z-highest: 9999;

    /* Container widths - 12 column grid */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;

    /* Grid */
    --grid-columns: 12;
    --grid-gutter: var(--spacing-16);
}



/* Dark mode specific enhancements */
.dark-theme body {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(100, 181, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(100, 181, 246, 0.15) 0%, transparent 50%),
        linear-gradient(to bottom right, rgba(100, 181, 246, 0.1) 0%, transparent 70%),
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='400' viewBox='0 0 800 800'%3E%3Cg fill='none' stroke='%2364b5f6' stroke-opacity='0.12' stroke-width='1'%3E%3Cpath d='M769 229L1037 260.9M927 880L731 737 520 660 309 538 40 599 295 764 126.5 879.5 40 599-197 493 102 382-31 229 126.5 79.5-69-63'/%3E%3Cpath d='M-31 229L237 261 390 382 603 493 308.5 537.5 101.5 381.5M370 905L295 764'/%3E%3Cpath d='M520 660L578 842 731 737 840 599 603 493 520 660 295 764 309 538 390 382 539 269 769 229 577.5 41.5 370 105 295 -36 126.5 79.5 237 261 102 382 40 599 -69 737 127 880'/%3E%3Cpath d='M520-140L578.5 42.5 731-63M603 493L539 269 237 261 370 105M902 382L539 269M390 382L102 382'/%3E%3Cpath d='M-222 42L126.5 79.5 370 105 539 269 577.5 41.5 927 80 769 229 902 382 603 493 731 737M295-36L577.5 41.5M578 842L295 764M40-201L127 80M102 382L-261 269'/%3E%3C/g%3E%3Cg fill='%2390caf9' fill-opacity='0.15'%3E%3Ccircle cx='769' cy='229' r='8'/%3E%3Ccircle cx='539' cy='269' r='8'/%3E%3Ccircle cx='603' cy='493' r='8'/%3E%3Ccircle cx='731' cy='737' r='8'/%3E%3Ccircle cx='520' cy='660' r='8'/%3E%3Ccircle cx='309' cy='538' r='8'/%3E%3Ccircle cx='295' cy='764' r='8'/%3E%3Ccircle cx='40' cy='599' r='8'/%3E%3Ccircle cx='102' cy='382' r='8'/%3E%3Ccircle cx='127' cy='80' r='8'/%3E%3Ccircle cx='370' cy='105' r='8'/%3E%3Ccircle cx='578' cy='42' r='8'/%3E%3Ccircle cx='237' cy='261' r='8'/%3E%3Ccircle cx='390' cy='382' r='8'/%3E%3C/g%3E%3C/svg%3E");
    background-attachment: fixed;
    background-size: cover;
    position: relative;
}

.dark-theme body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    background:
        radial-gradient(circle at 10% 10%, rgba(25, 118, 210, 0.1) 0%, transparent 30%),
        radial-gradient(circle at 90% 90%, rgba(25, 118, 210, 0.1) 0%, transparent 30%);
    z-index: -1;
    animation: pulseBackground 10s ease-in-out infinite alternate;
}

@keyframes pulseBackground {
    0% { opacity: 0.7; }
    100% { opacity: 1; }
}

.dark-theme .hero-content h1 {
    background: linear-gradient(90deg, var(--color-primary) 0%, #90caf9 50%, var(--color-primary) 100%);
    background-size: 200% auto;
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    animation: gradientText 5s ease infinite;
    text-shadow: 0 0 20px rgba(100, 181, 246, 0.3);
}

.dark-theme .hero-subtitle {
    color: rgba(224, 224, 224, 0.9);
}

.dark-theme .navbar-sticky {
    background-color: rgba(18, 18, 18, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    border-bottom: 1px solid rgba(100, 181, 246, 0.1);
}

.dark-theme .navbar-sticky.scrolled {
    background-color: rgba(18, 18, 18, 0.95);
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.6);
    border-bottom: 1px solid rgba(100, 181, 246, 0.2);
}

.dark-theme .cta-btn.primary {
    background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    border: none;
    color: #ffffff;
}

.dark-theme .cta-btn.primary:hover {
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
    background: linear-gradient(135deg, #2196f3 0%, #1565c0 100%);
}

.dark-theme .cta-btn.secondary {
    border: 2px solid rgba(100, 181, 246, 0.7);
    color: rgba(255, 255, 255, 0.95);
    background-color: transparent;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.dark-theme .cta-btn.secondary:hover {
    background-color: rgba(100, 181, 246, 0.1);
    border-color: rgba(100, 181, 246, 0.9);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transform: translateY(-2px);
}

/* Feature cards in dark mode */
.dark-theme .features-section {
    background-color: rgba(30, 30, 30, 0.7);
    position: relative;
}

.dark-theme .features-section::before {
    opacity: 0.3;
}

.dark-theme .features-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, transparent 50%, rgba(25, 118, 210, 0.05) 100%);
    z-index: 0;
    pointer-events: none;
}

.dark-theme .feature-card {
    background-color: var(--color-background-dark);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(100, 181, 246, 0.15);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.dark-theme .feature-card::before {
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
    height: 3px;
    box-shadow: 0 0 15px rgba(100, 181, 246, 0.7);
}

.dark-theme .feature-card::after {
    background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
}

.dark-theme .feature-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5), 0 0 25px rgba(100, 181, 246, 0.3);
    border-color: rgba(100, 181, 246, 0.4);
    transform: translateY(-10px);
}

.dark-theme .feature-icon {
    color: var(--color-primary-light);
    filter: drop-shadow(0 0 8px rgba(100, 181, 246, 0.5));
    opacity: 0.9;
    transition: all 0.3s ease;
}

.dark-theme .feature-card:hover .feature-icon {
    color: #ffffff;
    filter: drop-shadow(0 0 12px rgba(100, 181, 246, 0.8));
    opacity: 1;
    transform: scale(1.05);
}

.dark-theme .feature-card:hover h3 {
    color: var(--color-primary-light);
    text-shadow: 0 0 5px rgba(100, 181, 246, 0.3);
}

/* About section in dark mode */
.dark-theme .about-section {
    background-color: rgba(18, 18, 18, 0.7);
    position: relative;
}

.dark-theme .about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 30%, rgba(100, 181, 246, 0.1) 0%, transparent 30%),
        radial-gradient(circle at 70% 70%, rgba(100, 181, 246, 0.1) 0%, transparent 30%);
    z-index: 0;
    pointer-events: none;
}

.dark-theme .about-image img {
    filter: drop-shadow(0 0 15px rgba(100, 181, 246, 0.3));
}

.dark-theme .stat-number {
    color: var(--color-primary-light);
    text-shadow: 0 0 10px rgba(100, 181, 246, 0.4);
}

.dark-theme .section-header h2 {
    color: var(--color-primary-light);
    text-shadow: 0 0 15px rgba(100, 181, 246, 0.4);
    background: linear-gradient(90deg, var(--color-primary-light) 0%, #90caf9 50%, var(--color-primary-light) 100%);
    background-size: 200% auto;
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    animation: gradientText 5s ease infinite;
}

.dark-theme .section-header h2::after {
    background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
    box-shadow: 0 0 10px rgba(100, 181, 246, 0.5);
}

/* Base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px; /* Accounts for fixed header */
}

/* Section spacing and consistency */
section {
    padding: 60px 0;
    margin-bottom: 0;
    position: relative;
    scroll-margin-top: 80px;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-md);
    line-height: 1.6;
    color: var(--color-text);
    background-color: var(--color-background);
    transition: background-color var(--transition-slow), color var(--transition-slow);
    background-image:
        radial-gradient(circle at 15% 15%, rgba(25, 118, 210, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 85% 85%, rgba(100, 181, 246, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(33, 150, 243, 0.03) 0%, transparent 60%),
        linear-gradient(120deg, rgba(25, 118, 210, 0.03) 0%, rgba(66, 165, 245, 0.03) 50%, rgba(21, 101, 192, 0.03) 100%),
        url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%231976d2' fill-opacity='0.03' fill-rule='evenodd'/%3E%3C/svg%3E");
    background-attachment: fixed;
    background-size: cover;
    padding-top: 42px; /* Add padding for fixed navbar */
    position: relative;
    overflow-x: hidden;
    min-height: 100vh;
    width: 100%;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.03) 0%, rgba(33, 150, 243, 0.03) 50%, rgba(13, 71, 161, 0.03) 100%);
    z-index: -1;
    animation: gradientShift 15s ease-in-out infinite alternate;
    pointer-events: none;
}

@keyframes gradientShift {
    0% { background-position: 0% 0%; }
    100% { background-position: 100% 100%; }
}

.dark-theme {
    background-image:
        radial-gradient(circle at 15% 15%, rgba(100, 181, 246, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 85% 85%, rgba(144, 202, 249, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 50% 50%, rgba(66, 165, 245, 0.05) 0%, transparent 60%),
        linear-gradient(120deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 50%, rgba(21, 101, 192, 0.05) 100%),
        url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%2390caf9' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-primary-dark);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

/* Responsive media elements */
video, iframe, embed, object {
    max-width: 100%;
    height: auto;
}

/* Responsive images with proper scaling */
.responsive-img {
    width: 100%;
    height: auto;
    object-fit: cover;
}

/* Responsive video container */
.video-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    overflow: hidden;
}

.video-container video,
.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

ul, ol {
    list-style-position: inside;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: var(--spacing-16);
    line-height: var(--line-height-tight);
    letter-spacing: -0.01em;
}

h1 {
    font-size: 2.5rem; /* 40px */
    font-weight: 700;
    letter-spacing: -0.02em;
}

h2 {
    font-size: 1.875rem; /* 30px */
    font-weight: 600;
}

h3 {
    font-size: 1.5rem; /* 24px */
    font-weight: 600;
}

h4 {
    font-size: 1.25rem; /* 20px */
    font-weight: 600;
}

h5 {
    font-size: 1.125rem; /* 18px */
    font-weight: 600;
}

h6 {
    font-size: 1rem; /* 16px */
    font-weight: 600;
}

p {
    margin-bottom: var(--spacing-16);
    font-size: var(--font-size-md);
    line-height: var(--line-height-normal);
}

.text-sm {
    font-size: var(--font-size-sm);
}

.text-lg {
    font-size: var(--font-size-lg);
}

.font-light {
    font-weight: 300;
}

.font-normal {
    font-weight: 400;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

/* Theme Toggle */
.theme-toggle-wrapper {
    position: fixed;
    top: var(--spacing-md);
    right: var(--spacing-md);
    display: flex;
    align-items: center;
    cursor: pointer;
    z-index: 1010; /* Higher than navbar */
    padding: 5px;
    border-radius: var(--border-radius-full);
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all var(--transition-normal);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-toggle-wrapper:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.dark-theme .theme-toggle-wrapper {
    background-color: rgba(30, 40, 50, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.dark-theme .theme-toggle-wrapper:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

#theme-toggle {
    display: none;
}

.slider {
    width: 42.5px;
    height: 20.4px;
    border-radius: var(--border-radius-full);
    position: relative;
    background-color: #e0e0e0;
    transition: all var(--transition-normal);
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

#theme-toggle:checked + .slider {
    background-color: #1e2834;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

/* Sun icon - on right side in light mode */
.slider:after {
    content: '';
    position: absolute;
    top: 3.4px;
    right: 3.4px;
    left: auto;
    width: 13.6px;
    height: 13.6px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='13.6' height='13.6'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M12 18a6 6 0 1 1 0-12 6 6 0 0 1 0 12zm0-2a4 4 0 1 0 0-8 4 4 0 0 0 0 8zM11 1h2v3h-2V1zm0 19h2v3h-2v-3zM3.515 4.929l1.414-1.414L7.05 5.636 5.636 7.05 3.515 4.93zM16.95 18.364l1.414-1.414 2.121 2.121-1.414 1.414-2.121-2.121zm2.121-14.85l1.414 1.415-2.121 2.121-1.414-1.414 2.121-2.121zM5.636 16.95l1.414 1.414-2.121 2.121-1.414-1.414 2.121-2.121zM23 11v2h-3v-2h3zM4 11v2H1v-2h3z' fill='%23FFC107'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    opacity: 1;
    transition: opacity var(--transition-normal), transform var(--transition-normal);
    z-index: 0;
}

/* Moon icon - on left side in dark mode */
#theme-toggle:checked + .slider:after {
    content: '';
    position: absolute;
    top: 3.4px;
    left: 3.4px;
    right: auto;
    width: 13.6px;
    height: 13.6px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='13.6' height='13.6'%3E%3Cpath fill='none' d='M0 0h24v24H0z'/%3E%3Cpath d='M10 7a7 7 0 0 0 12 4.9v.1c0 5.523-4.477 10-10 10S2 17.523 2 12 6.477 2 12 2h.1A6.979 6.979 0 0 0 10 7zm-6 5a8 8 0 0 0 15.062 3.762A9 9 0 0 1 8.238 4.938 7.999 7.999 0 0 0 4 12z' fill='%23FFFFFF'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    opacity: 1;
    z-index: 0;
}

.slider:before {
    content: "";
    position: absolute;
    top: 1.7px;
    left: 1.7px;
    width: 17px;
    height: 17px;
    border-radius: 50%;
    background-color: white;
    transition: transform var(--transition-normal), background-color var(--transition-normal), box-shadow var(--transition-normal), border var(--transition-normal);
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
    z-index: 2; /* Ensure it's above the icons */
}

/* This is a duplicate and has been removed */

#theme-toggle:checked + .slider {
    background-color: #1e2834;
}

#theme-toggle:checked + .slider:before {
    transform: translateX(22.1px);
    background-color: white;
}

/* Light mode (sun) */
.slider:before {
    background-color: #ffffff;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
    border: none;
    border-radius: 50%;
}

/* Dark mode (moon) */
#theme-toggle:checked + .slider:before {
    background-color: #f4f4f4;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
    border: none;
    border-radius: 50%;
}

/* Moon effect using a pseudo-element - removed duplicate */

/* Add subtle animation */
@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
    50% { box-shadow: 0 0 10px rgba(255, 193, 7, 0.8); }
    100% { box-shadow: 0 0 5px rgba(255, 193, 7, 0.5); }
}

.slider:hover:before {
    animation: glow 1.5s infinite;
}

@keyframes moonGlow {
    0% { box-shadow: inset -3px -2px 0px 0px rgba(0, 0, 0, 0.2), 0 0 5px rgba(255, 255, 255, 0.5); }
    50% { box-shadow: inset -3px -2px 0px 0px rgba(0, 0, 0, 0.2), 0 0 10px rgba(255, 255, 255, 0.8); }
    100% { box-shadow: inset -3px -2px 0px 0px rgba(0, 0, 0, 0.2), 0 0 5px rgba(255, 255, 255, 0.5); }
}

#theme-toggle:checked + .slider:hover:before {
    animation: moonGlow 1.5s infinite;
}

/* Flash Messages */
.flash-messages {
    position: fixed;
    top: calc(70px + var(--spacing-lg)); /* Account for navbar height + spacing */
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 500px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.flash-message {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-md);
    transition: opacity var(--transition-normal);
}

.flash-message.success {
    background-color: var(--color-success);
    color: white;
}

.flash-message.error {
    background-color: var(--color-error);
    color: white;
}

.flash-message.info {
    background-color: var(--color-info);
    color: white;
}

.flash-message.warning {
    background-color: var(--color-warning);
    color: white;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: var(--font-size-xl);
    cursor: pointer;
    line-height: 1;
}

/* Header & Navigation */
header {
    background-color: var(--color-background);
    position: relative;
    z-index: 5;
    padding-top: 70px; /* Space for fixed navbar */
}

/* Sticky Navigation */
.navbar-sticky {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    box-shadow: 0 4px 20px rgba(25, 118, 210, 0.15);
    z-index: 1000;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(25, 118, 210, 0.1);
}

.navbar-sticky.scrolled {
    box-shadow: 0 4px 25px rgba(25, 118, 210, 0.2);
    background-color: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid rgba(25, 118, 210, 0.15);
}

.dark-theme .navbar-sticky {
    background-color: rgba(18, 18, 18, 0.85);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(100, 181, 246, 0.15);
}

.dark-theme .navbar-sticky.scrolled {
    background-color: rgba(18, 18, 18, 0.95);
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.4);
    border-bottom: 1px solid rgba(100, 181, 246, 0.2);
}

.navbar-sticky nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: var(--container-xl);
    margin: 0 auto;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    margin-left: 2rem;
    position: relative;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: absolute;
    right: 0;
}

/* User Menu Styles */
.user-menu {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: var(--color-background);
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-md);
    min-width: 150px;
    z-index: 1000;
    display: none;
    border: 1px solid var(--color-border);
    padding: 0.5rem 0;
}

.dark-theme .user-dropdown {
    background-color: var(--color-background-dark-alt);
    border: 1px solid var(--color-border-dark);
}

.user-dropdown a {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--color-text);
    text-decoration: none;
    transition: background-color 0.2s ease;
}

.user-dropdown a:hover {
    background-color: var(--color-background-alt);
}

.dark-theme .user-dropdown a:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.user-menu:hover .user-dropdown {
    display: block;
}

.login-btn-nav {
    color: white !important;
    font-weight: 600;
    display: inline-block;
    position: relative;
}

.support-btn {
    color: var(--color-text) !important;
}

.talk-expert-btn {
    background-color: #7b68ee;
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-md);
    font-weight: 600;
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    font-size: calc(var(--font-size-xl) + 5px);
    font-weight: 700;
    color: var(--color-primary);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    text-shadow: 0 2px 10px rgba(25, 118, 210, 0.2);
}

.logo:hover {
    transform: scale(1.05);
    background-position: right center;
    text-shadow: 0 2px 15px rgba(25, 118, 210, 0.3);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
    margin: 0;
    padding: 0;
}

.nav-item {
    font-size: var(--font-size-md);
    color: var(--color-text);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0.75rem;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    letter-spacing: 0.2px;
}

.nav-item:hover {
    color: var(--color-primary);
    background-color: rgba(25, 118, 210, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(25, 118, 210, 0.1);
}

.nav-item.active {
    /* All navigation highlighting disabled */
}

.nav-item.highlight {
    color: white;
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    border-radius: var(--border-radius-md);
    padding: 0.5rem 1rem;
    box-shadow: 0 4px 10px rgba(25, 118, 210, 0.3);
    transition: all 0.3s ease;
}

.nav-item.highlight:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(25, 118, 210, 0.4);
    color: white;
}

.nav-item.highlight::after {
    display: none;
}

.nav-item::after {
    content: '';
    position: absolute;
    width: 0;
    height: 3px;
    bottom: -4px;
    left: 50%;
    background-color: var(--color-primary);
    transition: width var(--transition-normal), left var(--transition-normal);
    transform: translateX(-50%);
}

.nav-item:hover::after {
    width: 70%;
}

.nav-item.active {
    color: var(--color-primary-dark);
    font-weight: 600;
}

.dark-theme .nav-item.active {
    color: var(--color-primary-light);
}

.nav-item.active::after {
    width: 70%;
    background-color: var(--color-primary-dark);
    opacity: 1;
}

.dark-theme .nav-item:hover {
    background-color: rgba(100, 181, 246, 0.05);
}

/* Dropdown Menu */
.dropdown {
    position: relative;
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--color-background);
    min-width: 200px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-md);
    z-index: 1001;
    overflow: hidden;
    border: 1px solid var(--color-border);
    transition: all 0.3s ease;
}

.dropdown:hover .dropdown-content {
    display: block;
    animation: fadeInDown 0.3s ease;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-content a {
    color: var(--color-text);
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--color-border);
}

.dropdown-content a:last-child {
    border-bottom: none;
}

.dropdown-content a:hover {
    background-color: rgba(25, 118, 210, 0.05);
    color: var(--color-primary);
}

.dark-theme .dropdown-content {
    background-color: var(--color-background-alt);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.dark-theme .dropdown-content a {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .dropdown-content a:hover {
    background-color: rgba(100, 181, 246, 0.1);
}

.menu-toggle {
    display: none;
    flex-direction: column;
    gap: 6px;
    cursor: pointer;
}

.menu-toggle span {
    display: block;
    width: 30px;
    height: 3px;
    background-color: var(--color-text);
    transition: all var(--transition-normal);
}

.menu-toggle.active span:nth-child(1) {
    transform: translateY(9px) rotate(45deg);
}

.menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.menu-toggle.active span:nth-child(3) {
    transform: translateY(-9px) rotate(-45deg);
}

/* Hero Section */
.hero-content {
    text-align: center;
    padding: var(--spacing-xl) var(--spacing-lg);
    max-width: var(--container-lg);
    margin: 0 auto;
    position: relative;
    z-index: 2;
    animation: fadeIn 1s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.hero-content h1 {
    font-size: 3.5rem; /* Slightly reduced for better balance */
    margin-bottom: var(--spacing-24);
    background: linear-gradient(90deg, var(--color-primary-dark) 0%, var(--color-primary) 50%, var(--color-accent) 100%);
    background-size: 200% auto;
    color: transparent;
    -webkit-background-clip: text;
    background-clip: text;
    animation: gradientText 5s ease infinite;
    text-shadow: 0 5px 30px rgba(25, 118, 210, 0.3);
    letter-spacing: -0.02em;
    line-height: 1.2;
    font-weight: 800;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

@keyframes gradientText {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.hero-subtitle {
    font-size: 1.25rem; /* Slightly reduced for better balance */
    margin-bottom: var(--spacing-40);
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
    animation: fadeIn 1s ease-out 0.3s both;
    color: var(--color-text-light);
    line-height: var(--line-height-relaxed);
    font-weight: 400;
    letter-spacing: 0.01em;
    position: relative;
}

.hero-subtitle::after {
    content: '';
    position: absolute;
    bottom: -1.25rem;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary), var(--color-accent));
    border-radius: 3px;
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}

header {
    position: relative;
    overflow: hidden;
    min-height: 80vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 10% 10%, rgba(25, 118, 210, 0.15) 0%, transparent 30%),
        radial-gradient(circle at 90% 90%, rgba(33, 150, 243, 0.15) 0%, transparent 30%),
        radial-gradient(circle at 50% 50%, rgba(13, 71, 161, 0.05) 0%, transparent 70%),
        linear-gradient(120deg, rgba(25, 118, 210, 0.05) 0%, rgba(66, 165, 245, 0.05) 50%, rgba(21, 101, 192, 0.05) 100%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%231976d2' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.9;
    z-index: -1;
    animation: backgroundShift 30s ease-in-out infinite alternate;
    background-size: 200% 200%;
}

@keyframes backgroundShift {
    0% { background-position: 0% 0%; }
    100% { background-position: 100% 100%; }
}

.dark-theme header::before {
    background-image:
        radial-gradient(circle at 20% 20%, rgba(100, 181, 246, 0.15) 0%, transparent 35%),
        radial-gradient(circle at 80% 80%, rgba(100, 181, 246, 0.15) 0%, transparent 35%),
        url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%2364b5f6' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}

/* Floating elements animation */
header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 30% 30%, rgba(25, 118, 210, 0.05) 0%, transparent 15%),
        radial-gradient(circle at 70% 70%, rgba(25, 118, 210, 0.05) 0%, transparent 15%),
        radial-gradient(circle at 30% 70%, rgba(25, 118, 210, 0.05) 0%, transparent 15%),
        radial-gradient(circle at 70% 30%, rgba(25, 118, 210, 0.05) 0%, transparent 15%),
        radial-gradient(circle at 50% 50%, rgba(25, 118, 210, 0.05) 0%, transparent 25%);
    z-index: -1;
    animation: floatingElements 20s ease-in-out infinite;
}

@keyframes floatingElements {
    0% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-10px) scale(1.05); }
    100% { transform: translateY(0) scale(1); }
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: var(--color-text-light);
    margin-bottom: var(--spacing-lg);
}

.cta-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

/* Buttons */
.cta-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeIn 1s ease-out 0.6s both;
}

.cta-btn {
    display: inline-block;
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    font-size: var(--font-size-md);
    position: relative;
    overflow: hidden;
    z-index: 1;
    min-width: 180px;
}

.cta-btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    z-index: -1;
    transform: translateX(-100%) rotate(45deg);
    transition: transform 0.6s;
}

.cta-btn:hover:before {
    transform: translateX(100%) rotate(45deg);
}

.cta-btn.primary {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
    border: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
    letter-spacing: 0.5px;
}

.cta-btn.primary::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
    z-index: -1;
    transition: opacity 0.5s ease;
    opacity: 0;
}

.cta-btn.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(25, 118, 210, 0.5);
    color: white;
}

.cta-btn.primary:hover::after {
    opacity: 1;
}

.cta-btn.primary:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(25, 118, 210, 0.3);
}

.cta-btn.secondary {
    background-color: transparent;
    color: var(--color-accent);
    border: 2px solid var(--color-accent);
    box-shadow: var(--shadow-accent);
    position: relative;
    overflow: hidden;
    z-index: 1;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.cta-btn.secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--color-accent);
    z-index: -1;
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.5s ease;
}

.cta-btn.secondary:hover {
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(124, 77, 255, 0.25);
    border-color: var(--color-accent);
}

.cta-btn.secondary:hover::before {
    transform: scaleX(1);
    transform-origin: left;
}

.cta-btn.secondary:active {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(25, 118, 210, 0.1);
}

.submit-btn {
    width: 100%;
    margin-top: var(--spacing-md);
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
}

.dark-theme .cta-btn.primary {
    box-shadow: 0 4px 15px rgba(100, 181, 246, 0.3);
}

.dark-theme .cta-btn.primary:hover {
    box-shadow: 0 6px 20px rgba(100, 181, 246, 0.4);
}

.dark-theme .cta-btn.secondary {
    box-shadow: 0 4px 15px rgba(100, 181, 246, 0.1);
}

.dark-theme .cta-btn.secondary:hover {
    box-shadow: 0 6px 20px rgba(100, 181, 246, 0.2);
    background-color: rgba(100, 181, 246, 0.05);
}

/* Section Styling */
section {
    padding: var(--spacing-32) var(--spacing-24); /* Further reduced to 2rem (48px to 32px) for tighter layout */
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-16); /* Further reduced from 24px to 16px for tighter spacing */
    position: relative;
    padding-bottom: var(--spacing-8); /* Reduced from 12px to 8px */
}

.section-header h2 {
    font-size: 2.2rem;
    font-weight: 800;
    margin-bottom: 0.5rem; /* Reduced from 0.8rem to 0.5rem for tighter spacing */
    color: var(--color-primary-dark);
    position: relative;
    display: inline-block;
    letter-spacing: -0.5px;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary-dark), var(--color-primary));
    border-radius: 3px;
}

.section-header p {
    color: var(--color-text-light);
    max-width: 600px;
    margin: 0 auto;
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Products/Services Section */
.products-section {
    padding: var(--spacing-xxl) 0;
    background-color: var(--color-background-alt);
}

/* Services Section */
.services-section {
    padding: var(--spacing-32) 0;
    background-color: var(--color-background);
    position: relative;
    overflow: hidden;
}

.services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(25, 118, 210, 0.03) 0%, transparent 30%),
        radial-gradient(circle at 80% 80%, rgba(25, 118, 210, 0.03) 0%, transparent 30%);
    z-index: 0;
}

.dark-theme .services-section {
    background-color: rgba(18, 18, 18, 0.7);
}

.dark-theme .services-section::before {
    background-image:
        radial-gradient(circle at 20% 20%, rgba(100, 181, 246, 0.05) 0%, transparent 30%),
        radial-gradient(circle at 80% 80%, rgba(100, 181, 246, 0.05) 0%, transparent 30%);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-12); /* Reduced from 16px to 12px for tighter layout */
    max-width: var(--container-xl);
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.service-card {
    background-color: var(--color-background);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid var(--color-border);
    position: relative;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
    z-index: 1;
}

.service-card:hover {
    transform: translate3d(0, -8px, 0);
    /* Removed box-shadow change for better performance */
    border-color: var(--color-primary-light);
}

.service-card-header {
    padding: var(--spacing-8) var(--spacing-16) var(--spacing-8);
    text-align: center;
    border-bottom: 1px solid var(--color-border);
    background-color: rgba(25, 118, 210, 0.02);
}

.service-icon {
    width: 56px;
    height: 56px;
    margin: 0 auto var(--spacing-8);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
}

.service-icon img {
    max-width: 100%;
    height: auto;
    transition: all var(--transition-normal);
}

.service-card:hover .service-icon img {
    transform: scale3d(1.1, 1.1, 1);
}

.service-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
    color: var(--color-primary-dark);
    font-weight: 700;
}

.service-subtitle {
    color: var(--color-text-light);
    font-size: var(--font-size-md);
    margin-bottom: 0;
}

.service-card-body {
    padding: 0;
    padding-top: var(--spacing-8);
    padding-bottom: var(--spacing-8);
    flex-grow: 1;
}

.service-features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.service-features-list li {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-8);
    transition: all var(--transition-fast);
}

.service-features-list li:last-child {
    margin-bottom: 0;
}

.service-features-list .feature-icon {
    color: var(--color-primary);
    margin-right: var(--spacing-8);
    font-size: 1rem;
    flex-shrink: 0;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(25, 118, 210, 0.1);
    border-radius: 50%;
    transition: all var(--transition-normal);
}

.service-card:hover .service-features-list .feature-icon {
    background-color: var(--color-primary);
    color: white;
    transform: scale3d(1.1, 1.1, 1);
}

.service-features-list .feature-text {
    font-size: var(--font-size-md);
    line-height: 1.5;
    color: var(--color-text);
}

.service-card-footer {
    padding: var(--spacing-8) var(--spacing-16) var(--spacing-12);
    text-align: center;
    margin-top: var(--spacing-4);
}

.service-cta-button {
    display: inline-block;
    padding: var(--spacing-8) var(--spacing-16);
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: white;
    border-radius: var(--border-radius-md);
    font-weight: 600;
    font-size: var(--font-size-sm);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
    width: 100%;
}

.service-cta-button:hover {
    transform: translate3d(0, -2px, 0);
    /* Removed box-shadow change for better performance */
    background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
    color: white;
}

/* Dark Theme Styles for Services */
.dark-theme .service-card {
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(100, 181, 246, 0.15);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dark-theme .service-card-header {
    background-color: rgba(100, 181, 246, 0.05);
    border-bottom: 1px solid rgba(100, 181, 246, 0.1);
}

.dark-theme .service-title {
    color: var(--color-primary-light);
}

.dark-theme .service-card:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(100, 181, 246, 0.2);
    border-color: rgba(100, 181, 246, 0.3);
}

.dark-theme .service-features-list .feature-icon {
    background-color: rgba(100, 181, 246, 0.15);
    color: var(--color-primary-light);
}

.dark-theme .service-card:hover .service-features-list .feature-icon {
    background-color: var(--color-primary-light);
    color: #121212;
}

.dark-theme .service-card-footer {
    /* No border */
}

.dark-theme .service-cta-button {
    background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.dark-theme .service-cta-button:hover {
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #2196f3 0%, #1565c0 100%);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
    max-width: var(--container-lg);
    margin-left: auto;
    margin-right: auto;
    padding: 0 var(--spacing-lg);
}

.product-card {
    background-color: var(--color-background);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid var(--color-border);
}

.dark-theme .product-card {
    background-color: var(--color-background-dark-alt);
    border: 1px solid var(--color-border-dark);
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.product-icon {
    width: 64px;
    height: 64px;
    margin-bottom: var(--spacing-md);
    color: var(--color-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-primary-light);
    border-radius: 50%;
}

.dark-theme .product-icon {
    background-color: rgba(100, 181, 246, 0.2);
}

.product-card h3 {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-sm);
    color: var(--color-heading);
}

.product-card p {
    color: var(--color-text);
    margin-bottom: var(--spacing-md);
    flex-grow: 1;
}

.product-features {
    list-style-type: none;
    padding: 0;
    margin-bottom: var(--spacing-md);
}

.product-features li {
    padding: var(--spacing-xs) 0;
    position: relative;
    padding-left: 1.5rem;
    color: var(--color-text);
}

.product-features li::before {
    content: '✓';
    color: var(--color-primary);
    position: absolute;
    left: 0;
}

.product-link {
    color: var(--color-primary);
    text-decoration: none;
    font-weight: 600;
    display: inline-block;
    margin-top: auto;
    transition: color var(--transition-fast);
}

.product-link:hover {
    color: var(--color-primary-dark);
    text-decoration: underline;
}

/* Features Section */
.features-section {
    background-color: var(--color-background-alt);
    position: relative;
    overflow: hidden;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%231976d2' fill-opacity='0.05'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 0;
}

.dark-theme .features-section::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%2364b5f6' fill-opacity='0.05'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3Cpath d='M6 5V0H5v5H0v1h5v94h1V6h94V5H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.8;
}

.dark-theme .features-section::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(100, 181, 246, 0.1) 0%, transparent 25%),
        radial-gradient(circle at 80% 70%, rgba(100, 181, 246, 0.1) 0%, transparent 25%);
    z-index: 0;
    animation: pulseGlow 10s ease-in-out infinite alternate;
}

@keyframes pulseGlow {
    0% { opacity: 0.5; }
    100% { opacity: 1; }
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-12); /* Reduced from var(--spacing-lg) to 12px for tighter layout */
    max-width: var(--container-xl);
    margin: 0 auto;
}

.feature-card {
    background-color: var(--color-background);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-16); /* Reduced from var(--spacing-24) to 16px for tighter layout */
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(25, 118, 210, 0.1);
    z-index: 1;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-accent) 100%);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
    z-index: 1;
}

.feature-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-accent) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.feature-card:hover {
    transform: translate3d(0, -8px, 0);
    /* Removed complex box-shadow for better performance */
    border-color: rgba(25, 118, 210, 0.2);
    color: white;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover::after {
    opacity: 0.9;
}

.feature-card:hover h3,
.feature-card:hover p,
.feature-card:hover .feature-metric,
.feature-card:hover .feature-metric-label {
    color: white;
    position: relative;
    z-index: 2;
}

.feature-card:hover .feature-icon {
    color: white;
    transform: scale3d(1.1, 1.1, 1);
    /* Removed filter for better performance */
    position: relative;
    z-index: 2;
}

.feature-icon {
    margin-bottom: var(--spacing-16);
    color: var(--color-primary);
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    background-color: rgba(25, 118, 210, 0.1);
    border-radius: 50%;
    font-size: 1.5rem;
    margin-top: var(--spacing-8);
    position: relative;
    z-index: 2;
}

.feature-icon::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.2) 0%, rgba(124, 77, 255, 0.2) 100%);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale3d(1.1, 1.1, 1);
    background-color: rgba(255, 255, 255, 0.2);
    /* Removed box-shadow for better performance */
}

.feature-card:hover .feature-icon::after {
    opacity: 1;
}

.feature-card h3 {
    margin-bottom: var(--spacing-12);
    transition: color 0.3s ease;
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--color-text);
}

.feature-card p {
    color: var(--color-text-light);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-relaxed);
    margin-bottom: var(--spacing-16);
    transition: color 0.3s ease;
}

.feature-card:hover h3 {
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark-theme .feature-card {
    background-color: var(--color-background-alt);
    border: 1px solid rgba(100, 181, 246, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.dark-theme .feature-icon {
    background-color: rgba(100, 181, 246, 0.1);
    color: var(--color-primary-light);
}

.dark-theme .feature-icon::after {
    background: linear-gradient(135deg, rgba(100, 181, 246, 0.2) 0%, rgba(149, 117, 205, 0.2) 100%);
}

.dark-theme .feature-card:hover {
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3), 0 5px 15px rgba(149, 117, 205, 0.15);
    border-color: rgba(100, 181, 246, 0.25);
}

.dark-theme .feature-card::before {
    background: linear-gradient(90deg, var(--color-primary-light) 0%, var(--color-accent) 100%);
    box-shadow: 0 0 10px rgba(100, 181, 246, 0.3);
}

.dark-theme .feature-card::after {
    background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-accent-dark) 100%);
}

/* About Section */
.about-section {
    background-color: var(--color-background);
}

.about-content {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xl);
    align-items: center;
    max-width: var(--container-xl);
    margin: 0 auto;
}

.about-image, .about-text {
    flex: 1;
    min-width: 300px;
}

.stats-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

.stat-item {
    flex: 1;
    min-width: 120px;
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--color-primary);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--color-text-light);
}

/* Contact Section */
.contact-section {
    background-color: var(--color-background-alt);
    position: relative;
    overflow: hidden;
}

.contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(25, 118, 210, 0.05) 0%, transparent 30%),
        radial-gradient(circle at 80% 80%, rgba(25, 118, 210, 0.05) 0%, transparent 30%),
        url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%231976d2' fill-opacity='0.03' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
    z-index: 0;
}

.dark-theme .contact-section::before {
    background-image:
        radial-gradient(circle at 20% 20%, rgba(100, 181, 246, 0.05) 0%, transparent 30%),
        radial-gradient(circle at 80% 80%, rgba(100, 181, 246, 0.05) 0%, transparent 30%),
        url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%2364b5f6' fill-opacity='0.03' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
}

.contact-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xl);
    max-width: 1000px; /* Restored to previous size but still compact */
    margin: 0 auto;
}

.contact-form-container, .contact-info {
    flex: 1;
    min-width: 300px; /* Restored to previous size */
}

.contact-form {
    background-color: var(--color-background);
    padding: var(--spacing-md); /* Further reduced padding to decrease height */
    border-radius: var(--border-radius-lg); /* More curved edges */
    box-shadow: var(--shadow-md); /* Restored to previous shadow */
    border: 2px solid var(--color-border); /* Restored to previous border */
    max-width: 450px; /* Keep same width */
    width: 100%;
}

.form-group {
    margin-bottom: var(--spacing-xs); /* Further reduced spacing to decrease height */
}

label {
    display: block;
    margin-bottom: var(--spacing-xs); /* Restored to previous spacing */
    font-weight: 500;
}

input[type="text"],
input[type="email"],
textarea {
    width: 100%;
    padding: var(--spacing-xs); /* Further reduced padding to decrease height */
    border-radius: var(--border-radius-lg); /* More curved edges for inputs */
    border: 2px solid var(--color-border); /* Restored to previous border */
    background-color: var(--color-background);
    color: var(--color-text);
    transition: all var(--transition-fast);
    font-size: var(--font-size-md); /* Restored to previous font size */
    box-shadow: var(--shadow-sm); /* Restored to previous shadow */
    margin-bottom: var(--spacing-2); /* Further reduced margin to decrease height */
}

input[type="text"]:focus,
input[type="email"]:focus,
textarea:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.3);
    background-color: var(--color-background);
}

/* Contact section in dark mode */
.dark-theme .contact-section {
    background-color: rgba(18, 18, 18, 0.7);
    position: relative;
}

.dark-theme .contact-section::before {
    opacity: 0.3;
}

.dark-theme input[type="text"],
.dark-theme input[type="email"],
.dark-theme textarea {
    background-color: #1e1e1e;
    border: 2px solid rgba(100, 181, 246, 0.3); /* Restored to previous border */
    color: white;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); /* Restored to previous shadow */
    font-size: var(--font-size-md); /* Restored to previous font size */
    opacity: 1;
    border-radius: var(--border-radius-lg); /* More curved edges for inputs */
}

.dark-theme input[type="text"]:hover,
.dark-theme input[type="email"]:hover,
.dark-theme textarea:hover {
    border-color: rgba(100, 181, 246, 0.5);
    background-color: #232323;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4), 0 0 5px rgba(100, 181, 246, 0.2);
}

.dark-theme input[type="text"]:focus,
.dark-theme input[type="email"]:focus,
.dark-theme textarea:focus {
    border-color: var(--color-primary-light);
    box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.4), 0 0 15px rgba(100, 181, 246, 0.2);
    background-color: #232323;
    color: #ffffff;
}

.dark-theme .contact-form {
    background-color: #191919;
    border: 2px solid rgba(100, 181, 246, 0.3); /* Restored to previous border */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); /* Restored to previous shadow */
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    color: white;
    padding: var(--spacing-md); /* Further reduced padding to decrease height */
    max-width: 450px; /* Keep same width */
    border-radius: var(--border-radius-lg); /* More curved edges */
}

.dark-theme .contact-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
    box-shadow: 0 0 15px rgba(100, 181, 246, 0.7);
    opacity: 0.7;
    z-index: 1;
}

.dark-theme .contact-form:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5), 0 0 20px rgba(100, 181, 246, 0.2);
    border-color: rgba(100, 181, 246, 0.3);
    transform: translateY(-5px);
}

.dark-theme .contact-icon {
    color: var(--color-primary-light);
    filter: drop-shadow(0 0 5px rgba(100, 181, 246, 0.3));
}

.dark-theme .contact-method {
    transition: all 0.3s ease;
    padding: 1rem; /* Restored to previous padding */
    border-radius: var(--border-radius-lg); /* More curved edges */
    background-color: rgba(30, 30, 30, 0.5); /* Restored to previous background */
}

.dark-theme .contact-method:hover {
    background-color: rgba(30, 30, 30, 0.5);
    transform: translateY(-5px);
}

.dark-theme .contact-details h3 {
    color: var(--color-primary-light);
}

.submit-btn {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    box-shadow: var(--shadow-md); /* Restored to previous shadow */
    transition: all 0.3s ease;
    border: none;
    color: #ffffff;
    font-weight: 600;
    font-size: 1.1rem; /* Restored to previous font size */
    padding: var(--spacing-sm) var(--spacing-lg); /* Reduced vertical padding to decrease height */
    cursor: pointer;
    width: 100%;
    margin-top: var(--spacing-md); /* Reduced margin to decrease height */
    border-radius: var(--border-radius-lg); /* More curved edges for button */
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.submit-btn:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
    background: linear-gradient(135deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
}

.dark-theme .submit-btn {
    background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2), 0 0 15px rgba(100, 181, 246, 0.2);
    transition: all 0.3s ease;
    border: none;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.dark-theme .submit-btn:hover {
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(100, 181, 246, 0.3);
    transform: translateY(-2px);
    background: linear-gradient(135deg, #2196f3 0%, #1565c0 100%);
}

.error-message {
    display: block;
    color: var(--color-error);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-xs);
}

.flash-message {
    display: block;
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-top: var(--spacing-md);
    text-align: center;
    font-weight: 500;
    box-shadow: var(--shadow-md);
}

.flash-message.success {
    background-color: var(--color-success);
    color: white;
}

.flash-message.error {
    background-color: var(--color-error);
    color: white;
}

.dark-theme .flash-message {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1rem; /* Reduced gap for compact layout */
    padding: 0.5rem; /* Added padding for better spacing */
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md); /* Restored to previous gap */
    padding: 1rem; /* Restored to previous padding */
    background-color: rgba(255, 255, 255, 0.5); /* Restored to previous background */
    border-radius: var(--border-radius-lg); /* More curved edges */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05); /* Restored to previous shadow */
    transition: all 0.3s ease; /* Restored to previous transition */
}

.contact-icon {
    color: var(--color-primary);
    flex-shrink: 0;
}

.contact-details h3 {
    margin-bottom: var(--spacing-xs); /* Restored to previous spacing */
}

.contact-details p {
    margin-bottom: var(--spacing-xs); /* Restored to previous spacing */
    color: var(--color-text-light);
}







.add-icon {
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
    opacity: 0.7;
}

.testimonial-add-cta h3 {
    margin-bottom: var(--spacing-sm);
}

.testimonial-add-cta p {
    margin-bottom: var(--spacing-lg);
    color: var(--color-text-light);
}

.placeholder-icon {
    color: var(--color-primary);
    margin-bottom: var(--spacing-md);
    opacity: 0.8;
}

.testimonial-placeholder h3 {
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-xl);
}

.testimonial-placeholder p {
    max-width: 600px;
    margin: 0 auto var(--spacing-lg);
    color: var(--color-text-light);
}

.testimonial-cta {
    margin-top: var(--spacing-lg);
}



.form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.form-actions button {
    flex: 1;
}

/* Footer */
footer {
    background-color: var(--color-background-alt);
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xl);
    max-width: var(--container-xl);
    margin: 0 auto;
    margin-bottom: var(--spacing-lg);
}

.footer-section {
    flex: 1;
    min-width: 200px;
}

.footer-section h3 {
    margin-bottom: var(--spacing-md);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: var(--color-text-light);
    transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--color-primary);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--color-border);
    color: var(--color-text-light);
    font-size: var(--font-size-sm);
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-toggle {
        display: flex;
    }

    .nav-container {
        display: none;
        position: fixed;
        top: 0;
        right: -100%;
        width: 100%;
        height: 100vh;
        background-color: var(--color-background);
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        transition: right var(--transition-normal);
        box-shadow: var(--shadow-lg);
        z-index: 1001; /* Higher than navbar-sticky */
        padding-top: 60px; /* Space for logo */
        overflow-y: auto;
    }

    .nav-container.active {
        right: 0;
        display: flex;
    }

    .nav-menu {
        flex-direction: column;
        align-items: center;
        width: 100%;
        padding: 2rem 0;
    }

    .nav-menu li {
        margin: 0.5rem 0;
    }

    .nav-right {
        flex-direction: column;
        width: 100%;
        padding: 1rem 2rem;
        gap: 1rem;
    }

    .nav-right a {
        width: 100%;
        text-align: center;
    }

    .user-menu {
        width: 100%;
    }

    .user-btn {
        width: 100%;
        justify-content: center;
    }

    .user-dropdown {
        position: static;
        width: 100%;
        box-shadow: none;
        border-radius: 0;
        margin-top: 10px;
        border: none;
        border-left: 2px solid var(--color-primary);
        margin-left: 1rem;
    }

    .user-menu:hover .user-dropdown {
        display: block;
    }

    .dropdown-content {
        position: static;
        width: 100%;
        box-shadow: none;
        border-radius: 0;
        margin-top: 10px;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        display: block;
        opacity: 0;
        border: none;
        background-color: rgba(0, 0, 0, 0.03);
    }

    .dark-theme .dropdown-content {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .dropdown:hover .dropdown-content {
        max-height: 300px;
        opacity: 1;
    }

    .dropdown-content a {
        padding: 10px 20px;
    }

    .navbar-sticky {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .logo {
        font-size: calc(var(--font-size-lg) + 3px);
    }

    .hero-content h1 {
        font-size: var(--font-size-2xl);
    }

    .hero-content .hero-subtitle {
        font-size: var(--font-size-md);
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .cta-btn {
        width: 100%;
        max-width: 300px;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .about-content, .contact-container, .service-content, .service-content.reverse {
        flex-direction: column;
    }

    .service-image, .service-details {
        width: 100%;
        margin-bottom: var(--spacing-lg);
    }

    .stats-container {
        justify-content: center;
    }

    .footer-content {
        flex-direction: column;
        gap: var(--spacing-lg);
    }
}
