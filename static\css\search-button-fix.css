/**
 * Emergency Search Button Fix
 * This file ensures the search button is always visible
 */

/* Force search button to be visible */
.search-button {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    right: 8px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    height: 36px !important;
    min-width: 80px !important;
    background-color: #4299e1 !important;
    color: white !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 0 15px !important;
    font-weight: 500 !important;
    font-size: 0.95rem !important;
    line-height: 36px !important;
    cursor: pointer !important;
    z-index: 999 !important;
    text-align: center !important;
}

/* Remove the ::after pseudo-element that was causing duplicate text */

/* Dark mode */
.dark-theme .search-button {
    background-color: #4f46e5 !important;
}

/* Hover effect */
.search-button:hover {
    background-color: #3182ce !important;
}

/* Responsive */
@media (max-width: 480px) {
    .search-button {
        height: 32px !important;
        min-width: 70px !important;
        padding: 0 10px !important;
        font-size: 0.85rem !important;
        line-height: 32px !important;
    }
}
