/**
 * Service CTA Button Fix
 * This script ensures service CTA buttons display their full text without ellipsis
 */

document.addEventListener('DOMContentLoaded', function() {
    // Apply fixes immediately and after delays to ensure they take effect
    fixServiceCTAButtons();
    setTimeout(fixServiceCTAButtons, 100);
    setTimeout(fixServiceCTAButtons, 500);
    setTimeout(fixServiceCTAButtons, 1000);
});

function fixServiceCTAButtons() {
    // Get all service CTA buttons
    const buttons = document.querySelectorAll('.service-cta-button');
    
    // Fix each button
    buttons.forEach(function(button) {
        // Set styles to prevent text truncation
        button.style.whiteSpace = 'normal';
        button.style.overflow = 'visible';
        button.style.textOverflow = 'clip';
        button.style.wordBreak = 'normal';
        button.style.wordWrap = 'normal';
        button.style.hyphens = 'none';
        
        // Ensure text is not being truncated by CSS
        const computedStyle = window.getComputedStyle(button);
        if (computedStyle.textOverflow === 'ellipsis' || 
            computedStyle.overflow === 'hidden' || 
            computedStyle.whiteSpace === 'nowrap') {
            
            // Force override any problematic styles
            const style = document.createElement('style');
            style.textContent = `
                .service-cta-button {
                    white-space: normal !important;
                    overflow: visible !important;
                    text-overflow: clip !important;
                    word-break: normal !important;
                    word-wrap: normal !important;
                    hyphens: none !important;
                }
            `;
            document.head.appendChild(style);
        }
        
        // Check if the button has the correct text
        if (button.textContent.includes('...')) {
            // Remove ellipsis
            button.textContent = button.textContent.replace(/\.{3,}/g, '');
        }
        
        // Check specific buttons and set their text explicitly
        if (button.textContent.includes('Automate') && button.textContent.includes('Support')) {
            button.textContent = 'Automate 85% of Support Tickets Now';
        } else if (button.textContent.includes('Transform') && button.textContent.includes('Customer')) {
            button.textContent = 'Transform Customer Experience in 14 Days';
        } else if (button.textContent.includes('Triple') && button.textContent.includes('Efficiency')) {
            button.textContent = 'Triple Your Team\'s Efficiency Today';
        }
    });
    
    // Log to console for debugging
    console.log('Service CTA button fix applied to ' + buttons.length + ' buttons');
}
