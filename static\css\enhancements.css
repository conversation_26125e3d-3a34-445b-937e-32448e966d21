/* Enhanced Hero Section */
.hero-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 4rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.hero-content h1 {
    font-size: 3rem;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    text-align: center;
    max-width: 800px;
}

.hero-subtitle {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    text-align: center;
    max-width: 800px;
    color: var(--color-text-light);
    line-height: 1.6;
}

.hero-preview {
    width: 100%;
    max-width: 500px;
    margin-top: 3rem;
    position: relative;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.chat-preview {
    background-color: var(--color-background);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 0 0 0 1px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    transform: perspective(1000px) rotateX(2deg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
}

.chat-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0) 100%);
    z-index: 1;
    pointer-events: none;
}

.dark-theme .chat-preview {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(30, 30, 30, 0.8);
}

.chat-header {
    display: flex;
    align-items: center;
    padding: 15px;
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: white;
}

.chat-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: white;
    color: var(--color-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 10px;
}

.chat-title {
    font-weight: 600;
}

.chat-messages {
    padding: 15px;
    max-height: 300px;
    overflow-y: auto;
}

.message {
    margin-bottom: 15px;
    padding: 10px 15px;
    border-radius: 18px;
    max-width: 80%;
    position: relative;
    animation: messageAppear 0.3s ease-out;
}

@keyframes messageAppear {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.message.user {
    background-color: #f0f0f0;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.dark-theme .message.user {
    background-color: #2a2a2a;
}

.message.bot {
    background-color: var(--color-primary-light);
    color: #333;
    margin-right: auto;
    border-bottom-left-radius: 4px;
}

.dark-theme .message.bot {
    background-color: var(--color-primary-dark);
    color: white;
}

.typing-indicator {
    display: flex;
    align-items: center;
    margin-right: auto;
    background-color: #e3f2fd;
    padding: 10px 15px;
    border-radius: 18px;
    border-bottom-left-radius: 4px;
    width: 60px;
    animation: pulse 2s infinite ease-in-out;
    position: relative;
    overflow: hidden;
}

.typing-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.dark-theme .typing-indicator {
    background-color: var(--color-primary-dark);
}

.typing-indicator .dot {
    height: 8px !important;
    width: 8px !important;
    background-color: rgba(0, 0, 0, 0.3) !important;
    border-radius: 50% !important;
    display: inline-block !important;
    margin-right: 5px !important;
    animation: typing 1.4s infinite ease-in-out !important;
    transform: translateZ(0) !important;
    will-change: transform !important;
}

.dark-theme .typing-indicator .dot {
    background-color: rgba(255, 255, 255, 0.5);
}

.typing-indicator .dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-indicator .dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
    animation-delay: 0.4s;
    margin-right: 0;
}

@keyframes typing {
    0% { transform: translateY(0) translateZ(0); opacity: 0.5; }
    50% { transform: translateY(-5px) translateZ(0); opacity: 1; }
    100% { transform: translateY(0) translateZ(0); opacity: 0.5; }
}

@keyframes pulse {
    0% { transform: scale(1) translateZ(0); }
    50% { transform: scale(1.05) translateZ(0); }
    100% { transform: scale(1) translateZ(0); }
}

.author-info h4 {
    margin: 0;
    font-size: 1rem;
}

.author-info p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--color-text-light);
}

/* Enhanced Feature Cards */
.feature-card {
    transition: transform 0.3s ease !important;
    cursor: pointer !important;
    position: relative !important;
    overflow: visible !important;
    z-index: 1 !important;
    margin-bottom: 20px !important;
    will-change: transform !important;
    transform: translateZ(0) !important;
}

.feature-card::after {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%) !important;
    opacity: 0 !important;
    z-index: 0 !important;
    transition: opacity 0.3s ease !important;
    pointer-events: none !important;
    transform: translateZ(0) !important;
}

.metric-wrapper {
    position: relative !important;
    z-index: 2 !important;
    background-color: inherit !important;
    padding: 0.25rem 0 !important;
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
    transform: translateZ(0) !important;
}

.feature-card:hover {
    transform: translateY(-5px) translateZ(0) !important;
}

.feature-card:hover::after {
    opacity: 0.02 !important;
}

.feature-card:hover .feature-metric,
.feature-card:hover .feature-metric-label {
    transform: translateZ(0) !important;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

.feature-icon {
    transition: transform 0.3s ease;
}

.feature-metric {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-primary);
    margin-top: 1rem;
    margin-bottom: 0.25rem;
    transition: color 0.3s ease;
    display: block;
    position: relative;
    z-index: 2;
    background-color: inherit;
    padding: 0.25rem 0;
}

.feature-metric-label {
    font-size: 0.9rem;
    color: var(--color-text-light);
    margin-top: 0;
    transition: color 0.3s ease;
    display: block;
    position: relative;
    z-index: 2;
    margin-bottom: 1rem;
    background-color: inherit;
    padding: 0.25rem 0;
}

.dark-theme .feature-metric {
    color: var(--color-primary-light);
    background-color: var(--color-bg-secondary);
}

.dark-theme .feature-metric-label {
    background-color: var(--color-bg-secondary);
}

.dark-theme .metric-wrapper {
    background-color: var(--color-bg-secondary);
}

.dark-theme .feature-card:hover .feature-metric,
.dark-theme .feature-card:hover .feature-metric-label,
.dark-theme .feature-card:hover .metric-wrapper {
    background-color: var(--color-bg-secondary);
    position: relative;
    z-index: 2;
}

/* Demo Section */
.demo-section {
    padding: 5rem 2rem;
    background-color: var(--color-background-alt);
}

.demo-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.demo-video {
    width: 100%;
    max-width: 800px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin: 3rem 0;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-theme .demo-video {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.demo-video video, .demo-video img {
    width: 100%;
    display: block;
}

/* Industry Solutions */
.industry-solutions {
    padding: 5rem 2rem;
}

.solutions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 var(--spacing-4);
}

.solution-card {
    background-color: var(--color-bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-md);
    transition: var(--transition-all);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: auto;
  min-height: 320px;
    width: 100%;
    border: 1px solid var(--color-border);
    z-index: 1;
}

.solution-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--color-primary-200);
    background-color: var(--color-bg-primary);
}

.dark-theme .solution-card {
    background-color: var(--color-bg-secondary);
    border-color: var(--color-border);
    box-shadow: var(--shadow-md);
}

.dark-theme .solution-card:hover {
    box-shadow: var(--shadow-xl);
    border-color: var(--color-primary-700);
    background-color: var(--color-bg-secondary);
}

.solution-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(to right, var(--color-primary-600), var(--color-accent-600));
    transition: var(--transition-all);
    z-index: 1;
}

.solution-card:hover::before {
    height: 6px;
}

.dark-theme .solution-card::before {
    background: linear-gradient(to right, var(--color-primary-500), var(--color-accent-500));
}

.solution-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-lg);
    background: linear-gradient(135deg, var(--color-primary-100), var(--color-primary-50));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-4);
    transition: var(--transition-all);
    position: relative;
}

.solution-icon i {
    font-size: 24px;
    color: var(--color-primary-600);
    transition: var(--transition-all);
}

.solution-card:hover .solution-icon {
    transform: scale(1.1);
    background: linear-gradient(135deg, var(--color-primary-200), var(--color-primary-100));
}

.solution-card:hover .solution-icon i {
    color: var(--color-primary-700);
}

.dark-theme .solution-icon {
    background: linear-gradient(135deg, var(--color-primary-900), var(--color-primary-800));
}

.dark-theme .solution-card:hover .solution-icon {
    background: linear-gradient(135deg, var(--color-primary-800), var(--color-primary-700));
}

.dark-theme .solution-icon i {
    color: var(--color-primary-400);
}

.dark-theme .solution-card:hover .solution-icon i {
    color: var(--color-primary-300);
}

.solution-title {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    margin-bottom: var(--spacing-3);
    color: var(--color-text-primary);
    transition: var(--transition-colors);
}

.solution-card:hover .solution-title {
    color: var(--color-primary-600);
}

.dark-theme .solution-card:hover .solution-title {
    color: var(--color-primary-400);
}

.solution-description {
    font-size: 0.95rem;
    color: var(--color-text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--spacing-4);
    flex-grow: 1;
    text-align: center;
    max-width: 100%;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    padding: 0 var(--spacing-2);
}

.solution-card:hover .solution-description {
    color: var(--color-text-secondary);
}

.dark-theme .solution-card:hover .solution-description {
    color: var(--color-text-secondary);
}

.solution-benefits {
    list-style: none;
    padding: 0;
    margin: 0.75rem 0 0 0;
    font-size: 0.85rem;
    color: var(--color-text-secondary);
}

.solution-benefits li {
    position: relative;
    margin-bottom: 0.5rem;
    line-height: 1.4;
    display: flex;
    align-items: flex-start;
}

/* Remove the pseudo-element checkmark to avoid duplicates */
.solution-benefits li:before {
    content: none;
}

/* FAQ Section */
.faq-section {
    padding: 4rem 1.5rem;
    background-color: var(--color-background-alt);
}

.faq-container {
    max-width: 650px;
    margin: 0 auto;
}

.faq-item {
    margin-bottom: 0.75rem;
    border: 1px solid rgba(0, 0, 0, 0.05); /* Very subtle border */
    border-radius: 1.5rem; /* Much more rounded edges */
    overflow: hidden;
    background: rgba(255, 255, 255, 0.5); /* Subtle background */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

.dark-theme .faq-item {
    border: 1px solid rgba(255, 255, 255, 0.05); /* Very subtle border for dark mode */
    background: rgba(30, 30, 30, 0.5); /* Subtle dark background */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* Subtle shadow for dark mode */
}

.faq-question {
    padding: 1rem;
    background: none;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.faq-question:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.dark-theme .faq-question:hover {
    background-color: rgba(255, 255, 255, 0.02);
}

.faq-icon {
    display: inline-block;
    margin-right: 0.75rem;
    font-size: 1rem;
}

.faq-question::after {
    content: ''; /* Removed Font Awesome down arrow */
}

.faq-item.active .faq-question::after {
    transform: none;
}

.faq-answer {
    padding: 0 1rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease, padding 0.3s ease, opacity 0.3s ease;
    opacity: 0;
    transform: translateY(-10px);
    background: none;
}

.faq-item.active .faq-answer {
    padding: 0 1rem 1rem;
    max-height: 800px;
    opacity: 1;
    transform: translateY(0);
    transition: max-height 0.4s ease, padding 0.3s ease, opacity 0.3s ease 0.2s, transform 0.3s ease 0.2s;
}

/* Final CTA Banner */
.final-cta {
    padding: 5rem 2rem;
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.final-cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
    opacity: 0.5;
    animation: particleMove 30s linear infinite;
}

@keyframes particleMove {
    0% { background-position: 0 0; }
    100% { background-position: 100px 100px; }
}

.final-cta-container {
    max-width: 800px;
    margin: 0 auto;
}

.final-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

.final-cta p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.final-cta .cta-buttons {
    justify-content: center;
}

.final-cta .cta-btn.primary {
    background-color: white;
    color: var(--color-primary-dark);
}

.final-cta .cta-btn.primary:hover {
    background-color: rgba(255, 255, 255, 0.9);
    transform: translateY(-3px);
}

.final-cta .cta-btn.secondary {
    border-color: white;
    color: white;
}

.final-cta .cta-btn.secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.2rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .testimonial-grid {
        grid-template-columns: 1fr;
    }

    .solutions-grid {
        grid-template-columns: 1fr 1fr;
    }

    .final-cta h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .hero-content h1 {
        font-size: 1.8rem;
    }

    .solutions-grid {
        grid-template-columns: 1fr;
    }

    .client-logos {
        gap: 1.5rem;
    }

    .client-logo {
        height: 30px;
    }
}

/* Contact Form UX */
.error-message {
    color: #e57373;
    font-size: 0.85rem;
    margin-top: 5px;
    display: none;
}

input.error, textarea.error {
    border-color: #e57373 !important;
    box-shadow: 0 0 0 2px rgba(229, 115, 115, 0.2) !important;
}

.form-confirmation {
    background-color: rgba(129, 199, 132, 0.1);
    border: 1px solid rgba(129, 199, 132, 0.3);
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    text-align: center;
    display: none;
}

.form-confirmation p {
    color: #4caf50;
    margin: 0;
}

.dark-theme .form-confirmation {
    background-color: #121212;
    border: 1px solid rgba(129, 199, 132, 0.2);
}

.dark-theme .form-confirmation p {
    color: #81c784;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.dark-theme .modal {
    background-color: #000000;
}

.modal.active {
    display: flex;
    opacity: 1;
}

.modal-content {
    background-color: var(--color-background);
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    padding: 30px;
    position: relative;
    transform: translateY(20px);
    transition: transform 0.3s ease;
    max-height: 90vh;
    overflow-y: auto;
}

.login-modal-content {
    max-width: 600px;
    padding: 40px;
}

.login-input {
    height: 50px;
    font-size: 16px;
    width: 100%;
    padding: 10px 15px;
    border-radius: var(--border-radius-md);
    border: 1px solid var(--color-border);
    background-color: var(--color-background);
    transition: all 0.3s ease;
}

.login-input:focus {
    border-color: var(--color-primary);
    box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
    outline: none;
}

.dark-theme .login-input {
    background-color: #1e1e1e;
    border: 1px solid rgba(100, 181, 246, 0.3);
    color: var(--color-text);
}

.dark-theme .login-input:focus {
    border-color: var(--color-primary-light);
    box-shadow: 0 0 0 2px rgba(100, 181, 246, 0.3);
    background-color: #232323;
}

.login-btn {
    height: 50px;
    font-size: 16px;
    font-weight: 600;
    margin-top: 20px;
}

.modal.active .modal-content {
    transform: translateY(0);
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    cursor: pointer;
    color: var(--color-text-secondary);
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--color-primary);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.checkbox-container input {
    margin-right: 8px;
}

.forgot-password {
    color: var(--color-primary);
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-footer {
    margin-top: 20px;
    text-align: center;
    font-size: 0.9rem;
}

.login-footer a {
    color: var(--color-primary);
    text-decoration: none;
}

.login-footer a:hover {
    text-decoration: underline;
}

/* Chatbot Styles */
.chatbot-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 999;
    transform: translateY(100%);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    border: 1px solid #e0e0e0;
}

.chatbot-launcher {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #1976d2, #64b5f6);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 998;
    transition: all 0.3s ease;
}

.chatbot-launcher:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.launcher-icon {
    color: white;
    font-size: 24px;
}

.dark-theme .chatbot-launcher {
    background: linear-gradient(135deg, #1565c0, #42a5f5);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.chatbot-widget.active {
    transform: translateY(0);
    opacity: 1;
}

.chatbot-header {
    background-color: #1976d2;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    color: white;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

.chatbot-title {
    display: flex;
    align-items: center;
    color: white;
    font-weight: 600;
}

.chatbot-logo {
    width: 24px;
    height: 24px;
    margin-right: 10px;
    border-radius: 50%;
    background-color: white;
}

.chatbot-controls button {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    margin-left: 10px;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.chatbot-controls button:hover {
    opacity: 1;
}

.chatbot-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: white;
}

.chatbot-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    background-color: #e5ddd5;
    display: flex;
    flex-direction: column;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAB3RJTUUH4QQQEwkZ/d/MUAAAABl0RVh0Q29tbWVudABDcmVhdGVkIHdpdGggR0lNUFeBDhcAAADKSURBVGje7dixCcMwFIXhH5cM4M7bZAFvkCmyQSZwlS08glfwBFYlN26CX5EihPiByif4QOBWHwghSJIkSZIk6fs1+c8vt9u963ruzb5nPQICAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAflryEuSJEmSJEkr9gQCjD7TyuHtYwAAAABJRU5ErkJggg==');
}

.chatbot-messages::-webkit-scrollbar {
    width: 6px;
}

.chatbot-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chatbot-messages::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

.dark-theme .chatbot-messages {
    background-color: #1e1e1e;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAB3RJTUUH4QQQEwkZ/d/MUAAAABl0RVh0Q29tbWVudABDcmVhdGVkIHdpdGggR0lNUFeBDhcAAADKSURBVGje7dixCcMwFIXhH5cM4M7bZAFvkCmyQSZwlS08glfwBFYlN26CX5EihPiByif4QOBWHwghSJIkSZIk6fs1+c8vt9u963ruzb5nPQICAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAflryEuSJEmSJEkr9gQCjD7TyuHtYwAAAABJRU5ErkJggg==');
    background-blend-mode: overlay;
}

.message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.bot-message {
    margin-right: 50px;
}

.user-message {
    flex-direction: row-reverse;
    margin-left: 50px;
    justify-content: flex-end;
}

.message-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
}

.user-message .message-avatar {
    margin-right: 0;
    margin-left: 10px;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.message-content {
    background-color: white;
    padding: 10px 15px;
    border-radius: 18px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    position: relative;
    max-width: 80%;
}

.dark-theme .message-content {
    background-color: #2d2d2d;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.bot-message .message-content {
    border-bottom-left-radius: 4px;
    background-color: #e3f2fd;
}

.dark-theme .bot-message .message-content {
    background-color: #1565c0;
    color: white;
}

.user-message .message-content {
    border-bottom-right-radius: 4px;
    background-color: #dcf8c6;
}

.dark-theme .user-message .message-content {
    background-color: #2e7d32;
    color: white;
}

.message-content p {
    margin: 0;
    line-height: 1.4;
    font-size: 14px;
}

.typing-indicator-container {
    display: none;
    margin-bottom: 15px;
}

.chatbot-input {
    display: flex;
    padding: 10px 15px;
    background-color: white;
    border-top: 1px solid #e0e0e0;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
}

.dark-theme .chatbot-input {
    background-color: #1e1e1e;
    border-top: 1px solid #333;
}

.chatbot-input input {
    flex: 1;
    border: none;
    padding: 10px 15px;
    border-radius: 20px;
    background-color: #f5f5f5;
    outline: none;
    font-size: 14px;
}

.dark-theme .chatbot-input input {
    background-color: #333;
    color: white;
}

.chatbot-input .send-btn {
    background-color: #1976d2;
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-left: 10px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: background-color 0.3s ease;
}

.chatbot-input .send-btn:hover {
    background-color: #1565c0;
}

.chatbot-launcher {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: #1976d2;
    border-radius: 50%;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    z-index: 998;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.chatbot-launcher:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.chatbot-launcher.hidden {
    display: none;
}

.launcher-icon {
    color: white;
    font-size: 24px;
}

/* Add Font Awesome for icons */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');
