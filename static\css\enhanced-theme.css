/**
 * Enhanced Theme Implementation
 * Improves dark/light mode consistency and transitions
 */

/* Theme Variables - Light Mode (Default) */
:root {
  /* Primary Colors */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Semantic Colors - Light Theme (Matching dark theme design) */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #ffffff;
  --color-bg-tertiary: #ffffff;
  --color-bg-accent: #ffffff;

  --color-text-primary: #1a1f36;
  --color-text-secondary: #2d3a58;
  --color-text-tertiary: #4a5d82;
  --color-text-accent: #2563eb;

  --color-border-light: #dae5f0;
  --color-border-medium: #c9dbf0;
  --color-border-dark: #a3c0e6;

  /* UI Elements */
  --color-card-bg: #ffffff;
  --color-card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --color-card-border: rgba(37, 99, 235, 0.1);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  --gradient-hero: linear-gradient(135deg, #2563eb 0%, #3b82f6 50%, #2563eb 100%);
  --gradient-card-hover: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(29, 78, 216, 0.05) 100%);

  /* Shadows */
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-outline: 0 0 0 3px rgba(37, 99, 235, 0.4);

  /* Transitions */
  --transition-fast: 150ms;
  --transition-normal: 300ms;
  --transition-slow: 500ms;
  --transition-ease: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Theme Overrides */
.dark-theme {
  /* Semantic Colors - Dark Theme */
  --color-bg-primary: #1a1f36;
  --color-bg-secondary: #1a1f36;
  --color-bg-tertiary: #1a1f36;
  --color-bg-accent: #1a1f36;

  --color-text-primary: #f0f4f8;
  --color-text-secondary: #d9e2ec;
  --color-text-tertiary: #bcccdc;
  --color-text-accent: #4299e1;

  --color-border-light: #2f3a5f;
  --color-border-medium: #3a4a80;
  --color-border-dark: #4c63b6;

  /* UI Elements */
  --color-card-bg: #1a1f36;
  --color-card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.25), 0 2px 4px -1px rgba(0, 0, 0, 0.15);
  --color-card-border: rgba(255, 255, 255, 0.08);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  --gradient-hero: linear-gradient(135deg, #4299e1 0%, #3182ce 50%, #4299e1 100%);
  --gradient-card-hover: linear-gradient(135deg, rgba(66, 153, 225, 0.15) 0%, rgba(49, 130, 206, 0.08) 100%);

  /* Shadows */
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.25);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.35), 0 2px 4px -1px rgba(0, 0, 0, 0.25);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.35), 0 4px 6px -2px rgba(0, 0, 0, 0.25);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.35), 0 10px 10px -5px rgba(0, 0, 0, 0.25);
  --shadow-outline: 0 0 0 3px rgba(66, 153, 225, 0.5);
}

/* Enhanced Body Styles */
body {
  transition: background-color var(--transition-normal), color var(--transition-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
}

/* Ensure light mode is properly applied */
body:not(.dark-theme) {
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
}

/* Enhanced Navbar */
.navbar {
  background-color: var(--color-bg-primary);
  border-bottom: 1px solid var(--color-border-light);
  transition: all var(--transition-normal);
}

/* Ensure navbar looks good in dark mode */
.dark-theme .navbar {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.navbar.scrolled {
  box-shadow: var(--shadow-sm);
}

/* Enhanced Logo */
.navbar-logo {
  color: var(--color-text-accent);
}

/* Enhanced Navigation Links */
.navbar-link {
  color: var(--color-text-secondary);
}

.navbar-link:hover,
.navbar-link.active {
  color: var(--color-text-accent);
}

.navbar-link::after {
  background-color: var(--color-text-accent);
}

/* Enhanced Dropdown Menu */
.dropdown-menu {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-md);
}

.dropdown-item {
  color: var(--color-text-secondary);
}

.dropdown-item:hover {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-accent);
}

/* Enhanced Login Button */
.login-btn {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-sm);
}

.login-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Enhanced Hero Section */
.hero-section {
  background-color: var(--color-bg-primary);
}

.hero-bg::before {
  background-image:
    radial-gradient(circle at 20% 20%, rgba(37, 99, 235, 0.15) 0%, transparent 40%),
    radial-gradient(circle at 80% 80%, rgba(37, 99, 235, 0.15) 0%, transparent 40%);
}

.dark-theme .hero-bg::before {
  background-image:
    radial-gradient(circle at 20% 20%, rgba(66, 153, 225, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(66, 153, 225, 0.3) 0%, transparent 50%);
}

.hero-title {
  color: var(--color-text-primary);
  animation: none;
}

.hero-subtitle {
  color: var(--color-text-secondary);
}

/* Enhanced Feature Cards */
.feature-card {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  background-image: var(--gradient-card-hover);
}

.feature-icon {
  color: var(--color-text-accent);
}

/* Enhanced Metrics */
.metric-wrapper {
  background-color: var(--color-card-bg) !important;
  box-shadow: var(--shadow-sm) !important;
}

.feature-metric {
  color: var(--color-text-accent) !important;
  background-color: var(--color-card-bg) !important;
}

.feature-metric-label {
  color: var(--color-text-tertiary) !important;
  background-color: var(--color-card-bg) !important;
}

/* Enhanced Section Headers */
.section-header h2 {
  color: var(--color-text-primary);
}

.section-header h2::after {
  background: var(--color-text-accent);
}

/* Enhanced Theme Toggle */
.theme-toggle {
  position: relative;
  width: 37.4px;
  height: 18.7px;
  background-color: var(--color-bg-tertiary);
  border-radius: 30px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all var(--transition-normal);
  border: 1px solid var(--color-border-medium);
  overflow: hidden;
}

.theme-toggle:hover {
  box-shadow: var(--shadow-sm);
}

.theme-toggle-slider {
  position: absolute;
  top: 1.7px;
  left: 1.7px;
  width: 15.3px;
  height: 15.3px;
  background-color: white;
  border-radius: 50%;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.theme-toggle-input:checked + .theme-toggle-slider {
  transform: translateX(18.7px) !important;
  background-color: var(--color-primary-600);
  box-shadow: 0 0 5px rgba(37, 99, 235, 0.5);
}

/* Enhanced Animations (Removed text gradient) */
@keyframes textGradient {
  0%, 50%, 100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 50%, 100% { transform: translateY(0px); }
}

/* Enhanced Footer */
footer {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-secondary);
  border-top: 1px solid var(--color-border-light);
}

/* Enhanced Modal */
.modal-content {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-xl);
}

/* Enhanced Form Elements */
input, textarea, select {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border-medium);
  color: var(--color-text-primary);
}

input:focus, textarea:focus, select:focus {
  border-color: var(--color-text-accent);
  box-shadow: var(--shadow-outline);
}

/* Enhanced Buttons */
.btn {
  background: var(--gradient-primary);
  color: white;
  transition: all var(--transition-normal);
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: transparent;
  color: var(--color-text-accent);
  border: 1px solid var(--color-text-accent);
}

.btn-secondary:hover {
  background-color: rgba(37, 99, 235, 0.1);
}

/* Enhanced Chatbot */
.chatbot-widget {
  background-color: var(--color-card-bg);
  border: 1px solid var(--color-border-light);
  box-shadow: var(--shadow-xl);
}

.chatbot-header {
  background-color: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border-light);
}

.message-content {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

.bot-message .message-content {
  background-color: var(--color-bg-accent);
}



/* Enhanced FAQ */
.faq-item {
  border: 1px solid var(--color-border-light);
  background-color: var(--color-card-bg);
}

.faq-question {
  color: var(--color-text-primary);
}

.faq-answer {
  color: var(--color-text-secondary);
}

/* Enhanced CTA Section */
.cta-section {
  background-color: var(--color-bg-secondary);
  border-top: 1px solid var(--color-border-light);
  border-bottom: 1px solid var(--color-border-light);
}

/* Enhanced Preloader */
.preloader {
  background-color: var(--color-bg-primary);
}

.loader:before, .loader:after {
  background-color: var(--color-text-accent);
}

.loader-text {
  color: var(--color-text-accent);
}
