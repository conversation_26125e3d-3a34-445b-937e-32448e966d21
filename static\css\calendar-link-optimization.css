/**
 * Calendar Link Optimization
 * Optimizes the calendar link for better performance and user experience
 */

/* Optimized calendar link styling */
.optimized-calendar-link {
    position: relative;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-font-smoothing: subpixel-antialiased;
}

.optimized-calendar-link:hover {
    transform: translateY(-2px);
}

.optimized-calendar-link:active {
    transform: translateY(1px);
}

/* Add a subtle loading indicator for the calendar link */
.optimized-calendar-link::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.2s ease;
    pointer-events: none;
    border-radius: inherit;
}

.optimized-calendar-link:active::after {
    opacity: 1;
}

/* Ensure the calendar icon is properly aligned */
.optimized-calendar-link i.fa-calendar,
.optimized-calendar-link i.fa-calendar-alt {
    margin-right: 6px;
    vertical-align: middle;
}

/* Ensure the text is properly aligned */
.optimized-calendar-link .btn-text {
    vertical-align: middle;
}

/* Optimize for dark mode */
.dark-theme .optimized-calendar-link::after {
    background-color: rgba(0, 0, 0, 0.1);
}
