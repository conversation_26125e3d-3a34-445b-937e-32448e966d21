/**
 * Button Alignment Fix
 * Ensures both CTA buttons are perfectly aligned on the same line
 */

/* Hero CTA Button Alignment - Index Page */
.hero-cta {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important; /* Changed from stretch to center for perfect alignment */
    gap: 1rem !important;
    flex-wrap: nowrap !important;
    max-width: 800px !important;
    margin: 0 auto !important;
    padding: 0 !important;
}

.hero-cta .btn {
    flex: 1 !important;
    max-width: 400px !important;
    height: 60px !important; /* Fixed height for both buttons */
    min-height: 60px !important;
    max-height: 60px !important;
    padding: 12px 24px !important;
    font-size: 0.9rem !important;
    text-align: center !important;
    white-space: normal !important;
    line-height: 1.2 !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    margin: 0 !important; /* Remove any margins that might cause misalignment */
    vertical-align: middle !important; /* Ensure vertical alignment */
}

/* Landing Page CTA Button Alignment */
.cta-buttons {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important; /* Changed from stretch to center for perfect alignment */
    gap: 1rem !important;
    flex-wrap: nowrap !important;
    margin: 0 auto !important;
    padding: 0 !important;
}

.cta-buttons .cta-btn {
    flex: 1 !important;
    max-width: 400px !important;
    height: 60px !important; /* Fixed height for both buttons */
    min-height: 60px !important;
    max-height: 60px !important;
    padding: 12px 24px !important;
    font-size: 0.9rem !important;
    text-align: center !important;
    white-space: normal !important;
    line-height: 1.2 !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
    margin: 0 !important; /* Remove any margins that might cause misalignment */
    vertical-align: middle !important; /* Ensure vertical alignment */
}

/* Specific button styling to maintain colors */
.hero-cta .btn-primary,
.cta-buttons .cta-btn.primary {
    background: #3b82f6 !important;
    background-image: none !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.hero-cta .btn-secondary,
.cta-buttons .cta-btn.secondary {
    background: #8b5cf6 !important;
    background-image: none !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3) !important;
}

/* Hover effects */
.hero-cta .btn:hover,
.cta-buttons .cta-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2) !important;
}

/* Enhanced CTA Section Alignment */
.enhanced-cta-buttons {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important; /* Perfect center alignment */
    gap: 1rem !important;
    flex-wrap: nowrap !important;
    margin: 0 auto !important;
    padding: 0 !important;
}

.enhanced-cta-buttons .enhanced-cta-btn {
    flex: 1 !important;
    max-width: 400px !important;
    height: 60px !important; /* Fixed height for both buttons */
    min-height: 60px !important;
    max-height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 !important;
    vertical-align: middle !important;
}

/* Mobile responsiveness - Keep buttons aligned */
@media (max-width: 768px) {
    .hero-cta,
    .cta-buttons,
    .enhanced-cta-buttons {
        flex-direction: row !important;
        gap: 0.5rem !important;
        flex-wrap: nowrap !important;
        justify-content: center !important;
        align-items: center !important; /* Maintain center alignment on mobile */
    }

    .hero-cta .btn,
    .cta-buttons .cta-btn,
    .enhanced-cta-buttons .enhanced-cta-btn {
        flex: 1 !important;
        max-width: 45% !important;
        height: 50px !important; /* Slightly smaller on mobile but still aligned */
        min-height: 50px !important;
        max-height: 50px !important;
        font-size: 0.8rem !important;
        padding: 10px 16px !important;
    }
}

@media (max-width: 480px) {
    .hero-cta .btn,
    .cta-buttons .cta-btn,
    .enhanced-cta-buttons .enhanced-cta-btn {
        font-size: 0.75rem !important;
        padding: 8px 12px !important;
        max-width: 48% !important;
        height: 48px !important;
        min-height: 48px !important;
        max-height: 48px !important;
    }
}

/* Override any conflicting styles */
.hero-cta *,
.cta-buttons *,
.enhanced-cta-buttons * {
    box-sizing: border-box !important;
}

/* Ensure no floating or positioning issues */
.hero-cta::before,
.hero-cta::after,
.cta-buttons::before,
.cta-buttons::after,
.enhanced-cta-buttons::before,
.enhanced-cta-buttons::after {
    display: none !important;
}
