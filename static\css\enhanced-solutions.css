/**
 * Enhanced Solutions Section
 * Professional styling for industry solutions cards
 */

/* Solutions Section Container - REDUCED SPACING */
.industry-solutions,
#solutions,
section#solutions,
section.industry-solutions {
  padding: 2rem 1.5rem !important; /* Reduced from 3rem 2rem for tighter layout */
  position: relative !important;
  overflow: hidden !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  min-height: 200px !important;
  z-index: 1 !important;
  margin: 0 !important;
  background-color: var(--color-bg-primary, #f8fafc) !important;
}

/* Section Header - REDUCED SPACING */
.industry-solutions .section-header,
#solutions .section-header {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  text-align: center !important;
  margin-bottom: 24px !important; /* Reduced from 40px to 24px for tighter layout */
  max-width: 800px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.industry-solutions .section-header h2,
#solutions .section-header h2 {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 2.25rem !important;
  font-weight: 700 !important;
  margin-bottom: 0.5rem !important; /* Reduced from 0.75rem to 0.5rem for tighter spacing */
  color: var(--color-text-primary, #0f172a) !important;
}

.industry-solutions .section-header p,
#solutions .section-header p {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 1.125rem !important;
  color: var(--color-text-secondary, #334155) !important;
}

/* Solutions Grid - REDUCED SPACING */
.solutions-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
  gap: 1rem !important; /* Reduced from 1.25rem to 1rem for tighter layout */
  margin-top: 1.5rem !important; /* Reduced from 2rem to 1.5rem */
  max-width: 1200px !important;
  margin-left: auto !important;
  margin-right: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  min-height: 100px !important;
  overflow: visible !important;
  position: relative !important;
  z-index: 4 !important;
}

/* Staggered animation for solution cards */
.solutions-grid .solution-card:nth-child(1) { animation-delay: 0.1s; }
.solutions-grid .solution-card:nth-child(2) { animation-delay: 0.2s; }
.solutions-grid .solution-card:nth-child(3) { animation-delay: 0.3s; }
.solutions-grid .solution-card:nth-child(4) { animation-delay: 0.4s; }
.solutions-grid .solution-card:nth-child(5) { animation-delay: 0.5s; }
.solutions-grid .solution-card:nth-child(6) { animation-delay: 0.6s; }

/* Solution Card - REDUCED SPACING */
.solution-card {
  background-color: var(--color-background, #ffffff) !important;
  border-radius: 10px !important;
  padding: 1rem !important; /* Reduced from 1.25rem to 1rem for tighter layout */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  height: 100% !important;
  min-height: 240px !important; /* Reduced from 260px to 240px */
  border: 1px solid var(--color-border, #e2e8f0) !important;
  z-index: 1 !important;
  animation: fadeInUp 0.6s ease forwards !important;
  opacity: 1 !important;
  visibility: visible !important;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.solution-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
  border-color: var(--color-primary);
}

.solution-card::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
  z-index: 1;
}

.solution-card:hover::after {
  transform: scaleX(1);
}

/* Solution Icon - REDUCED SPACING */
.solution-icon {
  width: 55px !important;
  height: 55px !important;
  border-radius: 10px !important;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(25, 118, 210, 0.2) 100%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-bottom: 0.75rem !important; /* Reduced from 1rem to 0.75rem for tighter spacing */
  transition: all 0.3s ease !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 2 !important;
}

.solution-icon i {
  font-size: 28px !important;
  color: var(--color-primary, #2563eb) !important;
  transition: all 0.3s ease !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.solution-card:hover .solution-icon {
  transform: scale(1.1) !important;
  background: linear-gradient(135deg, rgba(25, 118, 210, 0.2) 0%, rgba(25, 118, 210, 0.3) 100%) !important;
}

.solution-card:hover .solution-icon i {
  color: var(--color-primary-dark, #1d4ed8) !important;
}

/* Solution Title - REDUCED SPACING */
.solution-title {
  font-size: 1.3rem !important;
  font-weight: 600 !important;
  margin-bottom: 0.375rem !important; /* Reduced from 0.5rem to 0.375rem for tighter spacing */
  color: var(--color-text, #0f172a) !important;
  text-align: center !important;
  transition: color 0.3s ease !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 2 !important;
}

.solution-card:hover .solution-title {
  color: var(--color-primary, #2563eb) !important;
}

/* Solution Description - REDUCED SPACING */
.solution-description {
  font-size: 0.9rem !important;
  color: var(--color-text-light, #334155) !important;
  line-height: 1.4 !important;
  margin-bottom: 0.75rem !important; /* Reduced from 1rem to 0.75rem for tighter spacing */
  text-align: center !important;
  flex-grow: 1 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 2 !important;
}

/* Solution Benefits */
.solution-benefits {
  list-style: none !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 2 !important;
}

.solution-benefits li {
  position: relative !important;
  padding: 0.375rem 0 !important; /* Reduced from 0.5rem to 0.375rem for tighter spacing */
  margin-bottom: 0.25rem !important; /* Reduced from 0.3rem to 0.25rem */
  border-top: 1px solid rgba(0, 0, 0, 0.05) !important;
  display: flex !important;
  align-items: flex-start !important;
  transition: all 0.3s ease !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.solution-benefits li:first-child {
  border-top: none !important;
}

.solution-benefits li:last-child {
  margin-bottom: 0 !important;
}

.solution-card:hover .solution-benefits li {
  transform: translateX(3px) !important;
}

.check-icon {
  color: var(--color-primary, #2563eb) !important;
  font-weight: bold !important;
  margin-right: 0.75rem !important;
  flex-shrink: 0 !important;
  font-size: 1rem !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 20px !important;
  height: 20px !important;
  background-color: rgba(25, 118, 210, 0.1) !important;
  border-radius: 50% !important;
  transition: all 0.3s ease !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 3 !important;
}

.solution-card:hover .check-icon {
  background-color: var(--color-primary, #2563eb) !important;
  color: white !important;
  transform: scale(1.1) !important;
}

.benefit-text {
  font-size: 0.85rem !important;
  line-height: 1.4 !important;
  color: var(--color-text, #0f172a) !important;
  transition: color 0.3s ease !important;
  display: inline !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 3 !important;
}

.solution-card:hover .benefit-text {
  color: var(--color-text, #0f172a) !important;
  font-weight: 500 !important;
}

/* Dark Theme Styles */
.dark-theme .solution-card {
  background-color: rgba(30, 30, 30, 0.7) !important;
  border-color: rgba(100, 181, 246, 0.15) !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2) !important;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.dark-theme .solution-card:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(100, 181, 246, 0.2) !important;
  border-color: var(--color-primary-light, #60a5fa) !important;
}

.dark-theme .solution-card::after {
  background: linear-gradient(90deg, var(--color-primary-light, #60a5fa) 0%, var(--color-accent-light, #a78bfa) 100%) !important;
  box-shadow: 0 0 10px rgba(100, 181, 246, 0.3) !important;
}

.dark-theme .solution-icon {
  background: linear-gradient(135deg, rgba(100, 181, 246, 0.15) 0%, rgba(100, 181, 246, 0.25) 100%) !important;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.dark-theme .solution-icon i {
  color: var(--color-primary-light, #60a5fa) !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.dark-theme .solution-card:hover .solution-icon {
  background: linear-gradient(135deg, rgba(100, 181, 246, 0.25) 0%, rgba(100, 181, 246, 0.35) 100%) !important;
  box-shadow: 0 0 15px rgba(100, 181, 246, 0.3) !important;
}

.dark-theme .solution-title {
  color: rgba(255, 255, 255, 0.9) !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.dark-theme .solution-card:hover .solution-title {
  color: var(--color-primary-light, #60a5fa) !important;
  text-shadow: 0 0 10px rgba(100, 181, 246, 0.3) !important;
}

.dark-theme .solution-benefits {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.dark-theme .solution-benefits li {
  border-top: 1px solid rgba(255, 255, 255, 0.05) !important;
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.dark-theme .check-icon {
  color: var(--color-primary-light, #60a5fa) !important;
  background-color: rgba(100, 181, 246, 0.15) !important;
  display: inline-flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.dark-theme .solution-card:hover .check-icon {
  background-color: var(--color-primary-light, #60a5fa) !important;
  color: #121212 !important;
  box-shadow: 0 0 10px rgba(100, 181, 246, 0.3) !important;
}

/* Ensure no duplicate checkmarks in dark mode */
.dark-theme .solution-benefits li::before {
  content: none !important;
}

.dark-theme .solution-description {
  color: rgba(255, 255, 255, 0.7) !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.dark-theme .benefit-text {
  color: rgba(255, 255, 255, 0.8) !important;
  display: inline !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.dark-theme .solution-card:hover .benefit-text {
  color: rgba(255, 255, 255, 0.95) !important;
}

/* Responsive Adjustments - REDUCED MOBILE SPACING */
@media (max-width: 768px) {
  .solutions-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem; /* Reduced from 1rem to 0.75rem for tighter mobile layout */
  }

  .solution-card {
    padding: 0.75rem; /* Reduced from 1rem to 0.75rem */
    min-height: 220px; /* Reduced from 240px to 220px */
  }

  .solution-icon {
    width: 50px;
    height: 50px;
    margin-bottom: 0.5rem; /* Reduced from 0.75rem to 0.5rem */
  }

  .solution-icon i {
    font-size: 20px;
  }

  .solution-title {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .solutions-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem; /* Further reduced from 0.75rem to 0.5rem for very tight mobile layout */
  }

  .industry-solutions {
    padding: 2rem 1rem; /* Reduced from 2.5rem to 2rem */
  }

  .solution-card {
    padding: 0.5rem; /* Further reduced from 0.75rem to 0.5rem */
    min-height: 200px; /* Reduced from 220px to 200px */
  }
}
