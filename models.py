"""
Database models for Ziantrix Dynamic App
"""
from flask_sqlalchemy import SQLAlchemy
from flask_bcrypt import B<PERSON>rypt
from datetime import datetime
from sqlalchemy import Text, JSON
import uuid

db = SQLAlchemy()
bcrypt = Bcrypt()

# User model removed - authentication disabled

class FormSubmission(db.Model):
    """Model for storing all form submissions"""
    __tablename__ = 'form_submissions'

    id = db.Column(db.Integer, primary_key=True)
    # user_id removed - authentication disabled
    form_type = db.Column(db.String(50), nullable=False)  # 'contact', 'demo', 'newsletter', etc.
    name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), nullable=False)
    company = db.Column(db.String(200), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    message = db.Column(Text, nullable=True)
    inquiry_type = db.Column(db.String(100), nullable=True)
    source_page = db.Column(db.String(100), nullable=True)
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.String(500), nullable=True)
    additional_data = db.Column(JSON, nullable=True)  # For storing extra form fields
    status = db.Column(db.String(20), default='new', nullable=False)  # 'new', 'processed', 'responded'
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    processed_at = db.Column(db.DateTime, nullable=True)

    def to_dict(self):
        """Convert form submission to dictionary"""
        return {
            'id': self.id,
            # 'user_id' removed - authentication disabled
            'form_type': self.form_type,
            'name': self.name,
            'email': self.email,
            'company': self.company,
            'phone': self.phone,
            'message': self.message,
            'inquiry_type': self.inquiry_type,
            'source_page': self.source_page,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None
        }

    def __repr__(self):
        return f'<FormSubmission {self.form_type} from {self.email}>'

class ChatConversation(db.Model):
    """Model for storing chatbot conversations"""
    __tablename__ = 'chat_conversations'

    id = db.Column(db.Integer, primary_key=True)
    # user_id removed - authentication disabled
    session_id = db.Column(db.String(100), nullable=False, index=True)
    device_id = db.Column(db.String(100), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    messages = db.relationship('ChatMessage', backref='conversation', lazy=True, cascade='all, delete-orphan')

    def to_dict(self):
        """Convert conversation to dictionary"""
        return {
            'id': self.id,
            # 'user_id' removed - authentication disabled
            'session_id': self.session_id,
            'device_id': self.device_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'message_count': len(self.messages)
        }

    def __repr__(self):
        return f'<ChatConversation {self.session_id}>'

class ChatMessage(db.Model):
    """Model for storing individual chat messages"""
    __tablename__ = 'chat_messages'

    id = db.Column(db.Integer, primary_key=True)
    conversation_id = db.Column(db.Integer, db.ForeignKey('chat_conversations.id'), nullable=False)
    message_type = db.Column(db.String(20), nullable=False)  # 'user', 'bot'
    content = db.Column(Text, nullable=False)
    message_metadata = db.Column(JSON, nullable=True)  # For storing additional message data
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def to_dict(self):
        """Convert message to dictionary"""
        return {
            'id': self.id,
            'conversation_id': self.conversation_id,
            'message_type': self.message_type,
            'content': self.content,
            'metadata': self.message_metadata,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<ChatMessage {self.message_type}: {self.content[:50]}...>'

class Analytics(db.Model):
    """Model for storing analytics and metrics"""
    __tablename__ = 'analytics'

    id = db.Column(db.Integer, primary_key=True)
    event_type = db.Column(db.String(50), nullable=False)  # 'page_view', 'form_submit', 'chat_start', etc.
    event_data = db.Column(JSON, nullable=True)
    # user_id removed - authentication disabled
    session_id = db.Column(db.String(100), nullable=True)
    ip_address = db.Column(db.String(45), nullable=True)
    user_agent = db.Column(db.String(500), nullable=True)
    page_url = db.Column(db.String(500), nullable=True)
    referrer = db.Column(db.String(500), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)

    def to_dict(self):
        """Convert analytics record to dictionary"""
        return {
            'id': self.id,
            'event_type': self.event_type,
            'event_data': self.event_data,
            # 'user_id' removed - authentication disabled
            'session_id': self.session_id,
            'page_url': self.page_url,
            'referrer': self.referrer,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

    def __repr__(self):
        return f'<Analytics {self.event_type}>'

# Database utility functions
def init_db(app):
    """Initialize database with app"""
    db.init_app(app)
    bcrypt.init_app(app)

def create_tables(app):
    """Create all database tables"""
    with app.app_context():
        db.create_all()
        print("✅ Database tables created successfully")

def drop_tables(app):
    """Drop all database tables"""
    with app.app_context():
        db.drop_all()
        print("⚠️ All database tables dropped")
