{% extends "base.html" %}

{% block title %}Server Error - Ziantrix{% endblock %}

{% block meta_description %}Something went wrong on our end. Our team has been notified and we're working to fix the issue.{% endblock %}

{% block extra_head %}
<style>
    .error-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 70vh;
        text-align: center;
        padding: var(--spacing-lg);
    }

    .error-code {
        font-size: 8rem;
        font-weight: bold;
        color: var(--color-error);
        margin: 0;
        line-height: 1;
        animation: shake 1s ease-in-out;
    }

    .error-message {
        font-size: 2rem;
        margin: 1rem 0 2rem;
    }

    .error-description {
        max-width: 600px;
        margin-bottom: var(--spacing-lg);
        color: var(--color-text-light);
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <h1 class="error-code">500</h1>
    <h2 class="error-message">Server Error</h2>
    <p class="error-description">
        Something went wrong on our end. Our team has been notified and we're working to fix the issue.
    </p>
    <a href="/" class="cta-btn primary">Return to Home</a>
</div>
{% endblock %}
