/**
 * Service Card Button Display Fix
 * This script ensures the service card buttons are always visible
 */

document.addEventListener('DOMContentLoaded', function() {
    // Apply fixes immediately and after delays to ensure they take effect
    fixServiceCardButtons();
    setTimeout(fixServiceCardButtons, 100);
    setTimeout(fixServiceCardButtons, 500);
    setTimeout(fixServiceCardButtons, 1000);
    
    // Also apply fixes when window is resized
    window.addEventListener('resize', fixServiceCardButtons);
});

function fixServiceCardButtons() {
    // Get all service cards
    const serviceCards = document.querySelectorAll('.service-card');
    
    // Process each service card
    serviceCards.forEach(function(card) {
        // Ensure card has proper height and positioning
        card.style.minHeight = '500px';
        card.style.height = 'auto';
        card.style.display = 'flex';
        card.style.flexDirection = 'column';
        card.style.position = 'relative';
        card.style.paddingBottom = '100px';
        card.style.boxSizing = 'border-box';
        
        // Get the card body
        const cardBody = card.querySelector('.service-card-body');
        if (cardBody) {
            cardBody.style.flex = '1';
            cardBody.style.paddingBottom = '80px';
            cardBody.style.position = 'relative';
            cardBody.style.zIndex = '1';
        }
        
        // Get the card footer
        const cardFooter = card.querySelector('.service-card-footer');
        if (cardFooter) {
            // Force display of footer
            cardFooter.style.display = 'block';
            cardFooter.style.visibility = 'visible';
            cardFooter.style.opacity = '1';
            
            // Position footer at the bottom of the card
            cardFooter.style.position = 'absolute';
            cardFooter.style.bottom = '24px';
            cardFooter.style.left = '0';
            cardFooter.style.right = '0';
            cardFooter.style.width = '100%';
            cardFooter.style.padding = '0 24px 24px';
            cardFooter.style.boxSizing = 'border-box';
            cardFooter.style.zIndex = '10';
            cardFooter.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--color-card-bg');
            
            // Add a background element if it doesn't exist
            let background = cardFooter.querySelector('.footer-background');
            if (!background) {
                background = document.createElement('div');
                background.className = 'footer-background';
                cardFooter.insertBefore(background, cardFooter.firstChild);
            }
            
            // Style the background element
            background.style.position = 'absolute';
            background.style.top = '-20px';
            background.style.left = '0';
            background.style.right = '0';
            background.style.height = '20px';
            background.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--color-card-bg');
            background.style.zIndex = '-1';
            
            // Get the button
            const button = cardFooter.querySelector('.service-cta-button');
            if (button) {
                // Force display of button
                button.style.display = 'block';
                button.style.visibility = 'visible';
                button.style.opacity = '1';
                
                // Style the button
                button.style.width = '100%';
                button.style.minWidth = '260px';
                button.style.padding = '14px 20px';
                button.style.background = 'linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%)';
                button.style.color = 'white';
                button.style.fontWeight = '600';
                button.style.fontSize = '1rem';
                button.style.textAlign = 'center';
                button.style.borderRadius = '8px';
                button.style.transition = 'all 0.3s ease';
                button.style.textDecoration = 'none';
                button.style.border = 'none';
                button.style.cursor = 'pointer';
                button.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
                button.style.position = 'relative';
                button.style.zIndex = '20';
                button.style.whiteSpace = 'normal';
                button.style.overflow = 'visible';
                button.style.textOverflow = 'clip';
                button.style.boxSizing = 'border-box';
                
                // Add hover effect
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.boxShadow = '0 6px 15px rgba(0, 0, 0, 0.15)';
                });
                
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
                });
            }
        }
    });
    
    // Check if we're in dark mode
    const isDarkMode = document.body.classList.contains('dark-theme');
    if (isDarkMode) {
        // Update background colors for dark mode
        document.querySelectorAll('.service-card-footer, .footer-background').forEach(function(element) {
            element.style.backgroundColor = getComputedStyle(document.documentElement).getPropertyValue('--color-card-bg-dark');
        });
    }
}
