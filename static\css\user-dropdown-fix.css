/**
 * NUCLEAR DROPDOWN FIX - OVERRIDE EVERYTHING
 */

/* FORCE DROPDOWN TO WORK */
.user-menu {
    position: relative !important;
    display: inline-block !important;
}

.user-menu .user-dropdown {
    position: absolute !important;
    top: calc(100% + 2px) !important;
    right: 0 !important;
    width: 140px !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    z-index: 99999 !important;
    display: none !important;
    padding: 6px 0 !important;
    margin: 0 !important;
    overflow: hidden !important;
}

.dark-theme .user-menu .user-dropdown {
    background: #2d3748 !important;
    border-color: #4a5568 !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
}

.user-menu .user-dropdown a {
    display: block !important;
    padding: 8px 16px !important;
    color: #2d3748 !important;
    text-decoration: none !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    white-space: nowrap !important;
    border: none !important;
    background: none !important;
    transition: background-color 0.15s ease !important;
}

.dark-theme .user-menu .user-dropdown a {
    color: #e2e8f0 !important;
}

.user-menu .user-dropdown a:hover {
    background: #f7fafc !important;
    color: #2d3748 !important;
}

.dark-theme .user-menu .user-dropdown a:hover {
    background: #4a5568 !important;
    color: #e2e8f0 !important;
}

/* SIMPLE HOVER - NO JAVASCRIPT INTERFERENCE */
.user-menu:hover .user-dropdown {
    display: block !important;
}

.user-dropdown:hover {
    display: block !important;
}

/* FORCE BUTTON HEIGHTS TO BE EXACTLY THE SAME */
.hero-cta a, .cta-buttons a, .hero-section a, .btn, .cta-btn {
    height: 56px !important;
    min-height: 56px !important;
    max-height: 56px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 20px !important;
    box-sizing: border-box !important;
    font-size: 15px !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    text-align: center !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* Primary button - Blue */
.hero-cta .btn-primary, .cta-buttons .btn-primary, .btn.btn-primary, .cta-btn.primary {
    background: #3b82f6 !important;
    background-color: #3b82f6 !important;
    background-image: none !important;
    color: #ffffff !important;
}

/* Secondary button - Purple */
.hero-cta .btn-secondary, .cta-buttons .btn-secondary, .btn.btn-secondary, .cta-btn.secondary {
    background: #8b5cf6 !important;
    background-color: #8b5cf6 !important;
    background-image: none !important;
    color: #ffffff !important;
}

/* Hover effects */
.hero-cta .btn-primary:hover, .cta-buttons .btn-primary:hover, .btn.btn-primary:hover, .cta-btn.primary:hover {
    background: #2563eb !important;
    background-color: #2563eb !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.hero-cta .btn-secondary:hover, .cta-buttons .btn-secondary:hover, .btn.btn-secondary:hover, .cta-btn.secondary:hover {
    background: #7c3aed !important;
    background-color: #7c3aed !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3) !important;
}

/* Container alignment */
.hero-cta, .cta-buttons {
    display: flex !important;
    justify-content: center !important;
    align-items: stretch !important;
    gap: 16px !important;
    flex-wrap: nowrap !important;
    max-width: 800px !important;
    margin: 0 auto !important;
}
