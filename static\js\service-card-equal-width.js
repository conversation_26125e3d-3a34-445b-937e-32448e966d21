/**
 * Service Card Equal Width
 * This script ensures all service cards and their buttons have equal width
 */

document.addEventListener('DOMContentLoaded', function() {
    // Apply fixes immediately and after delays to ensure they take effect
    equalizeServiceCards();
    setTimeout(equalizeServiceCards, 100);
    setTimeout(equalizeServiceCards, 500);
    setTimeout(equalizeServiceCards, 1000);
});

function equalizeServiceCards() {
    // Get all service cards
    const serviceCards = document.querySelectorAll('.service-card');

    // If there are no service cards, exit
    if (serviceCards.length === 0) return;

    // Get the services grid
    const servicesGrid = document.querySelector('.services-grid');
    if (!servicesGrid) return;

    // Set the grid to have equal columns
    servicesGrid.style.display = 'grid';
    servicesGrid.style.gridTemplateColumns = 'repeat(3, minmax(0, 1fr))';
    servicesGrid.style.gap = '30px';
    servicesGrid.style.width = '100%';
    servicesGrid.style.maxWidth = '1200px';
    servicesGrid.style.margin = '0 auto';

    // Make all cards the same width
    let maxWidth = 0;

    // First pass: find the widest card
    serviceCards.forEach(function(card) {
        // Reset any inline width to get natural width
        card.style.width = '';
        const cardWidth = card.offsetWidth;
        if (cardWidth > maxWidth) {
            maxWidth = cardWidth;
        }
    });

    // Second pass: set all cards to the same width and height
    if (maxWidth > 0) {
        const fixedHeight = 520; // Fixed height for all cards

        serviceCards.forEach(function(card) {
            card.style.width = '100%';
            card.style.maxWidth = 'none';
            card.style.minWidth = '0';
            card.style.boxSizing = 'border-box';
            card.style.flex = '1 1 0';
            card.style.height = fixedHeight + 'px';
            card.style.minHeight = fixedHeight + 'px';
            card.style.maxHeight = fixedHeight + 'px';
            card.style.display = 'flex';
            card.style.flexDirection = 'column';
            card.style.position = 'relative';
            card.style.paddingBottom = '120px';

            // Also set the footer and button to the same width
            const footer = card.querySelector('.service-card-footer');
            if (footer) {
                footer.style.width = '100%';
                footer.style.boxSizing = 'border-box';
                footer.style.padding = '0 24px 24px';
                footer.style.position = 'absolute';
                footer.style.bottom = '30px';
                footer.style.left = '0';
                footer.style.right = '0';
            }

            const button = card.querySelector('.service-cta-button');
            if (button) {
                button.style.width = '100%';
                button.style.maxWidth = 'none';
                button.style.minWidth = '0';
                button.style.boxSizing = 'border-box';
                button.style.display = 'flex';
                button.style.justifyContent = 'center';
                button.style.alignItems = 'center';
            }
        });
    }

    // Ensure all buttons have the same width
    const buttons = document.querySelectorAll('.service-cta-button');
    let maxButtonWidth = 0;

    // First pass: find the widest button
    buttons.forEach(function(button) {
        // Reset any inline width to get natural width
        button.style.width = '';
        const buttonWidth = button.offsetWidth;
        if (buttonWidth > maxButtonWidth) {
            maxButtonWidth = buttonWidth;
        }
    });

    // Second pass: set all buttons to the same width
    if (maxButtonWidth > 0) {
        buttons.forEach(function(button) {
            button.style.width = '100%';
            button.style.maxWidth = '100%';
            button.style.minWidth = '0';
            button.style.boxSizing = 'border-box';
            button.style.display = 'flex';
            button.style.justifyContent = 'center';
            button.style.alignItems = 'center';
            button.style.padding = '14px 20px';
            button.style.textAlign = 'center';
            button.style.whiteSpace = 'normal';
            button.style.overflow = 'visible';
            button.style.textOverflow = 'clip';
        });
    }

    // Specifically target the third card and its button
    const thirdCard = document.querySelector('.services-grid .service-card:nth-child(3)');
    if (thirdCard) {
        thirdCard.style.width = '100%';
        thirdCard.style.maxWidth = 'none';
        thirdCard.style.minWidth = '0';
        thirdCard.style.flex = '1 1 0';

        const thirdButton = thirdCard.querySelector('.service-cta-button');
        if (thirdButton) {
            thirdButton.style.width = '100%';
            thirdButton.style.maxWidth = '100%';
            thirdButton.style.minWidth = '0';
            thirdButton.style.boxSizing = 'border-box';
            thirdButton.style.display = 'flex';
            thirdButton.style.justifyContent = 'center';
            thirdButton.style.alignItems = 'center';
            thirdButton.style.padding = '14px 20px';
            thirdButton.style.textAlign = 'center';
            thirdButton.style.whiteSpace = 'normal';
            thirdButton.style.overflow = 'visible';
            thirdButton.style.textOverflow = 'clip';
        }
    }
}
