/**
 * Service Card Equal Width
 * This CSS ensures all service cards and their buttons have equal width
 */

/* Make all service cards the same width */
.services-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 30px !important;
}

/* Ensure all service cards have the same width */
.service-card {
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
}

/* Ensure all service card footers have the same width */
.service-card-footer {
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
}

/* Ensure all service CTA buttons have the same width */
.service-cta-button {
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .services-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

@media (max-width: 992px) {
  .services-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .services-grid {
    grid-template-columns: 1fr !important;
  }
}
