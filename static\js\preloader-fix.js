/**
 * Preloader Fix
 * Ensures the preloader is properly removed when the page loads
 */

// Execute immediately to remove preloader if it's stuck
(function() {
    // Function to remove preloader
    function removePreloader() {
        const preloader = document.querySelector('.preloader');
        if (preloader) {
            preloader.classList.add('fade-out');
            
            // Force remove preloader after animation completes
            setTimeout(() => {
                preloader.style.display = 'none';
                document.body.style.overflow = 'auto'; // Ensure body scrolling is enabled
            }, 500);
        }
    }
    
    // Try to remove preloader immediately
    removePreloader();
    
    // Also try when DOM is ready
    document.addEventListener('DOMContentLoaded', removePreloader);
    
    // Final fallback - remove after a timeout
    setTimeout(remove<PERSON><PERSON>oa<PERSON>, 2000);
    
    // Also remove on window load event
    window.addEventListener('load', removePreloader);
})();
