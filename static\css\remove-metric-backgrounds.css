/**
 * Remove Metric Backgrounds
 * This file completely removes all background colors from feature metrics
 * in both light and dark mode with the highest specificity possible
 */

/* Target the feature metrics directly with !important */
.feature-card .feature-metric,
.feature-card div.feature-metric,
.feature-card > .feature-metric,
div.feature-card div.feature-metric,
.feature-card .feature-metric[class],
.feature-card .feature-metric[style] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

.feature-card .feature-metric-label,
.feature-card p.feature-metric-label,
.feature-card > .feature-metric-label,
div.feature-card p.feature-metric-label,
.feature-card .feature-metric-label[class],
.feature-card .feature-metric-label[style] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

.feature-card .feature-proof-point,
.feature-card p.feature-proof-point,
.feature-card > .feature-proof-point,
div.feature-card p.feature-proof-point,
.feature-card .feature-proof-point[class],
.feature-card .feature-proof-point[style] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

/* Target any container that might have a background */
.feature-card .metric-wrapper,
.feature-card div.metric-wrapper,
.feature-card > div,
div.feature-card div.metric-wrapper,
.feature-card [class*="metric"],
.feature-card *[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

/* Override any inline styles */
.feature-card [style*="background"],
.feature-card [style*="background-color"] {
  background: transparent !important;
  background-color: transparent !important;
}

/* Ensure dark mode is also covered */
.dark-theme .feature-card .feature-metric,
.dark-theme .feature-card .feature-metric-label,
.dark-theme .feature-card .feature-proof-point,
.dark-theme .feature-card .metric-wrapper,
.dark-theme .feature-card [class*="metric"],
.dark-theme .feature-card *[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Target the specific elements shown in the screenshot */
.feature-card:nth-child(2) .feature-metric,
.feature-card:nth-child(2) .feature-metric-label,
.feature-card:nth-child(2) .feature-proof-point,
.feature-card:nth-child(2) .metric-wrapper,
.feature-card:nth-child(2) [class*="metric"],
.feature-card:nth-child(2) *[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Ensure all feature cards have transparent backgrounds */
.feature-card {
  background-color: transparent !important;
}

.feature-card:hover {
  background-color: transparent !important;
}

.dark-theme .feature-card {
  background-color: transparent !important;
}

.dark-theme .feature-card:hover {
  background-color: transparent !important;
}
