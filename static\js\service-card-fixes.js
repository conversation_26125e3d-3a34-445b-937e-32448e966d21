/**
 * Service Card Fixes
 * This script ensures proper alignment of service card elements
 */

document.addEventListener('DOMContentLoaded', function() {
    // Apply fixes immediately and after a short delay to ensure they take effect
    applyServiceCardFixes();
    setTimeout(applyServiceCardFixes, 100);
    setTimeout(applyServiceCardFixes, 500);
});

function applyServiceCardFixes() {
    // Fix service feature icons and text alignment
    const featureItems = document.querySelectorAll('.service-features-list li');

    featureItems.forEach(function(item) {
        // Ensure flex display with row direction
        item.style.display = 'flex';
        item.style.flexDirection = 'row';
        item.style.alignItems = 'center'; // Changed from flex-start to center for better alignment
        item.style.justifyContent = 'flex-start';
        item.style.textAlign = 'left';
        item.style.width = '100%';
        item.style.paddingLeft = '24px';
        item.style.marginBottom = '20px'; // Increased from 12px to 20px for more spacing

        // Fix icon alignment
        const icon = item.querySelector('.feature-icon');
        if (icon) {
            icon.style.display = 'inline-flex';
            icon.style.alignItems = 'center';
            icon.style.justifyContent = 'center';
            icon.style.marginRight = '16px';
            icon.style.width = '28px';
            icon.style.height = '28px';
            icon.style.minWidth = '28px';
            icon.style.flexShrink = '0';
            icon.style.background = 'none';
            icon.style.backgroundColor = 'transparent';

            // Ensure icon has proper size for its content
            const iconInner = icon.querySelector('i');
            if (iconInner) {
                iconInner.style.fontSize = '16px';
                iconInner.style.display = 'block';
                iconInner.style.lineHeight = '1';
            }
        }

        // Fix text alignment
        const text = item.querySelector('.feature-text');
        if (text) {
            text.style.display = 'inline-block';
            text.style.flex = '1';
            text.style.textAlign = 'left';
            text.style.paddingTop = '2px';
            text.style.paddingBottom = '2px';
            text.style.lineHeight = '1.5';
        }
    });

    // Make all service cards equal height
    const serviceCards = document.querySelectorAll('.service-card');
    const fixedHeight = 520; // Increased fixed height for all cards

    // Set all cards to the same fixed height
    serviceCards.forEach(function(card) {
        // Set position relative for absolute positioning of footer
        card.style.position = 'relative';
        card.style.minHeight = fixedHeight + 'px';
        card.style.maxHeight = fixedHeight + 'px';
        card.style.height = fixedHeight + 'px';
        card.style.boxSizing = 'border-box';
        card.style.paddingBottom = '120px'; // Increased padding at bottom
    });

    // Fix service card body padding
    const serviceCardBodies = document.querySelectorAll('.service-card-body');
    serviceCardBodies.forEach(function(body) {
        body.style.padding = '0';
        body.style.paddingLeft = '0';
        body.style.paddingRight = '0';
        body.style.paddingTop = '16px'; // Increased from 8px to 16px
        body.style.paddingBottom = '100px'; // Increased from 70px to 100px
        body.style.margin = '0';
        body.style.width = '100%';
        body.style.boxSizing = 'border-box';

        // Ensure the feature list has proper padding
        const featureList = body.querySelector('.service-features-list');
        if (featureList) {
            featureList.style.paddingLeft = '0';
            featureList.style.paddingRight = '0';
            featureList.style.width = '100%';
            featureList.style.margin = '0';
        }
    });

    // Fix service card footer positioning
    const serviceCardFooters = document.querySelectorAll('.service-card-footer');
    serviceCardFooters.forEach(function(footer) {
        footer.style.position = 'absolute';
        footer.style.bottom = '30px'; // Increased from 24px to 30px
        footer.style.left = '0';
        footer.style.right = '0';
        footer.style.width = '100%';
        footer.style.boxSizing = 'border-box';
        footer.style.borderTop = 'none';
        footer.style.background = 'transparent';
        footer.style.boxShadow = 'none';
        footer.style.marginTop = '0';
        footer.style.padding = '0 24px 24px';
    });

    // Fix service CTA buttons
    const serviceCTAButtons = document.querySelectorAll('.service-cta-button');

    // First find the widest button
    let maxButtonWidth = 0;
    serviceCTAButtons.forEach(function(button) {
        // Reset any inline width to get natural width
        button.style.width = '';
        const buttonWidth = button.offsetWidth;
        if (buttonWidth > maxButtonWidth) {
            maxButtonWidth = buttonWidth;
        }
    });

    // Now set all buttons to the same width
    serviceCTAButtons.forEach(function(button) {
        button.style.display = 'flex';
        button.style.minHeight = '48px';
        button.style.lineHeight = '1.2';
        button.style.boxSizing = 'border-box';
        button.style.justifyContent = 'center';
        button.style.alignItems = 'center';
        button.style.padding = '14px 20px';
        button.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
        button.style.borderTop = 'none';
        button.style.textAlign = 'center';
        button.style.whiteSpace = 'normal';
        button.style.overflow = 'visible';
        button.style.textOverflow = 'clip';

        // Set fixed width for all buttons
        if (maxButtonWidth > 0) {
            button.style.width = '100%';
            button.style.minWidth = '0';
            button.style.maxWidth = '100%';
        }

        // Ensure text is properly centered and visible
        const buttonText = button.textContent;
        if (buttonText) {
            // Remove any ellipsis that might be in the text
            button.textContent = buttonText.trim().replace(/\s*\.{3,}\s*$/, '');
        }
    });

    // Specifically target the third service card (AI Agents) to ensure consistent spacing
    const thirdCard = document.querySelector('.services-grid .service-card:nth-child(3)');
    if (thirdCard) {
        // Force the third card to have the same width and height as the others
        thirdCard.style.width = '100%';
        thirdCard.style.maxWidth = 'none';
        thirdCard.style.minWidth = '0';
        thirdCard.style.flex = '1 1 0';
        thirdCard.style.height = fixedHeight + 'px';
        thirdCard.style.minHeight = fixedHeight + 'px';
        thirdCard.style.maxHeight = fixedHeight + 'px';

        const featureItems = thirdCard.querySelectorAll('.service-features-list li');
        featureItems.forEach(function(item) {
            item.style.marginBottom = '20px'; // Increased from 12px to 20px
        });

        // Ensure the last item doesn't have bottom margin
        const lastItem = thirdCard.querySelector('.service-features-list li:last-child');
        if (lastItem) {
            lastItem.style.marginBottom = '0';
        }

        // Ensure the card body has proper padding
        const cardBody = thirdCard.querySelector('.service-card-body');
        if (cardBody) {
            cardBody.style.paddingTop = '16px';
            cardBody.style.paddingBottom = '100px';
        }

        // Specifically target the third card's button to ensure it has the same width
        const thirdButton = thirdCard.querySelector('.service-cta-button');
        if (thirdButton) {
            thirdButton.style.width = '100%';
            thirdButton.style.minWidth = '0';
            thirdButton.style.maxWidth = '100%';
            thirdButton.style.display = 'flex';
            thirdButton.style.justifyContent = 'center';
            thirdButton.style.alignItems = 'center';
            thirdButton.style.boxSizing = 'border-box';
            thirdButton.style.padding = '14px 20px';
            thirdButton.style.textAlign = 'center';
            thirdButton.style.whiteSpace = 'normal';
            thirdButton.style.overflow = 'visible';
            thirdButton.style.textOverflow = 'clip';
        }

        // Ensure the third card's footer has proper width and padding
        const thirdFooter = thirdCard.querySelector('.service-card-footer');
        if (thirdFooter) {
            thirdFooter.style.width = '100%';
            thirdFooter.style.padding = '0 24px 24px';
            thirdFooter.style.boxSizing = 'border-box';
        }
    }
});
