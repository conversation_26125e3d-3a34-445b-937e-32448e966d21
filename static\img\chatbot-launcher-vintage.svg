<?xml version="1.0" encoding="UTF-8"?>
<svg width="100px" height="100px" viewBox="0 0 100 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Vintage Typewriter Chatbot Launcher</title>
    <defs>
        <!-- Vintage paper texture gradient -->
        <linearGradient id="paperGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#F5F0E1" />
            <stop offset="50%" stop-color="#E8DFC6" />
            <stop offset="100%" stop-color="#D9CEB2" />
        </linearGradient>

        <!-- Vintage metal gradient -->
        <linearGradient id="metalGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#5A5A5A" />
            <stop offset="50%" stop-color="#3D3D3D" />
            <stop offset="100%" stop-color="#2A2A2A" />
        </linearGradient>

        <!-- Vintage red accent -->
        <linearGradient id="redAccentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#A52A2A" />
            <stop offset="50%" stop-color="#8B0000" />
            <stop offset="100%" stop-color="#800000" />
        </linearGradient>

        <!-- Subtle shadow effect -->
        <filter id="vintageShadow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="2" result="blur" />
            <feOffset in="blur" dx="2" dy="2" result="offsetBlur" />
            <feComponentTransfer in="offsetBlur" result="darkenedBlur">
                <feFuncA type="linear" slope="0.4" />
            </feComponentTransfer>
            <feMerge>
                <feMergeNode in="darkenedBlur" />
                <feMergeNode in="SourceGraphic" />
            </feMerge>
        </filter>

        <!-- Vintage paper texture -->
        <pattern id="paperTexture" patternUnits="userSpaceOnUse" width="100" height="100">
            <rect width="100" height="100" fill="url(#paperGradient)" />
            <rect width="100" height="100" fill="#000000" opacity="0.03" />
            <filter id="noise">
                <feTurbulence type="fractalNoise" baseFrequency="0.8" numOctaves="3" stitchTiles="stitch" />
                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0" />
            </filter>
            <rect width="100" height="100" filter="url(#noise)" opacity="0.3" />
        </pattern>
    </defs>

    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" filter="url(#vintageShadow)">
        <!-- Main Circle with vintage paper texture -->
        <circle cx="50" cy="50" r="45" fill="url(#paperTexture)" />

        <!-- Decorative border -->
        <circle cx="50" cy="50" r="45" stroke="#8B4513" stroke-width="2" fill="none" />

        <!-- Typewriter body -->
        <g transform="translate(20, 25)">
            <!-- Typewriter base -->
            <rect x="0" y="15" width="60" height="35" rx="5" fill="url(#metalGradient)" />

            <!-- Typewriter paper roller -->
            <rect x="5" y="10" width="50" height="10" rx="5" fill="#2A2A2A" />

            <!-- Paper coming out of typewriter -->
            <rect x="10" y="0" width="40" height="15" rx="2" fill="white" />

            <!-- Typewriter keys -->
            <g transform="translate(5, 30)">
                <!-- Row 1 -->
                <circle cx="5" cy="5" r="4" fill="#D9D9D9" stroke="#2A2A2A" stroke-width="1" />
                <circle cx="15" cy="5" r="4" fill="#D9D9D9" stroke="#2A2A2A" stroke-width="1" />
                <circle cx="25" cy="5" r="4" fill="#D9D9D9" stroke="#2A2A2A" stroke-width="1" />
                <circle cx="35" cy="5" r="4" fill="#D9D9D9" stroke="#2A2A2A" stroke-width="1" />
                <circle cx="45" cy="5" r="4" fill="#D9D9D9" stroke="#2A2A2A" stroke-width="1" />

                <!-- Row 2 -->
                <circle cx="10" cy="15" r="4" fill="#D9D9D9" stroke="#2A2A2A" stroke-width="1" />
                <circle cx="20" cy="15" r="4" fill="#D9D9D9" stroke="#2A2A2A" stroke-width="1" />
                <circle cx="30" cy="15" r="4" fill="#D9D9D9" stroke="#2A2A2A" stroke-width="1" />
                <circle cx="40" cy="15" r="4" fill="#D9D9D9" stroke="#2A2A2A" stroke-width="1" />
            </g>

            <!-- Typewriter text on paper -->
            <g transform="translate(15, 7)">
                <rect x="0" y="0" width="30" height="1" fill="#333333" />
                <rect x="0" y="3" width="25" height="1" fill="#333333" />
                <rect x="0" y="6" width="20" height="1" fill="#333333" />
            </g>

            <!-- Typewriter red/black ink ribbon -->
            <rect x="5" y="25" width="50" height="2" fill="url(#redAccentGradient)" />

            <!-- Typewriter return lever -->
            <path d="M60,20 L65,20 L65,30 L60,30" fill="url(#metalGradient)" stroke="#2A2A2A" stroke-width="1" />
            <circle cx="65" cy="25" r="3" fill="#D9D9D9" stroke="#2A2A2A" stroke-width="1" />
        </g>

        <!-- Vintage stamp/seal removed as requested -->

        <!-- Decorative corner elements -->
        <path d="M15,15 L25,15 L25,16 L16,16 L16,25 L15,25 Z" fill="#8B4513" />
        <path d="M75,15 L85,15 L85,25 L84,25 L84,16 L75,16 Z" fill="#8B4513" />
        <path d="M15,75 L15,85 L25,85 L25,84 L16,84 L16,75 Z" fill="#8B4513" />
        <path d="M75,84 L75,85 L85,85 L85,75 L84,75 L84,84 Z" fill="#8B4513" />
    </g>
</svg>
