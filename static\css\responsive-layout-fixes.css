/**
 * Responsive Layout Fixes
 * Specific fixes for layout issues and responsive improvements
 */

/* ===== HERO SECTION RESPONSIVE FIXES ===== */

.hero-section {
    width: 100%;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    position: relative;
    overflow: hidden;
}

.hero-content {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    z-index: 2;
}

.hero-title {
    font-size: clamp(2rem, 5vw, 3.5rem);
    line-height: 1.2;
    margin-bottom: 1.5rem;
    word-wrap: break-word;
    hyphens: auto;
}

.hero-subtitle {
    font-size: clamp(1rem, 3vw, 1.25rem);
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.hero-cta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-video {
    width: 100%;
    max-width: 800px;
    margin: 3rem auto 0;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* ===== NAVIGATION RESPONSIVE FIXES ===== */

.navbar {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--color-bg-primary, #ffffff);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.navbar-container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    position: relative;
}

.navbar-logo {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1001;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 0 auto;
    list-style: none;
    padding: 0;
}

.navbar-link {
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: var(--color-text-primary, #333);
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.navbar-link:hover {
    background-color: rgba(79, 70, 229, 0.1);
    color: var(--color-primary, #4f46e5);
}

/* ===== SECTION RESPONSIVE FIXES ===== */

section {
    width: 100%;
    padding: 4rem 1rem;
    position: relative;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.section-title {
    font-size: clamp(1.8rem, 4vw, 2.5rem);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.section-description {
    font-size: clamp(1rem, 2.5vw, 1.125rem);
    line-height: 1.6;
    color: var(--color-text-secondary, #666);
}

/* ===== GRID RESPONSIVE FIXES ===== */

.features-grid,
.services-grid,
.solutions-grid {
    display: grid;
    gap: 2rem;
    width: 100%;
    margin-top: 3rem;
}

/* Default: Mobile-first single column */
.features-grid,
.services-grid,
.solutions-grid {
    grid-template-columns: 1fr;
}

/* ===== CARD RESPONSIVE FIXES ===== */

.feature-card,
.service-card,
.solution-card {
    width: 100%;
    padding: 2rem;
    border-radius: 12px;
    background: var(--color-bg-primary, #ffffff);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card:hover,
.service-card:hover,
.solution-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.feature-icon,
.service-icon,
.solution-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    display: block;
}

.feature-title,
.service-title,
.solution-title {
    font-size: clamp(1.25rem, 3vw, 1.5rem);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.feature-description,
.service-description,
.solution-description {
    font-size: clamp(0.9rem, 2vw, 1rem);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    color: var(--color-text-secondary, #666);
}

/* ===== BUTTON RESPONSIVE FIXES ===== */

.btn,
.cta-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    min-height: 44px;
    font-size: clamp(0.875rem, 2vw, 1rem);
    font-weight: 600;
    text-decoration: none;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    text-align: center;
    line-height: 1.2;
}

.btn-primary {
    background: var(--color-primary, #4f46e5);
    color: white;
}

.btn-secondary {
    background: var(--color-secondary, #8b5cf6);
    color: white;
}

.btn:hover,
.cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* ===== FOOTER RESPONSIVE FIXES ===== */

footer {
    width: 100%;
    padding: 3rem 1rem 2rem;
    background: var(--color-bg-secondary, #f8f9fa);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    justify-content: space-between;
}

.footer-section {
    flex: 1;
    min-width: 250px;
}

.footer-bottom {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Small tablets and large phones (600px and up) */
@media (min-width: 600px) {
    .hero-cta {
        flex-direction: row;
        flex-wrap: nowrap;
    }
    
    .hero-cta .btn {
        flex: 1;
        max-width: 250px;
    }
    
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablets (768px and up) */
@media (min-width: 768px) {
    .navbar-container {
        padding: 0 2rem;
    }
    
    .container {
        padding: 0 2rem;
    }
    
    section {
        padding: 5rem 2rem;
    }
    
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .solutions-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .mobile-menu-toggle {
        display: none !important;
    }
    
    .nav-links {
        display: flex !important;
        position: static !important;
        width: auto !important;
        height: auto !important;
        background: transparent !important;
        flex-direction: row !important;
        padding: 0 !important;
        box-shadow: none !important;
    }
}

/* Desktop (1024px and up) */
@media (min-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .solutions-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Large desktop (1280px and up) */
@media (min-width: 1280px) {
    .features-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .solutions-grid {
        grid-template-columns: repeat(5, 1fr);
    }
}

/* ===== MOBILE-SPECIFIC OVERRIDES ===== */

@media (max-width: 767px) {
    .hero-section {
        min-height: 80vh;
        padding: 6rem 1rem 4rem;
    }
    
    .navbar {
        height: 60px;
    }
    
    .mobile-menu-toggle {
        display: flex !important;
    }
    
    .nav-links {
        display: none;
        position: fixed;
        top: 60px;
        left: 0;
        width: 100%;
        background: var(--color-bg-primary, #ffffff);
        flex-direction: column;
        padding: 1rem 0;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        z-index: 999;
    }
    
    .nav-links.mobile-active {
        display: flex !important;
    }
    
    .navbar-link {
        width: 100%;
        text-align: center;
        padding: 1rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .hero-cta {
        flex-direction: column;
        gap: 1rem;
    }
    
    .hero-cta .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-section {
        width: 100%;
        margin-bottom: 2rem;
    }
}
