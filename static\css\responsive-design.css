/**
 * Comprehensive Responsive Design CSS
 * Mobile-first approach with proper breakpoints
 * Ensures full responsiveness across all devices
 */

/* ===== RESPONSIVE FOUNDATION ===== */

/* Ensure all elements are responsive by default */
* {
    box-sizing: border-box;
}

html {
    font-size: 16px; /* Base font size */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    width: 100%;
    min-height: 100vh;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
}

/* ===== RESPONSIVE CONTAINERS ===== */

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* ===== RESPONSIVE GRID SYSTEM ===== */

.grid {
    display: grid;
    gap: 1rem;
    width: 100%;
}

.grid-1 { grid-template-columns: 1fr; }
.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* Auto-fit responsive grids */
.grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* ===== RESPONSIVE FLEXBOX UTILITIES ===== */

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.flex-wrap {
    flex-wrap: wrap;
}

.flex-center {
    justify-content: center;
    align-items: center;
}

.flex-between {
    justify-content: space-between;
    align-items: center;
}

.flex-around {
    justify-content: space-around;
    align-items: center;
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */

h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: clamp(1.8rem, 4vw, 3.5rem);
}

h2 {
    font-size: clamp(1.5rem, 3.5vw, 2.5rem);
}

h3 {
    font-size: clamp(1.3rem, 3vw, 2rem);
}

h4 {
    font-size: clamp(1.1rem, 2.5vw, 1.5rem);
}

h5 {
    font-size: clamp(1rem, 2vw, 1.25rem);
}

h6 {
    font-size: clamp(0.9rem, 1.5vw, 1.1rem);
}

p {
    font-size: clamp(0.9rem, 2vw, 1rem);
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* ===== RESPONSIVE BUTTONS ===== */

.btn, .cta-btn, button {
    min-height: 44px; /* Touch-friendly minimum */
    padding: 0.75rem 1.5rem;
    font-size: clamp(0.875rem, 2vw, 1rem);
    border: none;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    white-space: nowrap;
    max-width: 100%;
}

/* ===== RESPONSIVE FORMS ===== */

input, textarea, select {
    width: 100%;
    min-height: 44px; /* Touch-friendly */
    padding: 0.75rem;
    font-size: 1rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--color-primary, #4f46e5);
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* ===== RESPONSIVE IMAGES AND MEDIA ===== */

img, video, iframe, embed, object {
    max-width: 100%;
    height: auto;
    display: block;
}

.responsive-media {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 56.25%; /* 16:9 aspect ratio */
    overflow: hidden;
}

.responsive-media > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* ===== RESPONSIVE NAVIGATION ===== */

.navbar {
    width: 100%;
    padding: 0 1rem;
}

.navbar-container {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

/* ===== RESPONSIVE SECTIONS ===== */

section {
    width: 100%;
    padding: 3rem 1rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

/* ===== RESPONSIVE CARDS ===== */

.card {
    width: 100%;
    padding: 1.5rem;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* ===== RESPONSIVE UTILITIES ===== */

.w-full { width: 100%; }
.h-full { height: 100%; }
.max-w-full { max-width: 100%; }
.min-h-screen { min-height: 100vh; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

/* Spacing utilities */
.m-0 { margin: 0; }
.p-0 { padding: 0; }
.mt-1 { margin-top: 0.25rem; }
.mb-1 { margin-bottom: 0.25rem; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }

/* ===== MOBILE BREAKPOINTS ===== */

/* Small mobile devices (320px and up) */
@media (min-width: 320px) {
    .container {
        padding: 0 1rem;
    }
}

/* Large mobile devices (480px and up) */
@media (min-width: 480px) {
    .container {
        padding: 0 1.5rem;
    }
    
    .grid-2-sm { grid-template-columns: repeat(2, 1fr); }
}

/* Tablets (768px and up) */
@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }
    
    .grid-2-md { grid-template-columns: repeat(2, 1fr); }
    .grid-3-md { grid-template-columns: repeat(3, 1fr); }
    
    section {
        padding: 4rem 2rem;
    }
}

/* Desktop (1024px and up) */
@media (min-width: 1024px) {
    .container {
        padding: 0 2rem;
    }
    
    .grid-3-lg { grid-template-columns: repeat(3, 1fr); }
    .grid-4-lg { grid-template-columns: repeat(4, 1fr); }
    
    section {
        padding: 5rem 2rem;
    }
}

/* Large desktop (1280px and up) */
@media (min-width: 1280px) {
    .container {
        padding: 0 2rem;
    }

    .grid-4-xl { grid-template-columns: repeat(4, 1fr); }
    .grid-5-xl { grid-template-columns: repeat(5, 1fr); }
}

/* ===== MOBILE-SPECIFIC RESPONSIVE RULES ===== */

/* Mobile-first approach - styles for mobile devices */
@media (max-width: 767px) {
    /* Force single column layout on mobile */
    .services-grid,
    .features-grid,
    .solutions-grid,
    .testimonial-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }

    /* Mobile navigation */
    .nav-links {
        display: none;
        position: fixed;
        top: 60px;
        left: 0;
        width: 100%;
        background: var(--color-bg-primary, #ffffff);
        flex-direction: column;
        padding: 1rem 0;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 1000;
    }

    .nav-links.mobile-active {
        display: flex;
    }

    .mobile-menu-toggle {
        display: block !important;
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        width: 30px;
        height: 30px;
        cursor: pointer;
        z-index: 1001;
    }

    /* Mobile hero section */
    .hero-section {
        min-height: 70vh;
        padding: 4rem 1rem 3rem;
        text-align: center;
    }

    .hero-cta {
        flex-direction: column;
        gap: 1rem;
        width: 100%;
        max-width: 300px;
        margin: 0 auto;
    }

    .hero-cta .btn {
        width: 100%;
        max-width: none;
    }

    /* Mobile cards */
    .feature-card,
    .service-card,
    .solution-card {
        margin-bottom: 1rem;
        padding: 1rem;
    }

    /* Mobile forms */
    .form-group {
        margin-bottom: 1rem;
    }

    /* Mobile chatbot */
    .chatbot-widget {
        width: calc(100% - 20px) !important;
        height: 70vh !important;
        bottom: 10px !important;
        left: 10px !important;
        right: 10px !important;
    }

    .chatbot-launcher {
        width: 50px !important;
        height: 50px !important;
        bottom: 20px !important;
        right: 20px !important;
    }

    /* Mobile footer */
    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 2rem;
    }

    .footer-section {
        width: 100%;
    }
}

/* ===== TABLET-SPECIFIC RESPONSIVE RULES ===== */

@media (min-width: 768px) and (max-width: 1023px) {
    /* Tablet grid layouts */
    .services-grid,
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .solutions-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }

    /* Tablet navigation */
    .navbar-link {
        padding: 0 0.5rem;
        font-size: 0.875rem;
    }

    /* Tablet hero section */
    .hero-section {
        padding: 5rem 2rem 4rem;
    }

    .hero-cta {
        flex-direction: row;
        gap: 1rem;
        justify-content: center;
    }

    .hero-cta .btn {
        flex: 1;
        max-width: 250px;
    }

    /* Tablet chatbot */
    .chatbot-widget {
        width: 350px !important;
        height: 500px !important;
        bottom: 20px !important;
        right: 20px !important;
    }
}

/* ===== LANDSCAPE ORIENTATION ADJUSTMENTS ===== */

@media (max-width: 768px) and (orientation: landscape) {
    .hero-section {
        min-height: 90vh;
        padding: 2rem 1rem;
    }

    .chatbot-widget {
        height: 80vh !important;
        max-height: 400px !important;
    }

    .navbar {
        height: 50px !important;
    }
}

/* ===== HIGH DPI DISPLAYS ===== */

@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Optimize for retina displays */
    .hero-bg,
    .section-bg {
        background-size: 50% 50%;
    }
}

/* ===== ACCESSIBILITY RESPONSIVE FEATURES ===== */

@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== PRINT STYLES ===== */

@media print {
    .navbar,
    .chatbot-launcher,
    .chatbot-widget,
    .mobile-menu-toggle {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }

    .container {
        max-width: none !important;
        padding: 0 !important;
    }
}
