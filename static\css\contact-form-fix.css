/**
 * Contact Form Fix
 * Fixes layout issues with the contact form
 */

/* Fix contact container layout */
.contact-container {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem; /* Restored to previous gap */
    max-width: 1000px; /* Restored to previous size but still compact */
    margin: 0 auto;
    position: relative;
    z-index: 5;
}

/* Ensure proper sizing for form and info containers */
.contact-form-container,
.contact-info {
    flex: 1;
    min-width: 300px; /* Restored to previous size */
    position: relative;
    z-index: 10;
}

/* Prevent form from overlapping with other content */
.contact-form {
    background-color: var(--color-background);
    padding: 1rem; /* Further reduced padding to decrease height */
    border-radius: 12px; /* More curved edges */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); /* Restored to previous shadow */
    border: 2px solid var(--color-border); /* Restored to previous border */
    position: relative;
    z-index: 15;
    width: 100%;
    max-width: 450px; /* Keep same width */
    box-sizing: border-box;
}

/* Ensure form inputs are properly sized */
.contact-form input[type="text"],
.contact-form input[type="email"],
.contact-form textarea {
    width: 100%;
    padding: 0.5rem; /* Further reduced padding to decrease height */
    border-radius: 10px; /* More curved edges for inputs */
    border: 2px solid var(--color-border); /* Restored to previous border */
    background-color: var(--color-background);
    color: var(--color-text);
    transition: all 0.2s ease;
    font-size: 0.9375rem; /* Restored to previous font size */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* Restored to previous shadow */
    margin-bottom: 0.125rem; /* Further reduced margin to decrease height */
    box-sizing: border-box;
    position: relative;
    z-index: 20;
}

/* Ensure contact info is properly displayed */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 1rem;
}

.contact-method {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    background-color: rgba(255, 255, 255, 0.5);
    padding: 1rem;
    border-radius: 12px; /* More curved edges */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.dark-theme .contact-method {
    background-color: rgba(30, 30, 30, 0.5);
}

.contact-method:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark-theme .contact-method:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .contact-container {
        flex-direction: column;
    }

    .contact-form-container,
    .contact-info {
        width: 100%;
    }

    .contact-form {
        margin-bottom: 2rem;
    }
}

/* Dark theme adjustments */
.dark-theme .contact-form {
    background-color: #191919;
    border: 2px solid rgba(100, 181, 246, 0.3); /* Restored to previous border */
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3); /* Restored to previous shadow */
    padding: 1rem; /* Further reduced padding to decrease height */
}

.dark-theme .contact-form input[type="text"],
.dark-theme .contact-form input[type="email"],
.dark-theme .contact-form textarea {
    background-color: #1e1e1e;
    border: 2px solid rgba(100, 181, 246, 0.3); /* Restored to previous border */
    color: white;
    border-radius: 10px; /* More curved edges for inputs */
}
