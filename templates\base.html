<!DOCTYPE html>
<html lang="en">
<head>
    <!-- Immediate theme application to prevent flash -->
    <script>
        (function() {
            // Wait for DOM to be ready
            function applyTheme() {
                // Get saved theme or default to light
                var savedTheme = localStorage.getItem('ziantrix_theme');
                
                // Apply theme immediately
                if (savedTheme === 'dark') {
                    if (document.documentElement) {
                        document.documentElement.classList.add('dark-theme');
                    }
                    if (document.body) {
                        document.body.classList.add('dark-theme');
                    }
                } else {
                    if (document.documentElement) {
                        document.documentElement.classList.remove('dark-theme');
                    }
                    if (document.body) {
                        document.body.classList.remove('dark-theme');
                    }
                    // Explicitly set light theme
                    localStorage.setItem('ziantrix_theme', 'light');
                }

                // Prevent transitions on initial load
                if (document.documentElement) {
                    document.documentElement.classList.add('no-transition');
                }
                if (document.body) {
                    document.body.classList.add('no-transition');
                }

                // Remove no-transition class after a short delay
                setTimeout(function() {
                    if (document.documentElement) {
                        document.documentElement.classList.remove('no-transition');
                    }
                    if (document.body) {
                        document.body.classList.remove('no-transition');
                    }
                }, 100);
            }

            // Try to apply theme when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', applyTheme);
            } else {
                applyTheme();
            }
        })();
    </script>
    <!-- Aggressive Preloader Fix - Inline script to ensure it runs as early as possible -->
    <script>
        // Immediately hide preloader
        (function() {
            // Function to remove preloader
            function removePreloader() {
                var preloader = document.querySelector('.preloader');
                if (preloader) {
                    preloader.style.opacity = '0';
                    preloader.style.visibility = 'hidden';
                    preloader.style.pointerEvents = 'none';
                    preloader.style.display = 'none';
                    if (document.body) {
                        document.body.style.overflow = 'auto';
                    }
                }
            }

            // Try to remove preloader immediately
            removePreloader();

            // Also try when DOM is ready
            document.addEventListener('DOMContentLoaded', removePreloader);

            // Final fallback - remove after a timeout
            setTimeout(removePreloader, 1000);
            setTimeout(removePreloader, 2000);

            // Also remove on window load event
            window.addEventListener('load', removePreloader);
        })();
    </script>

    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="theme-color" content="#1976d2">
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="preconnect" href="//cdnjs.cloudflare.com" crossorigin>
    <title>{% block title %}Ziantrix - AI Co-Workers for Your Business - No Tech Team Needed{% endblock %}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://calendar.app.google" crossorigin>
    <link rel="dns-prefetch" href="https://calendar.app.google">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <!-- Critical Path CSS (inlined for performance) -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/critical-path.css') }}">

    <!-- Performance Optimizations -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/performance-optimizations.css') }}">

    <!-- Additional critical styles -->
    <style>
        :root {
          --color-primary: #4f46e5;
          --color-primary-light: #818cf8;
          --color-primary-dark: #3730a3;
          --color-accent: #a855f7;
          --color-bg-light: #ffffff;
          --color-bg-dark: #0f172a;
          --color-text-light: #111827;
          --color-text-dark: #f8fafc;
        }
        *, *::before, *::after { box-sizing: border-box; margin: 0; padding: 0; }
        body { font-family: 'Inter', system-ui, sans-serif; overflow-x: hidden; }
        .navbar { position: fixed; top: 0; left: 0; right: 0; z-index: 50; background-color: #f8fafc; height: 42px; }
        .dark-theme .navbar { background-color: #0f172a; }
    </style>

    <!-- Enhanced Theme System (loads first to establish theme variables) -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-theme.css') }}">

    <!-- Smooth Theme Transition -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/smooth-theme-transition.css') }}">

    <!-- Consistent Mode Appearance -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/consistent-mode-appearance.css') }}">

    <!-- Consolidated Styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/ziantrix-main.css') }}">

    <!-- DISABLE CONFLICTING HEADER HIGHLIGHT STYLES -->
    <style>
        /* Disable all header-highlight.css styles */
        .navbar-link::after,
        .dropdown-item::after,
        a[data-target]::after,
        #about-nav-link::after,
        #support-nav-link::after,
        #services-nav-link::after,
        .navbar-link.dropdown-toggle::after,
        .navbar-link.active::after,
        .dropdown-item.active::after,
        a[data-target].active::after,
        #about-nav-link.active::after,
        #support-nav-link.active::after,
        #services-nav-link.active::after,
        .navbar-link.dropdown-toggle.active::after,
        .navbar-link:hover::after,
        .dropdown-item:hover::after,
        a[data-target]:hover::after,
        #about-nav-link:hover::after,
        #support-nav-link:hover::after,
        #services-nav-link:hover::after,
        .navbar-link.dropdown-toggle:hover::after {
            display: none !important;
            content: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
            width: 0 !important;
            height: 0 !important;
        }
    </style>

    <!-- Legacy Styles (will be gradually replaced) -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhancements.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-chatbot.css') }}">
    <!-- Legacy chatbot styles for backward compatibility -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/chatbot.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/chatbot-buttons.css') }}">
    <!-- Login enhancements CSS removed - authentication disabled -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/form-enhancements.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/spacing-fixes.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/feature-cards.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern-feature-grid.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/interactive-faq.css') }}">

    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-cta.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-cta-section.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bubble-cta.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fixes.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/metric-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/feature-metrics-direct-fix.css') }}">
    <!-- Password toggle CSS removed - authentication disabled -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/improved-dropdown.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/simple-services-dropdown.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/professional-services.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/chatbot-override.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/chatbot-button-fix.css') }}">
    <!-- Navigation fix -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/nav-fix.css') }}">
    <!-- Flicker-Free Navigation (replaces all other navigation CSS) -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/flicker-free-navigation.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fix-dropdown-functionality.css') }}">
    <!-- Enhanced Logo Styling - Larger size and blue color -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-logo.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/form-input-fix.css') }}">
    <!-- Password icon CSS removed - authentication disabled -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/user-dropdown-fix.css') }}">
    <!-- Disabled header glow to prevent UI disturbances -->
    <!-- <link rel="stylesheet" href="{{ url_for('static', filename='css/simple-header-glow.css') }}"> -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/contact-form-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/flash-message-enhancements.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/calendar-link-optimization.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/remove-select-arrow.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/info-message.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/select-option-tooltip.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/search-button-alignment.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/search-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/search-box-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/search-button-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/proof-points.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/faq-mode-consistency.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/transparent-feature-cards.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/remove-metric-backgrounds.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/final-metric-background-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/footer-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/feature-card-alignment.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/professional-services.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/services-override.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-solutions.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/checkmark-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/cta-button-fixes.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/service-cta-button-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/service-card-equal-width.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/service-card-width-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/service-feature-text-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/service-card-footer-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/service-card-button-display-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/service-card-alignment-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/service-button-width-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/fix-feature-cards-visibility.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/remove-text-glow.css') }}">

    <!-- Clickable Contact Styling -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/clickable-contact.css') }}">

    <!-- Cross-device compatibility improvements -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/cross-device-compatibility.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-menu-toggle.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/chatbot-mobile-fix.css') }}">

    <!-- Comprehensive Responsive Design -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive-design.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/mobile-enhancements.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive-layout-fixes.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive-media.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/responsive-utilities.css') }}">



    <!-- Apply Exact Dark Mode Pattern to Light Mode -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/exact-dark-pattern-for-light.css') }}">

    <!-- Button Alignment Fix - Ensures CTA buttons are perfectly aligned -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/button-alignment-fix.css') }}">

    <!-- Preload critical fonts and images -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" as="style">

    <!-- Performance Optimizations - Load First -->
    <script src="{{ url_for('static', filename='js/optimized-scroll.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/image-optimization.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/performance-monitor.js') }}" defer></script>

    <!-- External Link Optimizer - High Priority -->
    <script src="{{ url_for('static', filename='js/external-link-optimizer.js') }}"></script>



    <!-- Master Navigation Handler (replaces all other navigation scripts) -->
    <script src="{{ url_for('static', filename='js/header-fix.js') }}"></script>

    <!-- Select Option Tooltip -->
    <script src="{{ url_for('static', filename='js/select-option-tooltip.js') }}"></script>

    <!-- Search Button Visibility Fix -->
    <script src="{{ url_for('static', filename='js/search-button-visibility-fix.js') }}"></script>

    <!-- Fix Search Button Text -->
    <script src="{{ url_for('static', filename='js/fix-search-button-text.js') }}"></script>

    <!-- Remove Button Ellipsis -->
    <script src="{{ url_for('static', filename='js/remove-button-ellipsis.js') }}"></script>

    <!-- Service CTA Button Fix -->
    <script src="{{ url_for('static', filename='js/service-cta-button-fix.js') }}"></script>

    <!-- Fix Service Button Text -->
    <script src="{{ url_for('static', filename='js/fix-service-button-text.js') }}"></script>

    <!-- Service Card Equal Width -->
    <script src="{{ url_for('static', filename='js/service-card-equal-width.js') }}"></script>

    <!-- Service Feature Text Fix -->
    <script src="{{ url_for('static', filename='js/service-feature-text-fix.js') }}"></script>

    <!-- Service Card Button Display Fix -->
    <script src="{{ url_for('static', filename='js/service-card-button-display-fix.js') }}"></script>

    <!-- Service Card Alignment Fix -->
    <script src="{{ url_for('static', filename='js/service-card-alignment-fix.js') }}"></script>

    <!-- Service Button Width Fix -->
    <script src="{{ url_for('static', filename='js/service-button-width-fix.js') }}"></script>

    <!-- Mobile Navigation -->
    <script src="{{ url_for('static', filename='js/mobile-navigation.js') }}"></script>

    <!-- Chatbot Touch Fix -->
    <script src="{{ url_for('static', filename='js/chatbot-touch-fix.js') }}"></script>

    <!-- Responsive Enhancements -->
    <script src="{{ url_for('static', filename='js/responsive-enhancements.js') }}"></script>

    <link rel="icon" type="image/png" href="{{ url_for('static', filename='img/favicon.png') }}">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script>
        // Fallback for Font Awesome if CDN fails
        (function() {
            var css = document.querySelector('link[href*="font-awesome"]');
            if (css !== null) {
                css.addEventListener('error', function() {
                    var link = document.createElement('link');
                    link.rel = 'stylesheet';
                    link.href = '{{ url_for("static", filename="css/fontawesome-fallback.css") }}';
                    document.head.appendChild(link);
                });
            }
        })();

        // Register Service Worker for better mobile performance
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('{{ url_for("static", filename="js/service-worker.js") }}').then(function(registration) {
                    console.log('ServiceWorker registration successful with scope: ', registration.scope);
                }, function(err) {
                    console.log('ServiceWorker registration failed: ', err);
                });
            });
        }
    </script>
    <meta name="description" content="{% block meta_description %}Ziantrix provides AI-powered chatbot solutions for businesses of all sizes. Enhance customer support with our advanced AI technology.{% endblock %}">

    <!-- Preloader Fix - Load early to prevent stuck preloader -->
    <script src="{{ url_for('static', filename='js/preloader-fix.js') }}"></script>



    {% block extra_head %}{% endblock %}
    <!-- ANTI-FLICKER: DISABLE TRANSITIONS ONLY FOR NAVIGATION -->
    <style>
        /* Specific anti-flicker styles for navigation only */
        .navbar-link,
        .dropdown-item,
        a[data-target],
        #services-nav-link,
        .navbar-dropdown .navbar-link.dropdown-toggle,
        .navbar-link::before, .navbar-link::after,
        .dropdown-item::before, .dropdown-item::after,
        a[data-target]::before, a[data-target]::after,
        .navbar-dropdown .navbar-link.dropdown-toggle::before,
        .navbar-dropdown .navbar-link.dropdown-toggle::after,
        #services-nav-link::before, #services-nav-link::after {
            transition: none !important;
            animation: none !important;
            -webkit-transition: none !important;
            -moz-transition: none !important;
            -o-transition: none !important;
            -webkit-animation: none !important;
            -moz-animation: none !important;
            -o-animation: none !important;
        }
    </style>

    <!-- RESTORE ANIMATIONS FOR CONTENT ELEMENTS (WITHOUT TEXT GLOW) -->
    <style>
        /* Restore animations for content elements without text glow */
        .hero-section *,
        .features-section *,
        .services-section *,
        .industry-solutions *,

        .faq-section *,
        .about-section *,
        .contact-section * {
            transition: all 0.3s ease !important;
            animation: fadeIn 0.5s ease-in-out !important;
            text-shadow: none !important;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>

    <style>
        /* Preloader */
        .preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease, visibility 0.5s ease !important;
        }

        .dark-theme .preloader {
            background-color: #1a1f36;
        }

        .dark-theme .loader:before, .dark-theme .loader:after {
            background-color: #64b5f6;
        }

        .dark-theme .loader-text {
            color: #64b5f6;
        }

        .preloader.fade-out {
            opacity: 0;
            visibility: hidden;
            pointer-events: none;
            display: none;
        }

        .loader {
            width: 80px;
            height: 80px;
            position: relative;
        }

        .loader:before, .loader:after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background-color: #1976d2;
            opacity: 0.6;
            animation: pulse 2s ease-in-out infinite !important;
        }

        .loader:after {
            animation-delay: 1s !important;
        }

        @keyframes pulse {
            0% { transform: scale(0); opacity: 1; }
            100% { transform: scale(1.5); opacity: 0; }
        }

        /* Ensure preloader animation works */
        .loader:before, .loader:after {
            animation: pulse 2s ease-in-out infinite !important;
            -webkit-animation: pulse 2s ease-in-out infinite !important;
        }

        .loader-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 18px;
            font-weight: bold;
            color: #1976d2;
        }
    </style>

    <!-- Force Black Logo - MUST BE LAST -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/force-black-logo.css') }}">

    <!-- Remove All Navigation Borders - FINAL OVERRIDE -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/remove-all-navigation-borders.css') }}">

    <!-- Footer Quick Links Fix - Remove spacing and highlighting -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/footer-quick-links-fix.css') }}">

    <!-- Uniform Button Sizing - Makes all CTA buttons the same size -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/uniform-button-sizing.css') }}">

    <!-- Button Improvements - Enhanced button styling and hover effects -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/button-improvements.css') }}">

    <!-- Contact Demo Modal Styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/contact-demo-modal.css') }}">

    <!-- CRITICAL FIXES - MUST BE LAST TO OVERRIDE EVERYTHING -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/critical-fixes.css') }}">

    <!-- Image Logo Fix - Override all text-based logo styles -->
    <style>
        /* Override all conflicting logo styles for image display */
        .navbar-logo,
        .navbar-logo:link,
        .navbar-logo:visited,
        .navbar-logo:hover,
        .navbar-logo:focus,
        .navbar-logo:active,
        .dark-theme .navbar-logo,
        .dark-theme .navbar-logo:link,
        .dark-theme .navbar-logo:visited,
        .dark-theme .navbar-logo:hover,
        .dark-theme .navbar-logo:focus,
        .dark-theme .navbar-logo:active {
            /* Reset all text-specific properties */
            color: initial !important;
            background: none !important;
            background-image: none !important;
            background-color: transparent !important;
            background-clip: initial !important;
            -webkit-background-clip: initial !important;
            text-shadow: none !important;
            filter: none !important;
            font-size: initial !important;
            font-weight: initial !important;
            letter-spacing: initial !important;

            /* Ensure proper display for image */
            display: flex !important;
            align-items: center !important;
            position: absolute !important;
            left: 25px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            height: auto !important;
            width: auto !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Style the image inside the logo link */
        .navbar-logo img {
            display: block !important;
            height: 40px !important;
            width: auto !important;
            max-width: none !important;
            opacity: 1 !important;
            visibility: visible !important;
            filter: none !important;
            background: none !important;
            border: none !important;
            outline: none !important;
        }

        /* Hover effect for the image */
        .navbar-logo:hover {
            transform: translateY(calc(-50% - 2px)) !important;
        }

        .navbar-logo:hover img {
            filter: brightness(1.1) !important;
        }

        /* Mobile responsive */
        @media (max-width: 768px) {
            .navbar-logo {
                left: 10px !important;
            }

            .navbar-logo img {
                height: 32px !important;
            }
        }
    </style>
</head>
<body>
    <!-- Preloader -->
    <div class="preloader">
        <div class="loader">
            <div class="loader-text">Z</div>
        </div>
    </div>
    <!-- Theme Toggle moved to navbar -->

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">
                        <span class="message-text">{{ message }}</span>
                        <button class="close-btn">&times;</button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Navigation Bar with Login/Logout -->
    <nav class="navbar">
        <div class="navbar-container" style="display: flex; align-items: center; padding: 0 20px; position: relative; justify-content: space-between;">
            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" style="display: none;" aria-label="Toggle mobile menu" onclick="toggleMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </button>

            <!-- Left Logo -->
            <a href="/" class="navbar-logo" style="display: flex !important; align-items: center !important; visibility: visible !important; opacity: 1 !important; position: absolute !important; left: 4px !important; top: 50% !important; transform: translateY(-50%) !important; z-index: 100 !important; background: none !important; color: transparent !important; width: auto !important; max-width: 200px !important;">
                <img src="{{ url_for('static', filename='img/Logo_flat_app_header.png') }}" alt="Ziantrix" class="responsive-img-contain" style="width: 200px !important; height: 50px !important; object-fit: contain !important; display: block !important; visibility: visible !important; opacity: 1 !important; filter: none !important; background: none !important; border: none !important; z-index: 101 !important;">
            </a>

            <!-- Centered Navigation -->
            <div class="nav-links" style="display: flex; align-items: center; height: 100%; justify-content: center; margin: 0 auto;">
                <a href="/#features" class="navbar-link" data-target="features" onclick="navigateTo('/#features'); return false;" style="transition: none !important; animation: none !important;">Why Ziantrix</a>

                <a href="/#services" class="navbar-link" data-target="services" id="services-nav-link" onclick="navigateTo('/#services'); return false;" style="transition: none !important; animation: none !important;">Services</a>

                <a href="/#solutions" class="navbar-link" data-target="solutions" onclick="navigateTo('/#solutions'); return false;" style="transition: none !important; animation: none !important;">Industries</a>

                <a href="/#experience-ai" class="navbar-link" data-target="experience-ai" onclick="navigateTo('/#experience-ai'); return false;" style="transition: none !important; animation: none !important;">Experience AI</a>

                <a href="/#impact" class="navbar-link" data-target="impact" onclick="navigateTo('/#impact'); return false;" style="transition: none !important; animation: none !important;">Impact</a>

                <a href="/#faq" class="navbar-link" data-target="faq" onclick="navigateTo('/#faq'); return false;" style="transition: none !important; animation: none !important;">FAQ</a>
            </div>

            <!-- Right Side - Empty (login and support removed) -->
            <div class="nav-right" style="display: flex; align-items: center; gap: 1rem;">
                <!-- All authentication and support links removed -->
            </div>
        </div>
    </nav>



    {% block header_content %}{% endblock %}

    <!-- Main Content -->
    <main style="min-height: auto; margin-top: 0; padding-top: 0;">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer>
        <div class="footer-content" style="background: transparent !important;">
            <div class="footer-section" style="background: transparent !important;">
                <h3 style="background: transparent !important;">Ziantrix</h3>
                <p style="background: transparent !important;">AI-powered chatbot solutions for businesses of all sizes.</p>
            </div>
            <div class="footer-section" style="background: transparent !important;">
                <h3 style="background: transparent !important;">Quick Links</h3>
                <ul style="background: transparent !important;">
                    <li style="background: transparent !important;"><a href="/#features" data-target="features" onclick="navigateTo('/#features'); return false;" style="background: transparent !important;">Why Ziantrix</a></li>
                    <li style="background: transparent !important;"><a href="/#services" data-target="services" onclick="navigateTo('/#services'); return false;" style="background: transparent !important;">Services</a></li>
                    <li style="background: transparent !important;"><a href="/#solutions" data-target="solutions" onclick="navigateTo('/#solutions'); return false;" style="background: transparent !important;">Industries</a></li>
                    <li style="background: transparent !important;"><a href="/#impact" data-target="impact" onclick="navigateTo('/#impact'); return false;" style="background: transparent !important;">Impact</a></li>
                    <li style="background: transparent !important;"><a href="/#faq" data-target="faq" onclick="navigateTo('/#faq'); return false;" style="background: transparent !important;">FAQ</a></li>


                </ul>
            </div>
            <div class="footer-section" style="background: transparent !important;">
                <h3 style="background: transparent !important; margin-bottom: 1rem;">Contact Us</h3>
                <div class="footer-contact-icons" style="display: flex; gap: 1rem; align-items: center; justify-content: flex-start; margin-top: 0; margin-left: 0; padding-left: 0;">
                    <!-- Email Icon -->
                    <div class="footer-contact-icon" onclick="window.location.href='mailto:<EMAIL>'" style="display: flex; align-items: center; justify-content: center; cursor: pointer; padding: 8px;">
                        <i class="fas fa-envelope" style="color: #000000; font-size: 20px;"></i>
                    </div>

                    <!-- Phone Icon -->
                    <div class="footer-contact-icon" onclick="window.location.href='tel:+917093388672'" style="display: flex; align-items: center; justify-content: center; cursor: pointer; padding: 8px;">
                        <i class="fas fa-phone" style="color: #000000; font-size: 20px;"></i>
                    </div>

                    <!-- LinkedIn Icon -->
                    <div class="footer-contact-icon" onclick="window.open('https://www.linkedin.com/company/ziantrixai/', '_blank', 'noopener,noreferrer')" style="display: flex; align-items: center; justify-content: center; cursor: pointer; padding: 8px;">
                        <i class="fab fa-linkedin-in" style="color: #000000; font-size: 20px;"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer-bottom" style="background: transparent !important;">
            <p style="background: transparent !important;">&copy; 2025 Ziantrix. All rights reserved.</p>
        </div>
    </footer>

    <!-- Footer Contact Icons Styling -->
    <style>
        /* Clean contact icons without any background effects */
        .footer-contact-icon {
            transition: none !important;
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
        }

        .footer-contact-icon:hover {
            transform: none !important;
            background: transparent !important;
            box-shadow: none !important;
        }

        .footer-contact-icon:hover i {
            color: #000000 !important;
        }

        .footer-contact-icon:active {
            transform: none !important;
            background: transparent !important;
        }

        @media (max-width: 768px) {
            .footer-contact-icons {
                gap: 0.8rem !important;
            }

            .footer-contact-icon {
                padding: 6px !important;
            }

            .footer-contact-icon i {
                font-size: 18px !important;
                color: #000000 !important;
            }
        }
    </style>

    <!-- Script to forcibly remove backgrounds from footer text -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Function to remove backgrounds from footer text
            function removeFooterBackgrounds() {
                // Target all footer text elements
                const footerElements = document.querySelectorAll('footer p, footer h3, footer a, footer li, footer span, footer div, .footer-section p, .footer-section h3, .footer-section a, .footer-section li, .footer-section span, .footer-section div, .footer-content p, .footer-content h3, .footer-content a, .footer-content li, .footer-content span, .footer-content div, .footer-bottom p, .footer-bottom span, .footer-bottom div');

                footerElements.forEach(function(element) {
                    element.style.backgroundColor = 'transparent';
                    element.style.background = 'transparent';
                    element.style.boxShadow = 'none';
                    element.style.border = 'none';
                    element.style.padding = '0';
                });
            }

            // Run immediately
            removeFooterBackgrounds();

            // Run after a short delay to ensure all styles are loaded
            setTimeout(removeFooterBackgrounds, 100);
            setTimeout(removeFooterBackgrounds, 500);

            // Run when theme changes
            const checkbox = document.getElementById('checkbox');
            if (checkbox) {
                checkbox.addEventListener('change', function() {
                    setTimeout(removeFooterBackgrounds, 100);
                });
            }
        });
    </script>

    <!-- Core JavaScript -->
    <script>
        // Navigation styles are now in clean-navigation.css

        // Global navigation function - now uses the master navigation handler
        function navigateTo(url) {
            if (url.startsWith('/#')) {
                const sectionId = url.substring(2);

                // Use the master navigation handler if available
                if (window.navigationHandler && typeof window.navigationHandler.scrollToSection === 'function') {
                    window.navigationHandler.scrollToSection(sectionId);
                } else {
                    // Fallback behavior
                    const targetSection = document.getElementById(sectionId);
                    if (targetSection) {
                        const headerOffset = 80;
                        const elementPosition = targetSection.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });

                        // Update URL hash
                        history.pushState(null, null, url);

                        // Only highlight header navigation, not footer links
                        const headerNavItems = document.querySelectorAll('.navbar-link:not(footer .navbar-link), .dropdown-item:not(footer .dropdown-item)');
                        headerNavItems.forEach(item => {
                            if (item) {
                                item.classList.remove('active');
                                if (item.getAttribute('href') === url || item.getAttribute('data-target') === sectionId) {
                                    item.classList.add('active');
                                }
                            }
                        });
                    } else {
                        // Navigate to the homepage with the hash
                        window.location.href = url;
                    }
                }
            } else {
                // For other URLs, just navigate
                window.location.href = url;
            }
        }

        // Mobile menu toggle function
        function toggleMobileMenu() {
            const menuToggle = document.querySelector('.mobile-menu-toggle');
            const navLinks = document.querySelector('.nav-links');

            if (menuToggle && navLinks) {
                menuToggle.classList.toggle('active');
                navLinks.classList.toggle('mobile-active');
                document.body.classList.toggle('menu-open');

                // Close menu when clicking outside
                if (navLinks.classList.contains('mobile-active')) {
                    document.addEventListener('click', closeMobileMenuOnOutsideClick);
                } else {
                    document.removeEventListener('click', closeMobileMenuOnOutsideClick);
                }
            }
        }

        // Close mobile menu when clicking outside
        function closeMobileMenuOnOutsideClick(event) {
            const menuToggle = document.querySelector('.mobile-menu-toggle');
            const navLinks = document.querySelector('.nav-links');

            if (!menuToggle.contains(event.target) && !navLinks.contains(event.target)) {
                menuToggle.classList.remove('active');
                navLinks.classList.remove('mobile-active');
                document.body.classList.remove('menu-open');
                document.removeEventListener('click', closeMobileMenuOnOutsideClick);
            }
        }

        // Handle window resize to close mobile menu on desktop
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 768) {
                const menuToggle = document.querySelector('.mobile-menu-toggle');
                const navLinks = document.querySelector('.nav-links');

                if (menuToggle && navLinks) {
                    menuToggle.classList.remove('active');
                    navLinks.classList.remove('mobile-active');
                    document.body.classList.remove('menu-open');
                    document.removeEventListener('click', closeMobileMenuOnOutsideClick);
                }
            }
        });

        // Mobile menu toggle (legacy support)
        const menuToggle = document.querySelector('.menu-toggle');
        const navbarMenu = document.querySelector('.navbar-menu');
        const navbarRight = document.querySelector('.navbar-right');

        if (menuToggle && navbarMenu && navbarRight) {
            menuToggle.addEventListener('click', function() {
                menuToggle.classList.toggle('active');
                navbarMenu.classList.toggle('active');
                navbarRight.classList.toggle('active');
                document.body.classList.toggle('menu-open');
            });
        }

        // Handle dropdown on mobile
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
        dropdownToggles.forEach(toggle => {
            if (toggle) {
                toggle.addEventListener('click', function(e) {
                    if (window.innerWidth <= 1023) {
                        e.preventDefault();
                        const dropdown = this.closest('.navbar-dropdown');
                        if (dropdown) {
                            dropdown.classList.toggle('active');
                        }
                    }
                });
            }
        });

        // Close flash messages
        const closeButtons = document.querySelectorAll('.flash-message .close-btn');
        closeButtons.forEach(button => {
            if (button) {
                button.addEventListener('click', function() {
                    const flashMessage = this.parentElement;
                    if (flashMessage) {
                        flashMessage.classList.add('fade-out');
                        setTimeout(() => {
                            flashMessage.style.display = 'none';
                        }, 300);
                    }
                });
            }
        });

        // Auto-hide flash messages after 5 seconds
        setTimeout(() => {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(message => {
                if (message) {
                    message.classList.add('fade-out');
                    setTimeout(() => {
                        message.style.display = 'none';
                    }, 300);
                }
            });
        }, 5000);

        // Sticky navigation handling
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            window.addEventListener('scroll', function() {
                if (window.scrollY > 10) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });
        }
    </script>

    {% block scripts %}{% endblock %}
    <!-- DISABLED Navigation fix scripts to prevent conflicts -->
    <!-- <script src="{{ url_for('static', filename='js/nav-fix.js') }}" defer></script> -->
    <!-- <script src="{{ url_for('static', filename='js/navigation-fix-all.js') }}" defer></script> -->
    <!-- <script src="{{ url_for('static', filename='js/navigation-highlight.js') }}" defer></script> -->

    <!-- Modal for demo request -->
    <div id="demoModal" class="modal">
        <div class="modal-content">
            <span class="close-modal" onclick="closeDemoModal()">&times;</span>
            <h2 style="margin-bottom: 15px; font-size: 18px;">Contact Us for Demo</h2>
            <form id="demoForm" action="/submit_demo" method="POST">
                <div class="form-group">
                    <label for="demo-name">Full Name *</label>
                    <input type="text" id="demo-name" name="name" placeholder="Enter your full name" required>
                </div>
                <div class="form-group">
                    <label for="demo-email">Email Address *</label>
                    <input type="email" id="demo-email" name="email" placeholder="Enter your email address" required>
                </div>
                <div class="form-group">
                    <label for="demo-company">Company Name *</label>
                    <input type="text" id="demo-company" name="company" placeholder="Enter your company name" required>
                </div>
                <div class="form-group">
                    <label for="demo-phone">Phone Number</label>
                    <input type="tel" id="demo-phone" name="phone" placeholder="Enter your phone number">
                </div>
                <div class="form-group">
                    <label for="demo-industry">Industry</label>
                    <select id="demo-industry" name="industry">
                        <option value="">Select your industry</option>
                        <option value="technology">Technology</option>
                        <option value="healthcare">Healthcare</option>
                        <option value="finance">Finance</option>
                        <option value="retail">Retail</option>
                        <option value="education">Education</option>
                        <option value="manufacturing">Manufacturing</option>
                        <option value="other">Other</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="demo-size">Company Size</label>
                    <select id="demo-size" name="company_size">
                        <option value="">Select company size</option>
                        <option value="1-10">1-10 employees</option>
                        <option value="11-50">11-50 employees</option>
                        <option value="51-200">51-200 employees</option>
                        <option value="201-1000">201-1000 employees</option>
                        <option value="1000+">1000+ employees</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="demo-message">Tell us about your needs</label>
                    <textarea id="demo-message" name="message" rows="2"></textarea>
                </div>
                <button type="submit" class="submit-btn cta-btn primary">Start Cutting Support Costs Today</button>
            </form>
        </div>
    </div>

    <!-- Login and registration modals removed - authentication disabled -->

    <!-- Chatbot Widget -->
    <div id="chatbot-widget" class="chatbot-widget">
        <div class="chatbot-header">
            <div class="chatbot-title">
                <div class="chatbot-logo">
                    <span style="font-size: 18px; color: #4f46e5; font-weight: bold;">Z</span>
                </div>
                <span>Ziantrix Assistant</span>
            </div>
            <div class="chatbot-controls" style="display: flex; flex-direction: row; align-items: center;">
                <button class="minimize-btn" onclick="minimizeChatbot()" title="Minimize" style="font-size: 16px; width: 28px; height: 28px; margin-right: 4px;">−</button>
                <button class="close-btn" onclick="closeChatbot()" title="Close" style="font-size: 16px; width: 28px; height: 28px; cursor: pointer; z-index: 1001;">×</button>
            </div>
        </div>
        <div class="chatbot-body">
            <div class="chatbot-messages">
                <div class="message bot-message">
                    <div class="message-avatar">
                        <span style="font-size: 14px; color: #4f46e5; font-weight: bold;">Z</span>
                    </div>
                    <div class="message-content">
                        <p>Hello! I'm the Ziantrix Assistant. I can help you learn about our AI chatbots, services, and agents. What would you like to know about?</p>
                    </div>
                </div>

                <div class="typing-indicator-container" style="display: none;">
                    <div class="message bot-message">
                        <div class="message-avatar">
                            <span style="font-size: 14px; color: #4f46e5; font-weight: bold;">Z</span>
                        </div>
                        <div class="typing-indicator">
                            <span class="dot"></span>
                            <span class="dot"></span>
                            <span class="dot"></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="scroll-indicator" onclick="scrollChatToBottom()" title="Scroll to bottom" style="width: 36px; height: 36px; font-size: 16px; position: fixed; bottom: 150px; right: 40px; z-index: 1000;">
                <i class="fas fa-arrow-down"></i>
            </div>
            <div class="chatbot-input">
                <input type="text" placeholder="Type your message here..." id="chatbot-input-field">
                <button class="send-btn" onclick="sendMessage()" style="width: 32px; height: 32px; font-size: 14px;"><i class="fas fa-paper-plane"></i></button>
            </div>
        </div>
    </div>

    <!-- Chatbot Launcher -->
    <div id="chatbot-launcher" class="chatbot-launcher" onclick="openChatbot()">
        <span style="font-size: 20px; color: white; font-weight: bold;">Z</span>
        <span class="launcher-pulse"></span>
    </div>

    <!-- Modal Functions -->
    <script>
        // Modal functions
        function openDemoModal() {
            document.getElementById('demoModal').classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeDemoModal() {
            document.getElementById('demoModal').classList.remove('active');
            document.body.style.overflow = '';
        }

        // All login and registration modal functions removed - authentication disabled

        // Password toggle functions removed - authentication disabled
    </script>

    <!-- Core Scripts (deferred for performance) -->
    <script src="{{ url_for('static', filename='js/modern-ui.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/chatbot.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/flash-message-handler.js') }}"></script>

    <!-- Chatbot Button Fix Script -->
    <script>
        // Ensure chatbot buttons work properly
        document.addEventListener('DOMContentLoaded', function() {
            // Fix for close button
            const closeBtn = document.querySelector('.chatbot-controls .close-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const chatbot = document.getElementById('chatbot-widget');
                    const launcher = document.getElementById('chatbot-launcher');
                    if (chatbot && launcher) {
                        chatbot.classList.remove('active');
                        launcher.style.display = 'flex';
                        launcher.classList.remove('hidden');
                    }
                });
            }

            // Fix for minimize button
            const minimizeBtn = document.querySelector('.chatbot-controls .minimize-btn');
            if (minimizeBtn) {
                minimizeBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    const chatbot = document.getElementById('chatbot-widget');
                    const launcher = document.getElementById('chatbot-launcher');
                    if (chatbot && launcher) {
                        chatbot.classList.remove('active');
                        launcher.style.display = 'flex';
                        launcher.classList.remove('hidden');
                    }
                });
            }
        });
    </script>
    <!-- Login-related JavaScript files removed - authentication disabled -->
    <script src="{{ url_for('static', filename='js/network-status.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/modal-enhancements.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/form-data-storage.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/form-submission.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/form-input-fix.js') }}"></script>

    <!-- Video Player Script -->
    <script src="{{ url_for('static', filename='js/video-player.js') }}" defer></script>

    <!-- Interactive FAQ Script -->
    <script src="{{ url_for('static', filename='js/interactive-faq.js') }}" defer></script>



    <!-- Professional Services Script -->
    <script src="{{ url_for('static', filename='js/professional-services.js') }}" defer></script>

    <!-- Service Card Fixes -->
    <script src="{{ url_for('static', filename='js/service-card-fixes.js') }}" defer></script>

    <!-- Dark Mode Metrics Fix -->
    <script src="{{ url_for('static', filename='js/dark-mode-metrics-fix.js') }}"></script>
    <script src="{{ url_for('static', filename='js/direct-metrics-fix.js') }}"></script>
    <!-- Password-related JavaScript files removed - authentication disabled -->

    <!-- Dropdown Functionality Fix -->
    <script src="{{ url_for('static', filename='js/fix-dropdown-functionality.js') }}"></script>

    <!-- Header Highlighting for Section Headers -->
    <script src="{{ url_for('static', filename='js/simple-header-glow.js') }}"></script>

    <!-- Contact Demo Modal JavaScript -->
    <script src="{{ url_for('static', filename='js/contact-demo-modal.js') }}" defer></script>

    <!-- Navigation is now handled by the master navigation handler -->


</body>
</html>
