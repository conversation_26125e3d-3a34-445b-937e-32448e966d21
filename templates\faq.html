<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ziantrix FAQ</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-theme.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/modern-faq.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/search-fix.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/faq-mode-consistency.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/faq-new-styles.css') }}?v={{ range(1, 1000) | random }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Theme Toggle Styles */
        .theme-toggle-container {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
        }

        .theme-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
            cursor: pointer;
        }

        .theme-toggle-input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .theme-toggle-input:checked + .slider {
            background-color: #4f46e5;
        }

        .theme-toggle-input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* Light/Dark mode icons */
        .slider:after {
            content: '☀️';
            position: absolute;
            left: 6px;
            top: 3px;
            font-size: 12px;
        }

        .theme-toggle-input:checked + .slider:after {
            content: '🌙';
            left: auto;
            right: 6px;
        }

        /* Dark mode body styles */
        body.dark-theme {
            background-color: #121212;
            color: #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="theme-toggle-container">
        <label class="theme-toggle" for="theme-toggle">
            <input type="checkbox" id="theme-toggle" class="theme-toggle-input">
            <span class="slider"></span>
        </label>
    </div>
    <div class="faq-container">
        <div class="faq-header">
            <h1>Frequently Asked Questions</h1>

        </div>

        <div class="faq-list" id="faqList">
            <div class="faq-item" data-keywords="security encryption data protection privacy gdpr ccpa">
                <button class="faq-question" aria-expanded="false" aria-controls="answer-1">
                    <div class="question-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="question-text">How secure is the Ziantrix chatbot?</div>

                </button>
                <div class="faq-answer" id="answer-1" aria-hidden="true">
                    <div class="answer-content">
                        <p>Ziantrix employs enterprise-grade security measures including:</p>
                        <ul>
                            <li>End-to-end encryption for all conversations</li>
                            <li>SOC 2 Type II compliant data storage</li>
                            <li>Regular third-party security audits</li>
                            <li>Full compliance with GDPR, CCPA, and other data protection regulations</li>
                        </ul>
                        <p>We never store sensitive customer data without explicit permission, and all data is encrypted both in transit and at rest.</p>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="customization branding personalization custom colors fonts visual design">
                <button class="faq-question" aria-expanded="false" aria-controls="answer-2">
                    <div class="question-icon">
                        <i class="fas fa-paint-brush"></i>
                    </div>
                    <div class="question-text">Can the chatbot be customized to match our brand?</div>

                </button>
                <div class="faq-answer" id="answer-2" aria-hidden="true">
                    <div class="answer-content">
                        <p>Absolutely! Our chatbots are fully customizable to match your brand identity:</p>
                        <ul>
                            <li>Custom colors, fonts, and visual elements</li>
                            <li>Personalized tone and voice to match your brand personality</li>
                            <li>Custom avatar or brand mascot integration</li>
                            <li>Tailored conversation flows specific to your business</li>
                        </ul>
                        <p>Our team works closely with you during implementation to ensure the chatbot represents your brand perfectly.</p>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="implementation timeline deployment setup configuration training">
                <button class="faq-question" aria-expanded="false" aria-controls="answer-3">
                    <div class="question-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="question-text">How long does implementation take?</div>

                </button>
                <div class="faq-answer" id="answer-3" aria-hidden="true">
                    <div class="answer-content">
                        <p>Implementation typically takes 2-4 weeks, following this timeline:</p>
                        <ul>
                            <li><strong>Week 1:</strong> Discovery and requirements gathering</li>
                            <li><strong>Week 2:</strong> Knowledge base setup and initial configuration</li>
                            <li><strong>Week 3:</strong> Custom integration and training</li>
                            <li><strong>Week 4:</strong> Testing, refinement, and deployment</li>
                        </ul>
                        <p>For simpler implementations, we can deploy a basic version in as little as 5 business days.</p>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="languages multilingual translation international global">
                <button class="faq-question" aria-expanded="false" aria-controls="answer-4">
                    <div class="question-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="question-text">Does the chatbot support multiple languages?</div>

                </button>
                <div class="faq-answer" id="answer-4" aria-hidden="true">
                    <div class="answer-content">
                        <p>Yes, our chatbot supports over 40 languages, including:</p>
                        <ul>
                            <li>English, Spanish, French, German, Italian</li>
                            <li>Chinese (Simplified & Traditional), Japanese, Korean</li>
                            <li>Arabic, Russian, Portuguese, Dutch</li>
                            <li>And many more regional languages</li>
                        </ul>
                        <p>The AI automatically detects the user's language and responds accordingly, making it perfect for global businesses.</p>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="learning ai machine learning improvement training feedback">
                <button class="faq-question" aria-expanded="false" aria-controls="answer-5">
                    <div class="question-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <div class="question-text">How does the chatbot learn and improve over time?</div>

                </button>
                <div class="faq-answer" id="answer-5" aria-hidden="true">
                    <div class="answer-content">
                        <p>Our chatbot uses advanced machine learning to continuously improve:</p>
                        <ul>
                            <li>Analyzes conversation patterns to identify common questions</li>
                            <li>Learns from successful human agent interactions</li>
                            <li>Improves response accuracy based on user feedback</li>
                            <li>Adapts to changing terminology and new products/services</li>
                        </ul>
                        <p>You have complete control over the learning process with our admin dashboard, where you can review and approve all learning updates.</p>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="integration api crm zendesk salesforce shopify woocommerce">
                <button class="faq-question" aria-expanded="false" aria-controls="answer-6">
                    <div class="question-icon">
                        <i class="fas fa-plug"></i>
                    </div>
                    <div class="question-text">Can Ziantrix integrate with our existing systems?</div>

                </button>
                <div class="faq-answer" id="answer-6" aria-hidden="true">
                    <div class="answer-content">
                        <p>Yes, Ziantrix offers seamless integration with your existing tech stack:</p>
                        <ul>
                            <li>CRM systems (Salesforce, HubSpot, Zoho, etc.)</li>
                            <li>Help desk software (Zendesk, Freshdesk, ServiceNow)</li>
                            <li>E-commerce platforms (Shopify, WooCommerce, Magento)</li>
                            <li>Custom APIs and databases</li>
                        </ul>
                        <p>Our integration specialists ensure a smooth connection with minimal disruption to your current workflows.</p>
                    </div>
                </div>
            </div>
        </div>


    </div>


    <script>
        // Dark mode toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggle = document.getElementById('theme-toggle');

            // Check for saved theme preference or use default
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-theme');
                themeToggle.checked = true;
            }

            // Toggle theme when switch is clicked
            themeToggle.addEventListener('change', function() {
                if (this.checked) {
                    document.body.classList.add('dark-theme');
                    localStorage.setItem('theme', 'dark');
                } else {
                    document.body.classList.remove('dark-theme');
                    localStorage.setItem('theme', 'light');
                }
            });
        });
    </script>
</body>
</html>
