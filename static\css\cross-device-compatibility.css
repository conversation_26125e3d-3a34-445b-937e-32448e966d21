/**
 * Cross-Device Compatibility
 * Ensures consistent functionality across mobile, tablet, and desktop devices
 */

/* Base responsive adjustments */
html {
  -webkit-text-size-adjust: 100%; /* Prevent font scaling in landscape on iOS */
  touch-action: manipulation; /* Prevent delays on touch devices */
}

body {
  overflow-x: hidden; /* Prevent horizontal scrolling on mobile */
  -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  overscroll-behavior-y: none; /* Prevent pull-to-refresh on mobile */
}

/* Improved touch targets for all interactive elements */
button,
a,
input[type="button"],
input[type="submit"],
.navbar-link,
.dropdown-toggle,
.chatbot-launcher,
.chatbot-controls button,
.login-btn,
.cta-btn,
.theme-toggle,
.close-btn {
  min-height: 44px; /* Minimum touch target size per WCAG guidelines */
  min-width: 44px; /* Minimum touch target size per WCAG guidelines */
  touch-action: manipulation; /* Remove delay on mobile devices */
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0); /* Remove tap highlight on iOS */
}

/* Fix for iOS input fields */
input,
textarea {
  -webkit-appearance: none; /* Remove iOS default styling */
  border-radius: 0; /* Remove iOS default styling */
  font-size: 16px !important; /* Prevent zoom on focus in iOS */
}

/* Fix for iOS button styling */
button {
  -webkit-appearance: none; /* Remove iOS default styling */
  border-radius: 0; /* Remove iOS default styling */
}

/* Improved mobile navigation */
@media (max-width: 768px) {
  /* Add hamburger menu for mobile */
  .mobile-menu-toggle {
    display: block !important;
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    z-index: 1000;
    cursor: pointer;
    padding: 10px;
  }

  /* Hide desktop navigation on mobile */
  .nav-links {
    display: none !important;
    position: fixed;
    top: 60px;
    left: 0;
    width: 100%;
    height: auto;
    background-color: var(--color-bg-primary);
    flex-direction: column !important;
    padding: 20px 0;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    z-index: 999;
  }

  /* Show mobile navigation when active */
  .nav-links.mobile-active {
    display: flex !important;
  }

  /* Adjust navigation links for mobile */
  .navbar-link,
  .dropdown-toggle {
    width: 100%;
    text-align: center;
    padding: 15px 0 !important;
    margin: 0 !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  /* Adjust dropdown menu for mobile */
  .navbar-dropdown .dropdown-menu {
    position: static !important;
    width: 100%;
    box-shadow: none !important;
    border-radius: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Adjust dropdown items for mobile */
  .dropdown-item {
    width: 100%;
    text-align: center;
    padding: 15px 0 !important;
    background-color: rgba(0, 0, 0, 0.05) !important;
  }

  /* Dark theme adjustments for mobile navigation */
  .dark-theme .navbar-link,
  .dark-theme .dropdown-toggle {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .dark-theme .dropdown-item {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }
}

/* Chatbot adjustments for mobile */
@media (max-width: 768px) {
  /* Adjust chatbot launcher position and size */
  .chatbot-launcher {
    bottom: 10px !important;
    right: 10px !important;
    width: 50px !important;
    height: 50px !important;
  }

  /* Adjust chatbot widget size and position */
  .chatbot-widget {
    width: calc(100% - 20px) !important;
    height: 70vh !important;
    bottom: 10px !important;
    right: 10px !important;
    left: 10px !important;
    max-width: none !important;
  }

  /* Ensure chatbot controls are easily tappable */
  .chatbot-controls button {
    width: 44px !important;
    height: 44px !important;
    margin-left: 5px !important;
  }

  /* Ensure chatbot input is usable on mobile */
  .chatbot-input input {
    font-size: 16px !important; /* Prevent zoom on focus in iOS */
    padding: 12px !important;
  }

  /* Ensure chatbot messages are readable */
  .message-content p {
    font-size: 16px !important;
    line-height: 1.5 !important;
  }
}

/* Fix for iOS fixed positioning issues */
@supports (-webkit-touch-callout: none) {
  .navbar,
  .chatbot-launcher,
  .chatbot-widget {
    /* Use position: sticky as a fallback for iOS */
    position: fixed;
    /* Add padding to account for iOS safe areas */
    padding-bottom: env(safe-area-inset-bottom);
    padding-top: env(safe-area-inset-top);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}

/* Tablet-specific adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  /* Adjust navigation for tablets */
  .navbar-link {
    padding: 0 10px !important;
    font-size: 14px !important;
  }

  /* Adjust chatbot for tablets */
  .chatbot-widget {
    width: 350px !important;
    height: 500px !important;
    bottom: 15px !important;
    right: 15px !important;
  }
}

/* Landscape mode adjustments for mobile */
@media (max-width: 768px) and (orientation: landscape) {
  /* Adjust chatbot height in landscape */
  .chatbot-widget {
    height: 85vh !important;
  }

  /* Adjust navbar height in landscape */
  .navbar {
    height: 40px !important;
  }

  /* Adjust navigation in landscape */
  .nav-links.mobile-active {
    max-height: 80vh;
    overflow-y: auto;
  }
}

/* High-DPI screen adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Ensure images are sharp on high-DPI screens */
  img {
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Remove focus outlines for better visual appearance */
button:focus,
a:focus,
input:focus,
.navbar-link:focus,
.dropdown-toggle:focus {
  outline: none !important;
  outline-offset: 0 !important;
  box-shadow: none !important;
  border-color: inherit !important;
}

/* Dark theme focus states */
.dark-theme button:focus,
.dark-theme a:focus,
.dark-theme input:focus,
.dark-theme .navbar-link:focus,
.dark-theme .dropdown-toggle:focus {
  outline: none !important;
  box-shadow: none !important;
  border-color: inherit !important;
}
