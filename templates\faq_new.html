<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ziantrix FAQ - New Design</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        /* Base Styles */
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
            margin: 0;
            padding: 0;
        }

        /* Container */
        .faq-container {
            max-width: 550px;
            margin: 1rem auto;
            padding: 1rem;
            border: 3px solid #4f46e5;
            border-radius: 8px;
        }

        /* Header */
        .faq-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .faq-header h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #1a202c;
        }

        /* Search */
        .search-container {
            margin: 1.5rem 0;
        }

        .search-wrapper {
            position: relative;
            max-width: 500px;
            margin: 0 auto;
        }

        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #a0aec0;
        }

        #faqSearch {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 3rem;
            border-radius: 0.5rem;
            border: 2px solid #4f46e5;
            font-size: 0.95rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .search-button {
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            background-color: #4f46e5;
            color: white;
            border: none;
            border-radius: 0.375rem;
            padding: 0.4rem 0.75rem;
            font-weight: 500;
            cursor: pointer;
        }

        /* FAQ List */
        .faq-list {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .faq-item {
            border-radius: 1.5rem; /* Much more rounded edges */
            background: rgba(255, 255, 255, 0.5); /* Subtle background */
            border: 1px solid rgba(0, 0, 0, 0.05); /* Very subtle border */
            overflow: hidden;
            transition: all 0.3s ease;
            margin-bottom: 0.75rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Subtle shadow */
        }

        .faq-question {
            width: 100%;
            display: flex;
            align-items: center;
            padding: 0.75rem;
            background: #f7fafc;
            border: none;
            border-radius: 1.5rem; /* Much more rounded edges for questions */
            text-align: left;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.95rem;
            color: #2d3748;
            transition: all 0.3s ease;
        }

        .question-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 1.75rem;
            height: 1.75rem;
            border-radius: 50%;
            margin-right: 0.75rem;
            background-color: #4f46e5;
            color: white;
            flex-shrink: 0;
        }

        .question-text {
            flex-grow: 1;
        }

        .faq-answer {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.4s ease;
            background: none;
        }

        .faq-question[aria-expanded="true"] + .faq-answer {
            max-height: 800px;
        }

        .answer-content {
            padding: 0 1rem 1rem 3rem;
            color: #4a5568;
            font-size: 0.9rem;
            background-color: #f8fafc;
            border-top: 1px dashed #e2e8f0;
        }

        /* Dark Mode Toggle */
        .theme-toggle-container {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
        }

        .theme-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
            cursor: pointer;
        }

        .theme-toggle-input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        .theme-toggle-input:checked + .slider {
            background-color: #4f46e5;
        }

        .theme-toggle-input:checked + .slider:before {
            transform: translateX(26px);
        }

        /* Dark Mode Styles */
        body.dark-theme {
            background-color: #121212;
            color: #e2e8f0;
        }

        .dark-theme .faq-header h1 {
            color: #e2e8f0;
        }

        .dark-theme .faq-item {
            border: 2px solid #818cf8;
        }

        .dark-theme .faq-question {
            color: #e2e8f0;
            background: #1e293b;
        }

        .dark-theme .question-icon {
            background-color: #818cf8;
            color: #1e1b4b;
        }

        .dark-theme .answer-content {
            color: #a0aec0;
            background-color: #1a202c;
            border-top: 1px dashed #2d3748;
        }

        .dark-theme #faqSearch {
            background-color: #1e293b;
            color: #e2e8f0;
            border-color: #818cf8;
        }

        /* No Results */
        .no-results {
            text-align: center;
            padding: 2rem 1rem;
            color: #718096;
            display: none;
        }

        .no-results.visible {
            display: block;
        }

        .no-results-icon {
            font-size: 2rem;
            color: #cbd5e0;
            margin-bottom: 1rem;
        }

        .reset-button {
            margin-top: 1rem;
            padding: 0.5rem 1rem;
            background-color: #4f46e5;
            color: white;
            border: none;
            border-radius: 0.375rem;
            font-weight: 500;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="theme-toggle-container">
        <label class="theme-toggle" for="theme-toggle">
            <input type="checkbox" id="theme-toggle" class="theme-toggle-input">
            <span class="slider"></span>
        </label>
    </div>
    <div class="faq-container">
        <div class="faq-header">
            <h1>Frequently Asked Questions</h1>

        </div>

        <div class="faq-list" id="faqList">
            <div class="faq-item" data-keywords="security encryption data protection privacy gdpr ccpa">
                <button class="faq-question" aria-expanded="false" aria-controls="answer-1">
                    <div class="question-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="question-text">How secure is the Ziantrix chatbot?</div>
                </button>
                <div class="faq-answer" aria-hidden="true">
                    <div class="answer-content">
                        <p>Ziantrix employs enterprise-grade security measures:</p>
                        <ul>
                            <li>End-to-end encryption for all conversations</li>
                            <li>SOC 2 Type II and ISO 27001 certified infrastructure</li>
                            <li>GDPR and CCPA compliant data handling</li>
                            <li>Regular penetration testing and security audits</li>
                            <li>Optional data residency for sensitive industries</li>
                        </ul>
                        <p>We never use customer data to train our models without explicit permission, and we offer data retention controls to meet your compliance requirements.</p>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="integration api crm zendesk salesforce shopify woocommerce">
                <button class="faq-question" aria-expanded="false" aria-controls="answer-2">
                    <div class="question-icon">
                        <i class="fas fa-plug"></i>
                    </div>
                    <div class="question-text">Can Ziantrix integrate with our existing systems?</div>
                </button>
                <div class="faq-answer" aria-hidden="true">
                    <div class="answer-content">
                        <p>Yes, Ziantrix offers seamless integration with your existing tech stack:</p>
                        <ul>
                            <li>CRM systems (Salesforce, HubSpot, Zoho, etc.)</li>
                            <li>Help desk software (Zendesk, Freshdesk, ServiceNow)</li>
                            <li>E-commerce platforms (Shopify, WooCommerce, Magento)</li>
                            <li>Custom APIs and databases</li>
                        </ul>
                        <p>Our flexible API and pre-built connectors make integration straightforward, typically requiring minimal developer resources. For enterprise clients, we offer dedicated integration support.</p>
                    </div>
                </div>
            </div>

            <div class="faq-item" data-keywords="pricing cost subscription enterprise startup small business">
                <button class="faq-question" aria-expanded="false" aria-controls="answer-3">
                    <div class="question-icon">
                        <i class="fas fa-tag"></i>
                    </div>
                    <div class="question-text">How much does Ziantrix cost?</div>
                </button>
                <div class="faq-answer" aria-hidden="true">
                    <div class="answer-content">
                        <p>Ziantrix offers flexible pricing to accommodate businesses of all sizes:</p>
                        <ul>
                            <li><strong>Starter:</strong> $99/month - Up to 500 conversations, basic integrations</li>
                            <li><strong>Growth:</strong> $299/month - Up to 2,500 conversations, all integrations</li>
                            <li><strong>Scale:</strong> $799/month - Up to 10,000 conversations, priority support</li>
                            <li><strong>Enterprise:</strong> Custom pricing - Unlimited conversations, dedicated support</li>
                        </ul>
                        <p>All plans include our core AI capabilities, analytics dashboard, and regular updates. Volume discounts are available for annual commitments.</p>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // FAQ Toggle
            const faqQuestions = document.querySelectorAll('.faq-question');

            faqQuestions.forEach(question => {
                question.addEventListener('click', () => {
                    const expanded = question.getAttribute('aria-expanded') === 'true';

                    // Close all other questions
                    faqQuestions.forEach(q => {
                        if (q !== question) {
                            q.setAttribute('aria-expanded', 'false');
                            const answer = q.nextElementSibling;
                            answer.setAttribute('aria-hidden', 'true');
                            answer.style.maxHeight = '0';
                        }
                    });

                    // Toggle current question
                    question.setAttribute('aria-expanded', !expanded);
                    const answer = question.nextElementSibling;
                    answer.setAttribute('aria-hidden', !expanded);

                    if (!expanded) {
                        answer.style.maxHeight = answer.scrollHeight + 'px';
                    } else {
                        answer.style.maxHeight = '0';
                    }
                });
            });



            // Dark Mode Toggle
            const themeToggle = document.getElementById('theme-toggle');

            // Check for saved theme preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-theme');
                themeToggle.checked = true;
            }

            // Toggle theme when switch is clicked
            themeToggle.addEventListener('change', function() {
                if (this.checked) {
                    document.body.classList.add('dark-theme');
                    localStorage.setItem('theme', 'dark');
                } else {
                    document.body.classList.remove('dark-theme');
                    localStorage.setItem('theme', 'light');
                }
            });
        });
    </script>
</body>
</html>
