/**
 * Professional Services Section
 * A complete rewrite with professional styling and proper alignment
 */

/* Services Section Container */
.services-section {
  padding: 80px 0;
  width: 100%;
  background-color: var(--color-background);
}

.dark-theme .services-section {
  background-color: var(--color-background-dark);
}

/* Section Header */
.services-section .section-header {
  text-align: center;
  margin-bottom: 60px;
}

.services-section .section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--color-heading);
  position: relative;
  display: inline-block;
}

.services-section .section-title::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%);
  border-radius: 3px;
}

.dark-theme .services-section .section-title {
  color: var(--color-heading-dark);
}

.services-section .section-description {
  font-size: 1.2rem;
  color: var(--color-text-light);
  max-width: 700px;
  margin: 0 auto;
}

.dark-theme .services-section .section-description {
  color: var(--color-text-light-dark);
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Service Card */
.service-card {
  display: flex;
  flex-direction: column;
  background-color: var(--color-card-bg);
  border-radius: 12px;
  border: 1px solid var(--color-border);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.dark-theme .service-card {
  background-color: var(--color-card-bg-dark);
  border-color: var(--color-border-dark);
}

.dark-theme .service-card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Service Card Header */
.service-card-header {
  display: flex;
  align-items: flex-start;
  padding: 24px 24px 20px;
  border-bottom: 1px solid var(--color-border);
}

.dark-theme .service-card-header {
  border-color: var(--color-border-dark);
}

.service-icon {
  width: 56px;
  height: 56px;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.service-icon img {
  width: 100%;
  height: auto;
}

.service-header-text {
  display: flex;
  flex-direction: column;
  text-align: left;
}

.service-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 4px;
  color: var(--color-primary);
  text-align: left;
}

.dark-theme .service-title {
  color: var(--color-primary-light);
}

.service-subtitle {
  font-size: 1rem;
  color: var(--color-text-light);
  margin: 0;
  text-align: left;
}

.dark-theme .service-subtitle {
  color: var(--color-text-light-dark);
}

/* Service Card Body */
.service-card-body {
  padding: 24px;
  flex-grow: 1;
}

/* Service Features List */
.service-features-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
  width: 100%;
}

.service-features-list li {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  text-align: left;
  padding: 0;
}

.service-features-list li:last-child {
  margin-bottom: 0;
}

.service-features-list .feature-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: var(--color-primary);
  background: none;
  width: auto;
  height: auto;
  flex-shrink: 0;
}

.dark-theme .service-features-list .feature-icon {
  color: var(--color-primary-light);
}

.service-features-list .feature-text {
  font-size: 1rem;
  line-height: 1.5;
  color: var(--color-text);
  text-align: left;
}

.dark-theme .service-features-list .feature-text {
  color: var(--color-text-dark);
}

/* Service Card Footer */
.service-card-footer {
  padding: 0 24px 24px;
  margin-top: auto;
}

.service-cta-button {
  display: block;
  width: 100%;
  padding: 14px 20px;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  color: white;
  font-weight: 600;
  font-size: 1rem;
  text-align: center;
  border-radius: 8px;
  transition: all 0.3s ease;
  text-decoration: none;
  border: none;
  cursor: pointer;
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
}

.service-cta-button:hover {
  background: linear-gradient(90deg, var(--color-primary-dark) 0%, var(--color-primary) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(var(--color-primary-rgb), 0.3);
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .services-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    padding: 0 20px;
  }
}

@media (max-width: 768px) {
  .services-section {
    padding: 60px 0;
  }

  .services-section .section-title {
    font-size: 2rem;
  }

  .services-section .section-description {
    font-size: 1rem;
  }

  .service-card-header {
    padding: 20px 20px 16px;
  }

  .service-card-body {
    padding: 20px;
  }

  .service-card-footer {
    padding: 0 20px 20px;
  }
}

@media (max-width: 480px) {
  .services-section {
    padding: 40px 0;
  }

  .services-section .section-title {
    font-size: 1.75rem;
  }

  .service-icon {
    width: 48px;
    height: 48px;
  }

  .service-title {
    font-size: 1.25rem;
  }

  .service-subtitle {
    font-size: 0.875rem;
  }
}
