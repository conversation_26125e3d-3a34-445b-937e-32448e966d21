/**
 * Spacing Fixes
 * Consistent spacing throughout the website
 */

/* Global spacing adjustments - COMPACT SPACING FOR ALL SECTIONS */
section {
  padding: 48px 0 !important; /* Reduced from 60px to 48px for tighter layout */
  margin-bottom: 0 !important; /* No margin between sections */
  position: relative;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Fix excessive vertical spacing between sections */
section + section {
  margin-top: 0 !important; /* Remove any margin between consecutive sections */
}

/* Adjust hero section height while maintaining equal spacing */
.hero-section {
  min-height: auto; /* Don't force full viewport height */
  padding: 4rem 1.5rem 3rem !important; /* Reduced from 8rem 1.5rem 5rem for tighter layout */
}

.hero-content {
  padding: var(--spacing-4); /* Reduce padding */
}

.hero-title {
  margin-bottom: 1rem !important; /* Reduced from 1.5rem for tighter spacing */
}

.hero-subtitle {
  margin-bottom: 1.5rem !important; /* Reduced from 2rem for tighter spacing */
}

.hero-cta {
  margin-top: 1.5rem !important; /* Reduced from 2rem for tighter spacing */
}

.hero-image {
  margin-top: var(--spacing-8); /* Reduce space between CTA and image */
}

.hero-video {
  margin-top: 2rem !important; /* Reduced from 3rem for tighter spacing */
}

/* Adjust section headers - REDUCED SPACING */
.section-header {
  margin-bottom: var(--spacing-16); /* Reduced from 32px to 16px for tighter layout */
  padding-bottom: var(--spacing-4); /* Reduced from 8px to 4px */
}

/* Service content spacing */
.service-content {
  gap: var(--spacing-16); /* Consistent gap */
  padding: 0 var(--spacing-4); /* Consistent horizontal padding */
}

/* Services grid spacing - REDUCED */
.services-grid {
  gap: var(--spacing-12); /* Reduced from 16px to 12px for tighter layout */
  margin-top: var(--spacing-16); /* Reduced from 32px to 16px */
}

/* Service card internal spacing */
.service-card {
  min-height: auto; /* Let content determine height */
  height: 100%; /* Maintain equal height in grid */
}

.service-features-list {
  margin-bottom: var(--spacing-16); /* Consistent space before footer/button */
}

/* Testimonials grid - REDUCED SPACING */
.testimonial-grid {
  gap: var(--spacing-12); /* Reduced from 16px to 12px for tighter layout */
  margin-top: var(--spacing-16); /* Reduced from 32px to 16px */
}

/* FAQ container - REDUCED SPACING */
.faq-container {
  padding: 0 var(--spacing-4); /* Consistent horizontal padding */
  margin-top: var(--spacing-16); /* Reduced from 32px to 16px */
}

.faq-item {
  margin-bottom: var(--spacing-16); /* Consistent space between items */
}

/* About content - REDUCED SPACING */
.about-content {
  gap: var(--spacing-12); /* Reduced from 16px to 12px for tighter layout */
  padding: 0 var(--spacing-4); /* Consistent horizontal padding */
  margin-top: var(--spacing-16); /* Reduced from 32px to 16px */
}

/* Demo container - REDUCED SPACING */
.demo-container {
  padding: 0 var(--spacing-4); /* Consistent horizontal padding */
  margin-top: var(--spacing-16); /* Reduced from 32px to 16px */
}

.demo-video {
  margin: var(--spacing-16) 0; /* Consistent margin */
}

/* Contact container - REDUCED SPACING */
.contact-container {
  padding: 0 var(--spacing-4); /* Consistent horizontal padding */
  gap: var(--spacing-12); /* Reduced from 16px to 12px for tighter layout */
  margin-top: var(--spacing-16); /* Reduced from 32px to 16px */
}

/* Remove extra whitespace from empty lines in HTML */
.hero-content > *:last-child,
.section-header > *:last-child,
.service-details > *:last-child,
.about-text > *:last-child,
.contact-form > *:last-child,
.contact-info > *:last-child {
  margin-bottom: 0;
}

/* Fix double spacing after sections */
section:empty {
  display: none;
}

/* Fix extra space between sections */
br + section,
section + br {
  display: none;
}

/* Additional spacing reductions for tighter layout */
.section-header h2 {
  margin-bottom: 0.5rem !important; /* Reduced from 0.8rem to 0.5rem */
}

.section-header p {
  margin-bottom: 0.75rem !important; /* Reduced margin for section descriptions */
}

/* Reduce spacing between paragraphs and CTA buttons */
.hero-cta {
  margin-top: 1.5rem !important; /* Reduced from default spacing */
}

.btn + .btn {
  margin-top: 0.5rem !important; /* Tighter spacing between buttons */
}

/* Reduce card internal padding */
.service-card-header {
  padding: var(--spacing-6) var(--spacing-12) var(--spacing-6) !important; /* Reduced padding */
}

.service-card-body {
  padding: var(--spacing-6) var(--spacing-12) !important; /* Reduced padding */
}

.service-card-footer {
  padding: var(--spacing-6) var(--spacing-12) var(--spacing-8) !important; /* Reduced padding */
}

.feature-card {
  padding: var(--spacing-16) !important; /* Reduced from var(--spacing-24) */
}

/* Reduce spacing in feature lists */
.service-features-list li {
  margin-bottom: var(--spacing-6) !important; /* Reduced from var(--spacing-8) */
}

/* Responsive adjustments - COMPACT MOBILE SPACING */
@media (max-width: 768px) {
  /* Compact spacing for all sections on mobile */
  section {
    padding: 32px 0 !important; /* Reduced from 40px to 32px for tighter mobile layout */
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* Further reduce mobile spacing */
  .section-header {
    margin-bottom: var(--spacing-12) !important; /* Further reduced for mobile */
  }

  .services-grid,
  .testimonial-grid {
    gap: var(--spacing-8) !important; /* Further reduced gap for mobile */
    margin-top: var(--spacing-12) !important; /* Further reduced top margin */
  }

  .feature-card {
    padding: var(--spacing-12) !important; /* Further reduced padding for mobile */
  }

  /* Mobile hero section adjustments */
  .hero-section {
    padding: 3rem 1rem 2rem !important; /* Further reduced for mobile */
  }

  .hero-title {
    margin-bottom: 0.75rem !important; /* Further reduced for mobile */
  }

  .hero-subtitle {
    margin-bottom: 1rem !important; /* Further reduced for mobile */
  }

  .hero-cta {
    margin-top: 1rem !important; /* Further reduced for mobile */
  }
}
