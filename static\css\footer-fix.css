/**
 * Footer Fix
 * This file removes all background colors from footer text in both light and dark mode
 */

/* Target all footer text elements */
footer p,
footer h3,
footer a,
footer li,
footer span,
footer div,
.footer-section p,
.footer-section h3,
.footer-section a,
.footer-section li,
.footer-section span,
.footer-section div,
.footer-content p,
.footer-content h3,
.footer-content a,
.footer-content li,
.footer-content span,
.footer-content div,
.footer-bottom p,
.footer-bottom span,
.footer-bottom div {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

/* Target the footer sections */
.footer-section {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Target the footer content */
.footer-content {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Target the footer bottom */
.footer-bottom {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border-top: 1px solid var(--color-border) !important;
}

/* Dark mode specific */
.dark-theme footer p,
.dark-theme footer h3,
.dark-theme footer a,
.dark-theme footer li,
.dark-theme footer span,
.dark-theme footer div,
.dark-theme .footer-section p,
.dark-theme .footer-section h3,
.dark-theme .footer-section a,
.dark-theme .footer-section li,
.dark-theme .footer-section span,
.dark-theme .footer-section div,
.dark-theme .footer-content p,
.dark-theme .footer-content h3,
.dark-theme .footer-content a,
.dark-theme .footer-content li,
.dark-theme .footer-content span,
.dark-theme .footer-content div,
.dark-theme .footer-bottom p,
.dark-theme .footer-bottom span,
.dark-theme .footer-bottom div {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

/* Completely disable navigation highlighting for footer links */
footer .footer-section a,
footer .footer-section a.active,
footer .footer-section a:hover,
footer .footer-section a:focus,
footer .footer-section a:visited,
.dark-theme footer .footer-section a,
.dark-theme footer .footer-section a.active,
.dark-theme footer .footer-section a:hover,
.dark-theme footer .footer-section a:focus,
.dark-theme footer .footer-section a:visited {
  color: var(--color-text-light) !important;
  font-weight: normal !important;
  text-decoration: none !important;
  border-bottom: none !important;
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
  border: none !important;
  outline: none !important;
}

footer .footer-section a:hover,
.dark-theme footer .footer-section a:hover {
  color: var(--color-primary) !important;
  text-decoration: none !important;
}

/* Target the footer sections in dark mode */
.dark-theme .footer-section {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Target the footer content in dark mode */
.dark-theme .footer-content {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Target the footer bottom in dark mode */
.dark-theme .footer-bottom {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border-top: 1px solid var(--color-border-dark) !important;
}
