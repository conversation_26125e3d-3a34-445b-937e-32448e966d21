/* Enhanced CTA Section Styles */
.cta-section {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(25, 118, 210, 0.1) 100%);
    padding: 60px 0;
    margin: 40px 0;
    position: relative;
    overflow: hidden;
}

.dark-theme .cta-section {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.08) 0%, rgba(33, 150, 243, 0.15) 100%);
}

.cta-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.cta-content {
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.cta-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 30px;
    color: #1a202c;
    line-height: 1.3;
}

.dark-theme .cta-title {
    color: #f8fafc;
}

.cta-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.cta-feature {
    display: flex;
    align-items: flex-start;
    text-align: left;
    padding: 15px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.cta-feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.dark-theme .cta-feature {
    background-color: rgba(45, 55, 72, 0.7);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dark-theme .cta-feature:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
}

.cta-feature i {
    font-size: 1.5rem;
    color: #1976d2;
    margin-right: 15px;
    background-color: rgba(25, 118, 210, 0.1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    flex-shrink: 0;
}

.dark-theme .cta-feature i {
    color: #64b5f6;
    background-color: rgba(33, 150, 243, 0.2);
}

.cta-feature span {
    font-size: 1.05rem;
    color: #4a5568;
    line-height: 1.5;
    font-weight: 500;
}

.dark-theme .cta-feature span {
    color: #e2e8f0;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-top: 20px;
}

.cta-button {
    display: inline-flex;
    align-items: center;
    padding: 14px 28px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.05rem;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.cta-button:hover::before {
    transform: translateX(0);
}

.cta-button.primary {
    background-color: #1976d2;
    color: white;
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
}

.cta-button.primary:hover {
    background-color: #1565c0;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(25, 118, 210, 0.3);
}

.cta-button.secondary {
    background-color: transparent;
    color: #1976d2;
    border: 2px solid #1976d2;
}

.cta-button.secondary:hover {
    background-color: rgba(25, 118, 210, 0.08);
    transform: translateY(-3px);
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.1);
}

.dark-theme .cta-button.primary {
    background-color: #2196f3;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.dark-theme .cta-button.primary:hover {
    background-color: #1e88e5;
}

.dark-theme .cta-button.secondary {
    color: #64b5f6;
    border-color: #64b5f6;
}

.dark-theme .cta-button.secondary:hover {
    background-color: rgba(100, 181, 246, 0.1);
}

.button-text {
    position: relative;
    z-index: 1;
}

.button-icon {
    margin-left: 10px;
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.cta-button:hover .button-icon {
    transform: translateX(5px);
}

@media (max-width: 768px) {
    .cta-title {
        font-size: 1.8rem;
    }
    
    .cta-features {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
    }
    
    .cta-button {
        width: 100%;
        justify-content: center;
    }
}
