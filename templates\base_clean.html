<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="robots" content="index, follow">
    <meta name="author" content="Ziantrix">
    <meta name="theme-color" content="#4f46e5">

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://calendar.app.google" crossorigin>
    <link rel="dns-prefetch" href="https://calendar.app.google">

    <title>{% block title %}Ziantrix - AI Co-Workers for Your Business - No Tech Team Needed{% endblock %}</title>

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Critical Path CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/critical-path.css') }}">

    <!-- Core Styles -->
    <style>
        :root {
          --color-primary: #4f46e5;
          --color-primary-light: #818cf8;
          --color-primary-dark: #3730a3;
          --color-accent: #a855f7;
          --color-bg-light: #ffffff;
          --color-bg-dark: #0f172a;
          --color-text-light: #111827;
          --color-text-dark: #f8fafc;
        }

        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', system-ui, sans-serif;
            overflow-x: hidden;
        }

        /* Clean Navigation Styles */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 50;
            background-color: #f8fafc;
            height: 60px;
            border-bottom: 1px solid #e2e8f0;
            backdrop-filter: blur(10px);
        }

        .dark-theme .navbar {
            background-color: #0f172a;
            border-bottom-color: #334155;
        }

        .navbar-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
            height: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }

        .navbar-logo {
            font-size: 1.5rem;
            font-weight: 800;
            color: var(--color-primary);
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .nav-links {
            display: flex;
            align-items: center;
            gap: 2rem;
            height: 100%;
        }

        .navbar-link {
            color: var(--color-text-light);
            text-decoration: none;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            height: 40px;
        }

        .navbar-link:hover {
            background-color: rgba(79, 70, 229, 0.1);
            color: var(--color-primary);
        }

        .navbar-link.active {
            background-color: var(--color-primary);
            color: white;
        }

        .dark-theme .navbar-link {
            color: var(--color-text-dark);
        }

        .dark-theme .navbar-link:hover {
            background-color: rgba(129, 140, 248, 0.1);
            color: var(--color-primary-light);
        }

        /* Mobile Menu */
        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            cursor: pointer;
            padding: 0.5rem;
        }

        .mobile-menu-toggle span {
            width: 25px;
            height: 3px;
            background-color: var(--color-primary);
            margin: 3px 0;
            transition: 0.3s;
        }

        /* Theme Toggle */
        .theme-toggle {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 6px;
            transition: background-color 0.2s ease;
        }

        .theme-toggle:hover {
            background-color: rgba(79, 70, 229, 0.1);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .navbar-container {
                padding: 0 1rem;
            }

            .nav-links {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background-color: inherit;
                flex-direction: column;
                padding: 1rem;
                border-bottom: 1px solid #e2e8f0;
            }

            .nav-links.active {
                display: flex;
            }

            .mobile-menu-toggle {
                display: flex;
            }

            .navbar-link {
                width: 100%;
                justify-content: center;
                padding: 1rem;
            }
        }

        /* Content spacing for fixed navbar */
        .main-content {
            margin-top: 60px;
        }
    </style>

    <!-- Enhanced Theme System -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-theme.css') }}">

    <!-- Main Styles -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhancements.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced-chatbot.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/chatbot.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/form-enhancements.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/button-alignment-fix.css') }}">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='img/favicon.png') }}">

    <!-- Meta Description -->
    <meta name="description" content="{% block meta_description %}Ziantrix provides AI-powered chatbot solutions for businesses of all sizes. Enhance customer support with our advanced AI technology.{% endblock %}">

    {% block extra_head %}{% endblock %}
</head>
<body>
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="flash-message {{ category }}">
                        <span class="message-text">{{ message }}</span>
                        <button class="close-btn">&times;</button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Clean Navigation Bar -->
    <nav class="navbar">
        <div class="navbar-container">
            <!-- Logo -->
            <a href="{{ url_for('index') }}" class="navbar-logo">
                Ziantrix
            </a>

            <!-- Mobile Menu Toggle -->
            <div class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </div>

            <!-- Navigation Links -->
            <div class="nav-links" id="navLinks">
                <a href="/#features" class="navbar-link" data-target="features" onclick="navigateTo('/#features'); return false;">Why Ziantrix</a>
                <a href="/#services" class="navbar-link" data-target="services" onclick="navigateTo('/#services'); return false;">Services</a>
                <a href="/#solutions" class="navbar-link" data-target="solutions" onclick="navigateTo('/#solutions'); return false;">Industries</a>
                <a href="/#faq" class="navbar-link" data-target="faq" onclick="navigateTo('/#faq'); return false;">FAQ</a>
                <a href="/#about" class="navbar-link" data-target="about" onclick="navigateTo('/#about'); return false;">About</a>
                <a href="/#contact" class="navbar-link" data-target="contact" onclick="navigateTo('/#contact'); return false;">Contact</a>

                <!-- Theme Toggle -->
                <button class="theme-toggle" onclick="toggleTheme()" aria-label="Toggle dark mode">
                    <span id="themeIcon">🌙</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        {% block header_content %}{% endblock %}

        <main>
            {% block content %}{% endblock %}
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="footer-container">
                <div class="footer-content">
                    <div class="footer-section">
                        <h3>Ziantrix</h3>
                        <p>AI Co-Workers for Your Business</p>
                    </div>
                    <div class="footer-section">
                        <h4>Services</h4>
                        <ul>
                            <li><a href="/#services">Chatbot Services</a></li>
                            <li><a href="/#services">AI Services</a></li>
                            <li><a href="/#services">AI Agents</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4>Company</h4>
                        <ul>
                            <li><a href="/#about">About</a></li>
                            <li><a href="/#contact">Contact</a></li>
                            <li><a href="/support">Support</a></li>
                        </ul>
                    </div>
                </div>
                <div class="footer-bottom">
                    <p>&copy; 2024 Ziantrix. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </div>

    <!-- Chatbot -->
    <div id="chatbot-container" class="chatbot-container">
        <div class="chatbot-header">
            <span>Ziantrix AI Assistant</span>
            <div class="chatbot-controls">
                <button onclick="minimizeChatbot()" title="Minimize"><i class="fas fa-minus"></i></button>
                <button onclick="closeChatbot()" title="Close"><i class="fas fa-times"></i></button>
            </div>
        </div>
        <div class="chatbot-messages" id="chatbot-messages">
            <div class="message bot-message">
                <div class="message-content">
                    Hello! I'm here to help you learn about Ziantrix's AI solutions. How can I assist you today?
                </div>
            </div>
        </div>
        <div class="chatbot-input">
            <input type="text" id="chatbot-input-field" placeholder="Type your message..." onkeypress="if(event.key==='Enter') sendMessage()">
            <button onclick="sendMessage()"><i class="fas fa-paper-plane"></i></button>
        </div>
    </div>

    <!-- Chatbot Toggle Button -->
    <button id="chatbot-toggle" class="chatbot-toggle" onclick="toggleChatbot()">
        <i class="fas fa-comments"></i>
    </button>

    <!-- Core JavaScript -->
    <script>
        // Navigation functionality
        function navigateTo(url) {
            if (url.startsWith('/#')) {
                const sectionId = url.substring(2);
                const targetSection = document.getElementById(sectionId);
                if (targetSection) {
                    const headerOffset = 80;
                    const elementPosition = targetSection.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });

                    // Update URL hash
                    history.pushState(null, null, url);

                    // Update active nav item
                    document.querySelectorAll('.navbar-link').forEach(item => {
                        item.classList.remove('active');
                        if (item.getAttribute('data-target') === sectionId) {
                            item.classList.add('active');
                        }
                    });
                } else {
                    window.location.href = url;
                }
            } else {
                window.location.href = url;
            }
        }

        // Mobile menu toggle
        function toggleMobileMenu() {
            const navLinks = document.getElementById('navLinks');
            navLinks.classList.toggle('active');
        }

        // Theme toggle
        function toggleTheme() {
            const body = document.body;
            const themeIcon = document.getElementById('themeIcon');

            body.classList.toggle('dark-theme');

            if (body.classList.contains('dark-theme')) {
                themeIcon.textContent = '☀️';
                localStorage.setItem('theme', 'dark');
            } else {
                themeIcon.textContent = '🌙';
                localStorage.setItem('theme', 'light');
            }
        }

        // Load saved theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme');
            const themeIcon = document.getElementById('themeIcon');

            if (savedTheme === 'dark') {
                document.body.classList.add('dark-theme');
                themeIcon.textContent = '☀️';
            }

            // Highlight current section on scroll
            window.addEventListener('scroll', function() {
                const sections = document.querySelectorAll('section[id]');
                const navLinks = document.querySelectorAll('.navbar-link[data-target]');

                let current = '';
                sections.forEach(section => {
                    const sectionTop = section.getBoundingClientRect().top;
                    if (sectionTop <= 100) {
                        current = section.getAttribute('id');
                    }
                });

                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('data-target') === current) {
                        link.classList.add('active');
                    }
                });
            });

            // Flash message close functionality
            document.querySelectorAll('.close-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    this.parentElement.style.display = 'none';
                });
            });

            // Auto-hide flash messages after 5 seconds
            setTimeout(() => {
                document.querySelectorAll('.flash-message').forEach(msg => {
                    msg.style.opacity = '0';
                    setTimeout(() => msg.style.display = 'none', 300);
                });
            }, 5000);
        });

        // Chatbot functionality
        let chatbotOpen = false;

        function toggleChatbot() {
            const container = document.getElementById('chatbot-container');
            const toggle = document.getElementById('chatbot-toggle');

            chatbotOpen = !chatbotOpen;

            if (chatbotOpen) {
                container.style.display = 'flex';
                toggle.style.display = 'none';
                scrollChatToBottom();
            } else {
                container.style.display = 'none';
                toggle.style.display = 'flex';
            }
        }

        function closeChatbot() {
            chatbotOpen = false;
            document.getElementById('chatbot-container').style.display = 'none';
            document.getElementById('chatbot-toggle').style.display = 'flex';
        }

        function minimizeChatbot() {
            closeChatbot();
        }

        function scrollChatToBottom() {
            const messages = document.getElementById('chatbot-messages');
            messages.scrollTop = messages.scrollHeight;
        }

        function sendMessage() {
            const input = document.getElementById('chatbot-input-field');
            const message = input.value.trim();

            if (!message) return;

            // Add user message
            addMessage(message, 'user');
            input.value = '';

            // Send to API
            fetch('/api/chatbot', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.response) {
                    addMessage(data.response, 'bot');
                } else {
                    addMessage('Sorry, I encountered an error. Please try again.', 'bot');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                addMessage('Sorry, I encountered an error. Please try again.', 'bot');
            });
        }

        function addMessage(content, sender) {
            const messages = document.getElementById('chatbot-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;

            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            contentDiv.textContent = content;

            messageDiv.appendChild(contentDiv);
            messages.appendChild(messageDiv);

            scrollChatToBottom();
        }

        // Demo modal functionality (if needed)
        function openDemoModal() {
            // This function can be implemented if demo modal is needed
            console.log('Demo modal would open here');
        }
    </script>

    <!-- Additional Scripts -->
    <script src="{{ url_for('static', filename='js/chatbot.js') }}" defer></script>
    <script src="{{ url_for('static', filename='js/form-submission.js') }}" defer></script>

    {% block scripts %}{% endblock %}
</body>
</html>
