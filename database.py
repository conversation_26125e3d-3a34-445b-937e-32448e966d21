"""
Database configuration and utility functions for Ziantrix Dynamic App
"""
import os
import logging
from datetime import datetime
from sqlalchemy import text
from models import db, FormSubmission, ChatConversation, ChatMessage, Analytics

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database manager for handling all database operations"""

    def __init__(self, app=None):
        self.app = app
        if app:
            self.init_app(app)

    def init_app(self, app):
        """Initialize database with Flask app"""
        self.app = app

        # Debug: Log available environment variables
        logger.info(f"Available DATABASE_URL: {os.environ.get('DATABASE_URL', 'NOT_FOUND')}")
        logger.info(f"Available Postgres.DATABASE_URL: {os.environ.get('Postgres.DATABASE_URL', 'NOT_FOUND')}")

        # Configure database URL
        database_url = os.environ.get('DATABASE_URL') or os.environ.get('Postgres.DATABASE_URL')

        # Validate database URL
        if not database_url or database_url.strip() in ['', '}', '{', 'NOT_FOUND']:
            # Fallback to local SQLite for development
            database_url = 'sqlite:///ziantrix_app.db'
            logger.warning("No valid PostgreSQL DATABASE_URL found, using SQLite fallback")
        elif len(database_url.strip()) < 10:  # Basic validation for minimum URL length
            logger.warning(f"Invalid DATABASE_URL format: '{database_url}', using SQLite fallback")
            database_url = 'sqlite:///ziantrix_app.db'

        # Handle Railway/Heroku postgres URL format
        if database_url.startswith('postgres://'):
            database_url = database_url.replace('postgres://', 'postgresql://', 1)

        app.config['SQLALCHEMY_DATABASE_URI'] = database_url
        app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
        app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
            'pool_pre_ping': True,
            'pool_recycle': 300,
            'pool_timeout': 20,
            'max_overflow': 0
        }

        # Initialize extensions
        db.init_app(app)

        logger.info(f"Database configured with URL: {database_url[:50]}...")

    def create_tables(self):
        """Create all database tables"""
        try:
            with self.app.app_context():
                db.create_all()
                logger.info("✅ Database tables created successfully")
                return True
        except Exception as e:
            logger.error(f"❌ Error creating database tables: {str(e)}")
            return False

    def test_connection(self):
        """Test database connection"""
        try:
            with self.app.app_context():
                db.session.execute(text('SELECT 1'))
                logger.info("✅ Database connection successful")
                return True
        except Exception as e:
            logger.error(f"❌ Database connection failed: {str(e)}")
            return False

    # JSON migration removed - authentication disabled

# User management functions removed - authentication disabled

# Form submission functions
class FormManager:
    """Manager for form submission operations"""

    @staticmethod
    def save_form_submission(form_type, name, email, message=None, **kwargs):
        """Save form submission to database"""
        try:
            # Anonymous submissions only - authentication disabled
            submission = FormSubmission(
                form_type=form_type,
                name=name,
                email=email,
                message=message,
                company=kwargs.get('company'),
                phone=kwargs.get('phone'),
                inquiry_type=kwargs.get('inquiry_type'),
                source_page=kwargs.get('source_page'),
                ip_address=kwargs.get('ip_address'),
                user_agent=kwargs.get('user_agent'),
                additional_data=kwargs.get('additional_data')
            )

            db.session.add(submission)
            db.session.commit()

            logger.info(f"Saved {form_type} form submission from {email}")
            return submission, None

        except Exception as e:
            db.session.rollback()
            logger.error(f"Error saving form submission: {str(e)}")
            return None, "Failed to save form submission"

    # User-specific submission retrieval removed - authentication disabled

# Chat conversation functions
class ChatManager:
    """Manager for chat conversation operations"""

    @staticmethod
    def create_conversation(session_id, user_id=None, device_id=None):
        """Create a new chat conversation (anonymous only)"""
        try:
            conversation = ChatConversation(
                session_id=session_id,
                device_id=device_id
            )

            db.session.add(conversation)
            db.session.commit()

            logger.info(f"Created chat conversation: {session_id}")
            return conversation

        except Exception as e:
            db.session.rollback()
            logger.error(f"Error creating conversation: {str(e)}")
            return None

    @staticmethod
    def save_message(conversation_id, message_type, content, metadata=None):
        """Save a chat message"""
        try:
            message = ChatMessage(
                conversation_id=conversation_id,
                message_type=message_type,
                content=content,
                message_metadata=metadata
            )

            db.session.add(message)
            db.session.commit()

            return message

        except Exception as e:
            db.session.rollback()
            logger.error(f"Error saving chat message: {str(e)}")
            return None

# Initialize database manager
db_manager = DatabaseManager()
