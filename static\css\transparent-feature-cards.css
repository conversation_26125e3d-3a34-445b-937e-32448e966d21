/**
 * Transparent Feature Cards
 * Completely removes all white backgrounds from feature cards and their elements
 */

/* Feature Card Base */
.feature-card {
  background-color: transparent !important;
  border: 1px solid var(--color-border) !important;
  box-shadow: none !important;
}

.dark-theme .feature-card {
  background-color: transparent !important;
  border: 1px solid var(--color-border) !important;
  box-shadow: none !important;
}

/* Feature Card Elements - Aggressive Targeting */
.feature-card .feature-title,
.feature-card .feature-description,
.feature-card .feature-link,
.feature-card .feature-metric,
.feature-card .feature-metric-label,
.feature-card .feature-proof-point,
.feature-card .metric-wrapper,
.feature-card > div,
.feature-card > p,
.feature-card > span,
.feature-card [class*="metric"],
.feature-card [class*="feature"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  border-radius: 0 !important;
}

/* Target the specific elements shown in the screenshot */
.feature-card .feature-metric {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin-top: 1rem !important;
  margin-bottom: 0.25rem !important;
  text-align: center !important;
  padding: 0 !important;
  width: auto !important;
  max-width: 100% !important;
  display: block !important;
}

.feature-card .feature-metric-label {
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  letter-spacing: 0.05em !important;
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
  text-align: center !important;
  padding: 0 !important;
  width: auto !important;
  max-width: 100% !important;
  display: block !important;
}

.feature-card .feature-proof-point {
  font-size: 0.875rem !important;
  font-style: italic !important;
  margin-top: 0.25rem !important;
  margin-bottom: 1rem !important;
  text-align: center !important;
  padding: 0 !important;
  width: auto !important;
  max-width: 100% !important;
  display: block !important;
}

/* Light Mode Metrics - Extremely Specific Selectors */
body:not(.dark-theme) .feature-card .metric-wrapper,
html body:not(.dark-theme) .feature-card .metric-wrapper,
:not(.dark-theme) .feature-card .metric-wrapper,
body:not(.dark-theme) div.feature-card div.metric-wrapper,
body:not(.dark-theme) .feature-card div[class*="metric"],
body:not(.dark-theme) .feature-card *[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 auto !important;
  width: auto !important;
}

body:not(.dark-theme) .feature-card .feature-metric,
html body:not(.dark-theme) .feature-card .feature-metric,
:not(.dark-theme) .feature-card .feature-metric,
body:not(.dark-theme) div.feature-card div.metric-wrapper div.feature-metric,
body:not(.dark-theme) .feature-card div.feature-metric,
body:not(.dark-theme) .feature-card > .feature-metric {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  color: #1976d2 !important;
  padding: 0 !important;
}

body:not(.dark-theme) .feature-card .feature-metric-label,
html body:not(.dark-theme) .feature-card .feature-metric-label,
:not(.dark-theme) .feature-card .feature-metric-label,
body:not(.dark-theme) div.feature-card div.metric-wrapper p.feature-metric-label,
body:not(.dark-theme) .feature-card p.feature-metric-label,
body:not(.dark-theme) .feature-card > .feature-metric-label {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  color: #333333 !important;
  padding: 0 !important;
}

/* Dark Mode Metrics - Extremely Specific Selectors */
body.dark-theme .feature-card .metric-wrapper,
html body.dark-theme .feature-card .metric-wrapper,
.dark-theme .feature-card .metric-wrapper,
body.dark-theme div.feature-card div.metric-wrapper,
body.dark-theme .feature-card div[class*="metric"],
body.dark-theme .feature-card *[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 auto !important;
  width: auto !important;
}

body.dark-theme .feature-card .feature-metric,
html body.dark-theme .feature-card .feature-metric,
.dark-theme .feature-card .feature-metric,
body.dark-theme div.feature-card div.metric-wrapper div.feature-metric,
body.dark-theme .feature-card div.feature-metric,
body.dark-theme .feature-card > .feature-metric {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  color: #64b5f6 !important;
  text-shadow: 0 0 10px rgba(100, 181, 246, 0.4) !important;
  padding: 0 !important;
}

body.dark-theme .feature-card .feature-metric-label,
html body.dark-theme .feature-card .feature-metric-label,
.dark-theme .feature-card .feature-metric-label,
body.dark-theme div.feature-card div.metric-wrapper p.feature-metric-label,
body.dark-theme .feature-card p.feature-metric-label,
body.dark-theme .feature-card > .feature-metric-label {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  color: #e0e0e0 !important;
  padding: 0 !important;
}

/* Target any inline styles */
.feature-card [style*="background"],
.feature-card [style*="background-color"],
.feature-card [style*="box-shadow"],
.feature-card [style*="border"] {
  background: transparent !important;
  background-color: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

/* Target any pseudo-elements */
.feature-card *::before,
.feature-card *::after {
  background-color: transparent !important;
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

/* Feature Card Hover */
.feature-card:hover {
  background-color: transparent !important;
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
  border-color: var(--color-primary-200) !important;
}

.dark-theme .feature-card:hover {
  background-color: transparent !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3) !important;
  border-color: var(--color-primary-700) !important;
}

/* Override modern-feature-grid.css */
.feature-card {
  background-color: transparent !important;
}

.feature-card:hover {
  background-color: transparent !important;
}

.dark-theme .feature-card {
  background-color: transparent !important;
}

.dark-theme .feature-card:hover {
  background-color: transparent !important;
}
