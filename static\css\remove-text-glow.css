/**
 * Remove Text Glow Effects
 * Removes all text shadow, glow, and similar effects from text elements
 */

/* Remove text glow from all text elements */
h1, h2, h3, h4, h5, h6,
p, span, a, li, div,
.hero-title, .section-title, .feature-title, .solution-title, .service-title,
.hero-subtitle, .section-description, .feature-description, .solution-description, .service-subtitle,
.feature-metric, .feature-metric-label, .feature-proof-point,
.benefit-text, .check-icon,
.navbar-link, .navbar-logo, .dropdown-item,
.btn, .cta-btn, .service-cta-button, .feature-link,

.faq-question, .faq-answer,
.footer-content h3, .footer-content p, .footer-content a,
.login-form label, .login-form input, .login-form button,
.flash-message, .message-text,
.metric-value, .metric-label, .metric-proof {
  text-shadow: none !important;
  box-shadow: none !important;
  -webkit-text-shadow: none !important;
  -moz-text-shadow: none !important;
}

/* Remove glow from dark theme elements */
.dark-theme h1, .dark-theme h2, .dark-theme h3, .dark-theme h4, .dark-theme h5, .dark-theme h6,
.dark-theme p, .dark-theme span, .dark-theme a, .dark-theme li, .dark-theme div,
.dark-theme .hero-title, .dark-theme .section-title, .dark-theme .feature-title, .dark-theme .solution-title, .dark-theme .service-title,
.dark-theme .hero-subtitle, .dark-theme .section-description, .dark-theme .feature-description, .dark-theme .solution-description, .dark-theme .service-subtitle,
.dark-theme .feature-metric, .dark-theme .feature-metric-label, .dark-theme .feature-proof-point,
.dark-theme .benefit-text, .dark-theme .check-icon,
.dark-theme .navbar-link, .dark-theme .navbar-logo, .dark-theme .dropdown-item,
.dark-theme .btn, .dark-theme .cta-btn, .dark-theme .service-cta-button, .dark-theme .feature-link,

.dark-theme .faq-question, .dark-theme .faq-answer,
.dark-theme .footer-content h3, .dark-theme .footer-content p, .dark-theme .footer-content a,
.dark-theme .login-form label, .dark-theme .login-form input, .dark-theme .login-form button,
.dark-theme .flash-message, .dark-theme .message-text,
.dark-theme .metric-value, .dark-theme .metric-label, .dark-theme .metric-proof {
  text-shadow: none !important;
  box-shadow: none !important;
  -webkit-text-shadow: none !important;
  -moz-text-shadow: none !important;
}

/* Remove specific glow effects from feature metrics */
.feature-card .feature-metric,
.dark-theme .feature-card .feature-metric {
  text-shadow: none !important;
  -webkit-text-shadow: none !important;
  -moz-text-shadow: none !important;
}

/* Remove glow from solution titles */
.solution-card:hover .solution-title,
.dark-theme .solution-card:hover .solution-title {
  text-shadow: none !important;
  -webkit-text-shadow: none !important;
  -moz-text-shadow: none !important;
}

/* Remove gradient text effects */
.hero-title, .navbar-logo, .section-title::after {
  background: none !important;
  -webkit-background-clip: initial !important;
  background-clip: initial !important;
  color: var(--color-text-primary, #0f172a) !important;
}

.dark-theme .hero-title, .dark-theme .navbar-logo, .dark-theme .section-title::after {
  background: none !important;
  -webkit-background-clip: initial !important;
  background-clip: initial !important;
  color: var(--color-text-primary, #f8fafc) !important;
}

/* Restore specific colors for important elements */
.navbar-logo {
  color: var(--color-primary-600, #2563eb) !important;
}

.dark-theme .navbar-logo {
  color: var(--color-primary-400, #60a5fa) !important;
}

.hero-title {
  color: var(--color-text-primary, #0f172a) !important;
}

.dark-theme .hero-title {
  color: var(--color-text-primary, #f8fafc) !important;
}

/* Remove animation that might cause text glow */
@keyframes textGradient {
  0%, 100% { background-position: 0% 50%; }
}

.hero-title, .section-title, .feature-title, .solution-title {
  animation: none !important;
  -webkit-animation: none !important;
  -moz-animation: none !important;
}
