/**
 * Final Metric Background Fix
 * This is the most aggressive approach to remove all backgrounds from feature metrics
 * It uses !important flags, attribute selectors, and direct targeting of elements
 */

/* Universal reset for all feature metrics */
[class*="feature-metric"],
[class*="metric-label"],
[class*="proof-point"],
[class*="metric-wrapper"],
[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

/* Target the feature metrics with every possible selector */
.feature-card .feature-metric,
.feature-card div.feature-metric,
.feature-card > .feature-metric,
div.feature-card div.feature-metric,
.feature-card .feature-metric[class],
.feature-card .feature-metric[style],
.feature-metric,
div.feature-metric,
.feature-metric[class],
.feature-metric[style],
*[class="feature-metric"],
*[class*="feature-metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

/* Target the feature metric labels with every possible selector */
.feature-card .feature-metric-label,
.feature-card p.feature-metric-label,
.feature-card > .feature-metric-label,
div.feature-card p.feature-metric-label,
.feature-card .feature-metric-label[class],
.feature-card .feature-metric-label[style],
.feature-metric-label,
p.feature-metric-label,
.feature-metric-label[class],
.feature-metric-label[style],
*[class="feature-metric-label"],
*[class*="feature-metric-label"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

/* Target the feature proof points with every possible selector */
.feature-card .feature-proof-point,
.feature-card p.feature-proof-point,
.feature-card > .feature-proof-point,
div.feature-card p.feature-proof-point,
.feature-card .feature-proof-point[class],
.feature-card .feature-proof-point[style],
.feature-proof-point,
p.feature-proof-point,
.feature-proof-point[class],
.feature-proof-point[style],
*[class="feature-proof-point"],
*[class*="feature-proof-point"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

/* Target any container that might have a background */
.feature-card .metric-wrapper,
.feature-card div.metric-wrapper,
.feature-card > div,
div.feature-card div.metric-wrapper,
.feature-card [class*="metric"],
.feature-card *[class*="metric"],
.metric-wrapper,
div.metric-wrapper,
.metric-wrapper[class],
.metric-wrapper[style],
*[class="metric-wrapper"],
*[class*="metric-wrapper"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

/* Target the second feature card specifically */
.feature-card:nth-child(2) .feature-metric,
.feature-card:nth-child(2) .feature-metric-label,
.feature-card:nth-child(2) .feature-proof-point,
.feature-card:nth-child(2) .metric-wrapper,
.feature-card:nth-child(2) [class*="metric"],
.feature-card:nth-child(2) *[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
}

/* Target all feature cards */
.feature-card {
  background-color: transparent !important;
}

.feature-card:hover {
  background-color: transparent !important;
}

/* Ensure dark mode is also covered */
.dark-theme .feature-card .feature-metric,
.dark-theme .feature-card .feature-metric-label,
.dark-theme .feature-card .feature-proof-point,
.dark-theme .feature-card .metric-wrapper,
.dark-theme .feature-card [class*="metric"],
.dark-theme .feature-card *[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* Override any inline styles */
[style*="background"],
[style*="background-color"] {
  background: transparent !important;
  background-color: transparent !important;
}

/* Target any pseudo-elements */
.feature-card *::before,
.feature-card *::after {
  background-color: transparent !important;
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}
