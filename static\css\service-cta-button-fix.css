/**
 * Service CTA Button Fix
 * This CSS ensures service CTA buttons display their full text without ellipsis
 */

/* Target all service CTA buttons */
.service-cta-button {
    white-space: normal !important;
    overflow: visible !important;
    text-overflow: clip !important;
    word-break: normal !important;
    word-wrap: normal !important;
    hyphens: none !important;
    max-width: 100% !important;
    width: 100% !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    text-align: center !important;
    padding: 14px 20px !important;
    line-height: 1.2 !important;
    min-height: 48px !important;
}

/* Target specific buttons by their text content using attribute selectors */
.service-cta-button[onclick*="openDemoModal"] {
    white-space: normal !important;
    overflow: visible !important;
    text-overflow: clip !important;
}

/* Ensure buttons in service cards have proper styling */
.service-card .service-card-footer .service-cta-button {
    white-space: normal !important;
    overflow: visible !important;
    text-overflow: clip !important;
    word-break: normal !important;
    word-wrap: normal !important;
    hyphens: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .service-cta-button {
        font-size: 0.95rem !important;
        padding: 12px 16px !important;
    }
}
