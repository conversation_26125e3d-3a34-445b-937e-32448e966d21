/**
 * Service Card Button Display Fix
 * This CSS ensures the service card buttons are always visible
 */

/* Ensure service cards have proper height */
.service-card {
  min-height: 500px !important;
  height: auto !important;
  display: flex !important;
  flex-direction: column !important;
  position: relative !important;
  padding-bottom: 100px !important; /* Make room for the footer */
  box-sizing: border-box !important;
}

/* Ensure service card body doesn't push footer out of view */
.service-card-body {
  flex: 1 !important;
  padding-bottom: 80px !important; /* Space for footer */
  position: relative !important;
  z-index: 1 !important;
}

/* Position footer at the bottom of the card */
.service-card-footer {
  position: absolute !important;
  bottom: 24px !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  padding: 0 24px 24px !important;
  box-sizing: border-box !important;
  z-index: 10 !important;
  background-color: var(--color-card-bg) !important;
  margin-top: auto !important;
}

/* Add a solid background behind the button */
.service-card-footer::before {
  content: "" !important;
  position: absolute !important;
  top: -20px !important;
  left: 0 !important;
  right: 0 !important;
  height: 20px !important;
  background-color: var(--color-card-bg) !important;
  z-index: -1 !important;
}

/* Style the button */
.service-cta-button {
  display: block !important;
  width: 100% !important;
  min-width: 260px !important;
  padding: 14px 20px !important;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  text-align: center !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  border: none !important;
  cursor: pointer !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
  z-index: 20 !important;
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
  box-sizing: border-box !important;
}

.service-cta-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15) !important;
}

/* Dark theme adjustments */
.dark-theme .service-card-footer,
.dark-theme .service-card-footer::before {
  background-color: var(--color-card-bg-dark) !important;
}

/* Force display of buttons */
.service-card-footer {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.service-cta-button {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}
