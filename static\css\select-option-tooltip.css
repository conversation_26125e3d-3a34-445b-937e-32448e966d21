/**
 * Select Option Tooltip
 * Ensures tooltips on select options are properly displayed
 */

/* Make sure option titles (tooltips) are visible on hover */
select option:hover {
    cursor: pointer;
}

/* Ensure the title attribute is visible in modern browsers */
select option[title] {
    position: relative;
}

/* Add a custom tooltip for select options with title attributes */
.select-wrapper {
    position: relative;
}

/* Add a tooltip class to be used by JavaScript */
.select-tooltip {
    position: absolute;
    background-color: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 13px;
    z-index: 1000;
    max-width: 250px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
    bottom: 100%;
    left: 0;
    margin-bottom: 10px;
}

/* Dark theme adjustments */
.dark-theme .select-tooltip {
    background-color: #1e293b;
    color: #e2e8f0;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4);
}

/* Arrow for the tooltip */
.select-tooltip:after {
    content: "";
    position: absolute;
    top: 100%;
    left: 20px;
    border-width: 6px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

.dark-theme .select-tooltip:after {
    border-color: #1e293b transparent transparent transparent;
}
