/**
 * Flash Message Handler
 * Ensures consistent behavior for flash messages across the site
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize flash messages
    initFlashMessages();
    
    // Function to initialize flash messages
    function initFlashMessages() {
        // Get all flash messages
        const flashMessages = document.querySelectorAll('.flash-message');
        
        // Add event listeners to close buttons
        flashMessages.forEach(message => {
            const closeBtn = message.querySelector('.close-btn');
            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    message.classList.add('fade-out');
                    setTimeout(() => {
                        message.style.display = 'none';
                    }, 300);
                });
            }
            
            // Auto-hide flash messages after 5 seconds
            setTimeout(() => {
                message.classList.add('fade-out');
                setTimeout(() => {
                    message.style.display = 'none';
                }, 300);
            }, 5000);
        });
    }
    
    // Function to show a notification
    window.showNotification = function(message, type) {
        // Check if flash messages container exists
        let flashContainer = document.querySelector('.flash-messages');
        
        if (!flashContainer) {
            // Create container if it doesn't exist
            flashContainer = document.createElement('div');
            flashContainer.className = 'flash-messages';
            document.body.appendChild(flashContainer);
        }
        
        // Create notification
        const notification = document.createElement('div');
        notification.className = `flash-message ${type}`;
        notification.innerHTML = `
            <span class="message-text">${message}</span>
            <button class="close-btn">&times;</button>
        `;
        
        // Add to container
        flashContainer.appendChild(notification);
        
        // Add close button functionality
        const closeButton = notification.querySelector('.close-btn');
        closeButton.addEventListener('click', function() {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 300);
        });
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 5000);
        
        return notification;
    };
});
