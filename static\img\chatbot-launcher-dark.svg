<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 70 70">
  <defs>
    <linearGradient id="darkGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7C4DFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#448AFF;stop-opacity:1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle -->
  <circle cx="35" cy="35" r="34" fill="url(#darkGradient)" stroke="#448AFF" stroke-width="2" filter="url(#glow)"/>
  
  <!-- Robot head -->
  <rect x="20" y="22" width="30" height="26" rx="6" fill="white" opacity="0.9"/>
  
  <!-- Robot eyes -->
  <circle cx="27" cy="32" r="3" fill="#7C4DFF"/>
  <circle cx="43" cy="32" r="3" fill="#7C4DFF"/>
  
  <!-- Robot mouth -->
  <rect x="30" y="40" width="10" height="2" rx="1" fill="#7C4DFF"/>
  
  <!-- Robot antenna -->
  <line x1="35" y1="22" x2="35" y2="16" stroke="white" stroke-width="2" opacity="0.9"/>
  <circle cx="35" cy="14" r="2" fill="#FF4081"/>
  
  <!-- Chat bubble -->
  <circle cx="50" cy="20" r="8" fill="#FF4081" opacity="0.8"/>
  <path d="M46 24 L50 28 L54 24" fill="#FF4081" opacity="0.8"/>
  <text x="50" y="23" text-anchor="middle" fill="white" font-size="8" font-weight="bold">💬</text>
</svg>
