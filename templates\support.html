{% extends "base.html" %}

{% block title %}Support - Ziantrix{% endblock %}

{% block meta_description %}Get support for your Ziantrix AI chatbot solutions. Contact our team for assistance with implementation, customization, and troubleshooting.{% endblock %}

{% block content %}
<!-- Support Section -->
<section class="contact-section section-spacing">
    <div class="section-header">
        <h2>Get in Touch</h2>
        <p>Have questions? We're here to help.</p>
    </div>

    <div class="contact-container">
        <div class="contact-form-container">
            <form id="contactForm" action="/submit_form" method="POST" class="contact-form">
                <input type="hidden" name="source" value="support">

                <div class="form-group">
                    <label for="name">Your Name</label>
                    <input type="text" id="name" name="name" placeholder="Enter your name" required>
                    <span class="error-message" id="nameError"></span>
                </div>

                <div class="form-group">
                    <label for="email">Email Address</label>
                    <input type="email" id="email" name="email" placeholder="Enter your email" required>
                    <span class="error-message" id="emailError"></span>
                </div>

                <div class="form-group">
                    <label for="company">Company</label>
                    <input type="text" id="company" name="company" placeholder="Enter your company" required>
                    <span class="error-message" id="companyError"></span>
                </div>

                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea id="message" name="message" placeholder="How can we help you?" rows="3" required></textarea>
                    <span class="error-message" id="messageError"></span>
                </div>

                <button type="submit" class="submit-btn">SEND MESSAGE</button>

                <div id="formConfirmation" class="form-confirmation" style="display: none;">
                    <div class="confirmation-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h3>Thank You!</h3>
                    <p>Your message has been sent successfully. We'll get back to you soon.</p>
                </div>
            </form>
        </div>

        <div class="contact-info">
            <div class="contact-method clickable-contact" data-contact-type="email" data-contact-value="<EMAIL>">
                <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm17 4.238l-7.928 7.1L4 7.216V19h16V7.238zM4.511 5l7.55 6.662L19.502 5H4.511z" fill="currentColor"/></svg>
                </div>
                <div class="contact-details">
                    <h3>Email Us</h3>
                    <p><EMAIL></p>
                </div>
            </div>

            <div class="contact-method clickable-contact" data-contact-type="phone" data-contact-value="+917093388672">
                <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M9.366 10.682a10.556 10.556 0 0 0 3.952 3.952l.884-1.238a1 1 0 0 1 1.294-.296 11.422 11.422 0 0 0 4.583 1.364 1 1 0 0 1 .921.997v4.462a1 1 0 0 1-.898.995c-.53.055-1.064.082-1.602.082C9.94 21 3 14.06 3 5.5c0-.538.027-1.072.082-1.602A1 1 0 0 1 4.077 3h4.462a1 1 0 0 1 .997.921A11.422 11.422 0 0 0 10.9 8.504a1 1 0 0 1-.296 1.294l-1.238.884zm-2.522-.657l1.9-1.357A13.41 13.41 0 0 1 7.647 5H5.01c-.006.166-.009.333-.009.5C5 12.956 11.044 19 18.5 19c.167 0 .334-.003.5-.01v-2.637a13.41 13.41 0 0 1-3.668-1.097l-1.357 1.9a12.442 12.442 0 0 1-1.588-.75l-.058-.033a12.556 12.556 0 0 1-4.702-4.702l-.033-.058a12.442 12.442 0 0 1-.75-1.588z" fill="currentColor"/></svg>
                </div>
                <div class="contact-details">
                    <h3>Call Us</h3>
                    <p>+91 - 7093388672</p>
                </div>
            </div>

            <div class="contact-method clickable-contact" data-contact-type="linkedin" data-contact-value="https://www.linkedin.com/company/ziantrixai/">
                <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M6.94 5a2 2 0 1 1-4-.002 2 2 0 0 1 4 .002zM7 8.48H3V21h4V8.48zm6.32 0H9.34V21h3.94v-6.57c0-3.66 4.77-4 4.77 0V21H22v-7.93c0-6.17-7.06-5.94-8.72-2.91l.05-1.68z" fill="currentColor"/></svg>
                </div>
                <div class="contact-details">
                    <h3>LinkedIn</h3>
                    <p>Connect with us</p>
                </div>
            </div>

            <div class="contact-method">
                <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M12 20.9l4.95-4.95a7 7 0 1 0-9.9 0L12 20.9zm0 2.828l-6.364-6.364a9 9 0 1 1 12.728 0L12 23.728zM12 13a2 2 0 1 0 0-4 2 2 0 0 0 0 4zm0 2a4 4 0 1 1 0-8 4 4 0 0 1 0 8z" fill="currentColor"/></svg>
                </div>
                <div class="contact-details">
                    <h3>Visit Us</h3>
                    <p>Hyderabad, Telangana, India</p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const contactForm = document.getElementById('contactForm');
        const nameInput = document.getElementById('name');
        const emailInput = document.getElementById('email');
        const companyInput = document.getElementById('company');
        const messageInput = document.getElementById('message');
        const nameError = document.getElementById('nameError');
        const emailError = document.getElementById('emailError');
        const companyError = document.getElementById('companyError');
        const messageError = document.getElementById('messageError');

        if (contactForm) {
            contactForm.addEventListener('submit', function(e) {
                let isValid = true;

                // Validate name
                if (!nameInput.value.trim()) {
                    nameError.textContent = 'Please enter your name';
                    nameError.style.display = 'block';
                    nameInput.classList.add('error');
                    isValid = false;
                } else {
                    nameError.style.display = 'none';
                    nameInput.classList.remove('error');
                }

                // Validate email
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailInput.value.trim() || !emailRegex.test(emailInput.value)) {
                    emailError.textContent = 'Please enter a valid email address';
                    emailError.style.display = 'block';
                    emailInput.classList.add('error');
                    isValid = false;
                } else {
                    emailError.style.display = 'none';
                    emailInput.classList.remove('error');
                }

                // Validate company
                if (!companyInput.value.trim()) {
                    companyError.textContent = 'Please enter your company';
                    companyError.style.display = 'block';
                    companyInput.classList.add('error');
                    isValid = false;
                } else {
                    companyError.style.display = 'none';
                    companyInput.classList.remove('error');
                }

                // Validate message
                if (!messageInput.value.trim()) {
                    messageError.textContent = 'Please enter your message';
                    messageError.style.display = 'block';
                    messageInput.classList.add('error');
                    isValid = false;
                } else {
                    messageError.style.display = 'none';
                    messageInput.classList.remove('error');
                }

                if (!isValid) {
                    e.preventDefault();
                } else {
                    // If using AJAX form submission
                    e.preventDefault();

                    // Simulate form submission (replace with actual AJAX call)
                    setTimeout(function() {
                        // Hide form fields
                        contactForm.querySelectorAll('.form-group, button').forEach(el => {
                            el.style.opacity = '0.5';
                            el.style.pointerEvents = 'none';
                        });

                        // Show confirmation message
                        document.getElementById('formConfirmation').style.display = 'block';

                        // Reset form after delay
                        setTimeout(function() {
                            contactForm.reset();
                            contactForm.querySelectorAll('.form-group, button').forEach(el => {
                                el.style.opacity = '1';
                                el.style.pointerEvents = 'auto';
                            });
                            document.getElementById('formConfirmation').style.display = 'none';
                        }, 5000);
                    }, 1000);
                }
            });
        }

        // Contact method click functionality
        const clickableContacts = document.querySelectorAll('.clickable-contact');

        clickableContacts.forEach(contact => {
            contact.addEventListener('click', function() {
                const contactType = this.getAttribute('data-contact-type');
                const contactValue = this.getAttribute('data-contact-value');

                switch(contactType) {
                    case 'email':
                        window.location.href = `mailto:${contactValue}`;
                        break;
                    case 'phone':
                        window.location.href = `tel:${contactValue}`;
                        break;
                    case 'linkedin':
                        window.open(contactValue, '_blank', 'noopener,noreferrer');
                        break;
                }
            });

            // Add cursor pointer style
            contact.style.cursor = 'pointer';
        });
    });
</script>
{% endblock %}
