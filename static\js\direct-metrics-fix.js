/**
 * Direct DOM manipulation to fix feature card metrics
 * This script runs immediately without waiting for DOMContentLoaded
 */
(function() {
    // Function to apply the fix
    function fixMetrics() {
        // Get all metric wrappers
        var metricWrappers = document.querySelectorAll('.feature-card .metric-wrapper');
        var featureMetrics = document.querySelectorAll('.feature-card .feature-metric');
        var metricLabels = document.querySelectorAll('.feature-card .feature-metric-label');
        
        // Check if dark mode is active
        var isDarkMode = document.body.classList.contains('dark-theme');
        
        // Apply styles based on theme
        if (isDarkMode) {
            // Dark mode styles
            metricWrappers.forEach(function(wrapper) {
                wrapper.style.cssText = 'background-color: #1e1e1e !important; background: #1e1e1e !important; opacity: 1 !important; position: relative !important; z-index: 10 !important; box-shadow: 0 0 0 4px #1e1e1e !important;';
            });
            
            featureMetrics.forEach(function(metric) {
                metric.style.cssText = 'background-color: #1e1e1e !important; background: #1e1e1e !important; color: #64b5f6 !important; opacity: 1 !important; text-shadow: 0 0 10px rgba(100, 181, 246, 0.4) !important;';
            });
            
            metricLabels.forEach(function(label) {
                label.style.cssText = 'background-color: #1e1e1e !important; background: #1e1e1e !important; color: #aaa !important; opacity: 1 !important;';
            });
        } else {
            // Light mode styles
            metricWrappers.forEach(function(wrapper) {
                wrapper.style.cssText = 'background-color: #ffffff !important; background: #ffffff !important; opacity: 1 !important; position: relative !important; z-index: 10 !important; box-shadow: 0 0 0 4px #ffffff !important;';
            });
            
            featureMetrics.forEach(function(metric) {
                metric.style.cssText = 'background-color: #ffffff !important; background: #ffffff !important; color: #1976d2 !important; opacity: 1 !important;';
            });
            
            metricLabels.forEach(function(label) {
                label.style.cssText = 'background-color: #ffffff !important; background: #ffffff !important; color: #666 !important; opacity: 1 !important;';
            });
        }
    }
    
    // Run the fix immediately
    fixMetrics();
    
    // Run the fix again after a short delay to ensure it applies after other scripts
    setTimeout(fixMetrics, 100);
    setTimeout(fixMetrics, 500);
    setTimeout(fixMetrics, 1000);
    
    // Run the fix when the DOM is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        fixMetrics();
        
        // Run the fix again after a short delay
        setTimeout(fixMetrics, 100);
        
        // Set up a MutationObserver to watch for theme changes
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.attributeName === 'class' && mutation.target === document.body) {
                    fixMetrics();
                }
            });
        });
        
        // Start observing the body for class changes
        observer.observe(document.body, { attributes: true });
        
        // Watch for theme toggle changes
        var themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('change', function() {
                setTimeout(fixMetrics, 50);
            });
        }
    });
    
    // Run the fix when the window loads
    window.addEventListener('load', function() {
        fixMetrics();
        setTimeout(fixMetrics, 100);
    });
})();
