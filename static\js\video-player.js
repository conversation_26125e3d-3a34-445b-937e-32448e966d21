/**
 * Video Player Functionality
 * Handles the hero video player interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get the hero video and play button elements
    const heroVideo = document.querySelector('.hero-video video');
    const playButton = document.getElementById('heroVideoPlay');
    
    if (heroVideo && playButton) {
        // Toggle play/pause when the play button is clicked
        playButton.addEventListener('click', function() {
            if (heroVideo.paused) {
                // If video is paused, play it and unmute
                heroVideo.muted = false;
                heroVideo.play();
                playButton.style.opacity = '0';
                
                // Hide the play button after a short delay
                setTimeout(() => {
                    playButton.style.display = 'none';
                }, 300);
            } else {
                // If video is playing, pause it and show the play button
                heroVideo.pause();
                playButton.style.display = 'flex';
                playButton.style.opacity = '1';
            }
        });
        
        // Show the play button when the video ends
        heroVideo.addEventListener('ended', function() {
            playButton.style.display = 'flex';
            setTimeout(() => {
                playButton.style.opacity = '1';
            }, 10);
            
            // Reset the video to the beginning
            heroVideo.currentTime = 0;
        });
        
        // Handle video click to toggle play/pause
        heroVideo.addEventListener('click', function() {
            if (heroVideo.paused) {
                heroVideo.play();
                playButton.style.opacity = '0';
                setTimeout(() => {
                    playButton.style.display = 'none';
                }, 300);
            } else {
                heroVideo.pause();
                playButton.style.display = 'flex';
                setTimeout(() => {
                    playButton.style.opacity = '1';
                }, 10);
            }
        });
    }
});
