/**
 * Professional Services Section JavaScript
 * Handles interactive functionality for the services section
 */

document.addEventListener('DOMContentLoaded', function() {
  console.log('Professional Services JS loaded');

  // Get all service cards in the services section
  const serviceCards = document.querySelectorAll('.services-section .service-card');
  console.log('Found service cards:', serviceCards.length);

  // Add hover effects and animations
  serviceCards.forEach(card => {
    // Add hover effect
    card.addEventListener('mouseenter', function() {
      this.style.transform = 'translateY(-5px)';
      this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.15)';
    });

    card.addEventListener('mouseleave', function() {
      this.style.transform = 'translateY(0)';
      this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
    });

    // Add click event for CTA buttons
    const ctaButton = card.querySelector('.service-cta-button');
    if (ctaButton) {
      ctaButton.addEventListener('click', function(e) {
        // If the button has an onclick attribute, let it handle the event
        if (!this.hasAttribute('onclick')) {
          e.preventDefault();
          // Open demo modal if it exists
          if (typeof openDemoModal === 'function') {
            openDemoModal();
          }
        }
      });
    }
  });

  // Ensure service features are properly aligned
  const featureIcons = document.querySelectorAll('.services-section .service-features-list .feature-icon');
  console.log('Found feature icons:', featureIcons.length);

  featureIcons.forEach(icon => {
    icon.style.backgroundColor = 'transparent';
    icon.style.boxShadow = 'none';
    icon.style.background = 'none';
  });

  // Ensure feature text is properly aligned
  const featureTexts = document.querySelectorAll('.services-section .service-features-list .feature-text');
  console.log('Found feature texts:', featureTexts.length);

  featureTexts.forEach(text => {
    text.style.textAlign = 'left';
    text.style.display = 'inline-block';
    text.style.verticalAlign = 'middle';
  });

  // Handle dark mode transitions
  const themeToggle = document.getElementById('theme-toggle');
  if (themeToggle) {
    themeToggle.addEventListener('change', function() {
      // Apply smooth transitions for dark/light mode changes
      document.querySelectorAll('.service-card, .service-title, .service-subtitle, .feature-icon, .feature-text')
        .forEach(element => {
          element.style.transition = 'color 0.3s ease, background-color 0.3s ease';
        });
    });
  }

  // Force a redraw of the services section
  setTimeout(function() {
    const servicesSection = document.querySelector('.services-section');
    if (servicesSection) {
      servicesSection.style.display = 'none';
      setTimeout(function() {
        servicesSection.style.display = 'block';
      }, 10);
    }
  }, 100);
});
