# Python bytecode
__pycache__/
*.py[cod]
*$py.class

# Python environment
env/
venv/
ENV/
app_venv/

# Environment variables (CRITICAL - Contains secrets!)
.env
.env.local
.env.production
.env.development
.env.staging
*.env
!.env.example

# Build, distribution, and packaging
build/
# dist/ - Commented out to allow static site deployment
develop-eggs/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
*.sublime-workspace
*.sublime-project

# OS-specific files
.DS_Store
Thumbs.db
Desktop.ini

# Logs
*.log
logs/
*.log.*

# Database files (CRITICAL - Contains user data!)
data/
*.db
*.sqlite3
*.sqlite
*.sql
database.db
ziantrix_app.db

# Database backups and dumps
*.backup
*.dump
*.bak
backup/
backups/

# PyPI configuration file
.pypirc

# Test and coverage reports
.coverage
nosetests.xml
coverage.xml
*.cover
*.tox
*.nox
*.coverage

# Python distribution tools
*.pyz
*.pex
*.whl

# Packaging output
*.tar.gz
*.zip

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# Python-specific metadata
*.pkl
*.pyd
*.so

# Virtualenv folder
virtualenv/

# Security and secrets (CRITICAL!)
secrets/
keys/
certificates/
*.key
*.pem
*.p12
*.pfx
*.crt
*.cer

# Configuration files with secrets
config.py
config.json
settings.py
local_settings.py
production_settings.py

# API keys and tokens
api_keys.txt
tokens.txt
credentials.json
service-account.json

# Email and SMTP credentials
email_config.py
smtp_config.py

# Session and cache files
flask_session/
sessions/
cache/
*.cache

# Temporary files
tmp/
temp/
*.tmp
*.temp

# User uploads and media
uploads/
media/
user_files/

# Railway and deployment specific
.railway/
Procfile.local

# Docker (if used)
.dockerignore
docker-compose.override.yml
.docker/

# Monitoring and analytics
analytics/
metrics/
monitoring/

# Error reports and crash dumps
error_reports/
crash_dumps/
*.dmp

# Local development files
local/
dev/
development/

# Backup files from editors
*~
*.swp
*.swo
.#*
\#*#

# MacOS specific
.DS_Store?
._*
.Spotlight-V100
.Trashes

# Windows specific
ehthumbs.db
Thumbs.db:encryptable

# Linux specific
*~
.fuse_hidden*
.directory
.Trash-*
