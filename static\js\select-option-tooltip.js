/**
 * Select Option Tooltip
 * Adds tooltip functionality for select options with title attributes
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get all select elements with options that have title attributes
    const selects = document.querySelectorAll('select');
    
    selects.forEach(select => {
        // Check if this select has any options with title attributes
        const hasOptionWithTitle = Array.from(select.options).some(option => option.hasAttribute('title'));
        
        if (hasOptionWithTitle) {
            // Create tooltip element
            const tooltip = document.createElement('div');
            tooltip.className = 'select-tooltip';
            tooltip.style.opacity = '0';
            
            // Add tooltip to the select wrapper
            const selectWrapper = select.closest('.select-wrapper');
            if (selectWrapper) {
                selectWrapper.appendChild(tooltip);
                
                // Add change event listener to show tooltip for selected option
                select.addEventListener('change', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    
                    if (selectedOption.hasAttribute('title')) {
                        tooltip.textContent = selectedOption.getAttribute('title');
                        tooltip.style.opacity = '1';
                        
                        // Hide tooltip after 3 seconds
                        setTimeout(() => {
                            tooltip.style.opacity = '0';
                        }, 3000);
                    } else {
                        tooltip.style.opacity = '0';
                    }
                });
                
                // Add focus event listener
                select.addEventListener('focus', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    
                    if (selectedOption.hasAttribute('title')) {
                        tooltip.textContent = selectedOption.getAttribute('title');
                        tooltip.style.opacity = '1';
                    }
                });
                
                // Add blur event listener
                select.addEventListener('blur', function() {
                    tooltip.style.opacity = '0';
                });
                
                // Add mouseover event listener for the select element
                select.addEventListener('mouseover', function() {
                    const selectedOption = this.options[this.selectedIndex];
                    
                    if (selectedOption.hasAttribute('title')) {
                        tooltip.textContent = selectedOption.getAttribute('title');
                        tooltip.style.opacity = '1';
                    }
                });
                
                // Add mouseout event listener
                select.addEventListener('mouseout', function() {
                    tooltip.style.opacity = '0';
                });
            }
        }
    });
});
