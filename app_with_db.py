from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
import uuid
import smtplib
from email.message import EmailMessage
import os
import re
# from functools import wraps  # Not used
from datetime import datetime
import logging
import json

# Database imports
from models import db, FormSubmission, ChatConversation, ChatMessage, Analytics
from database import DatabaseManager, FormManager, ChatManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.environ.get("SECRET_KEY", "dev_secret_key_change_in_production")
app.config["SESSION_COOKIE_SECURE"] = os.environ.get("FLASK_ENV") == "production"
app.config["SESSION_COOKIE_HTTPONLY"] = True
app.config["PERMANENT_SESSION_LIFETIME"] = 1800  # 30 minutes

# Initialize database
db_manager = DatabaseManager(app)

# App configuration complete

# Configure static file caching
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 31536000  # 1 year in seconds

# Add response headers for better mobile performance
@app.after_request
def add_header(response):
    # Cache static files
    if 'Cache-Control' not in response.headers:
        if request.path.startswith('/static/'):
            response.headers['Cache-Control'] = 'public, max-age=31536000'
        else:
            response.headers['Cache-Control'] = 'no-store'

    # Add security headers
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'
    response.headers['X-XSS-Protection'] = '1; mode=block'

    return response

# Email configuration
EMAIL_SENDER = os.environ.get("EMAIL_SENDER", "<EMAIL>")
EMAIL_PASSWORD = os.environ.get("EMAIL_PASSWORD", "your_app_password")
EMAIL_RECIPIENT = os.environ.get("EMAIL_RECIPIENT", "<EMAIL>")
SMTP_SERVER = os.environ.get("SMTP_SERVER", "smtp.gmail.com")
SMTP_PORT = int(os.environ.get("SMTP_PORT", 465))

# Calendar booking link
CALENDAR_LINK = os.environ.get("CALENDAR_LINK", "https://calendar.app.google/uy4Szgfn4mdyZPLA6")

# Data directory for form submissions only
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
os.makedirs(DATA_DIR, exist_ok=True)

# Form validation patterns
EMAIL_PATTERN = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'

# User management functions removed - no longer needed

def validate_email(email):
    """Validate email format using regex pattern"""
    return re.match(EMAIL_PATTERN, email) is not None

def validate_form_data(name, email, message):
    """Validate form data and return errors if any"""
    errors = []

    if not name or len(name.strip()) < 2:
        errors.append("Name must be at least 2 characters long")

    if not email or not validate_email(email):
        errors.append("Please enter a valid email address")

    # Message is optional for some forms (using hidden fields)
    if message and len(message.strip()) < 10:
        errors.append("Message must be at least 10 characters long")

    return errors

def send_email(name, email, message):
    """Send email with form data and save to file"""
    try:
        # First, save the form data to a file
        data_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
        os.makedirs(data_dir, exist_ok=True)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = os.path.join(data_dir, f'contact_{timestamp}_{email.replace("@", "_at_")}.txt')

        # Extract inquiry type if it's in the message
        inquiry_type = ""
        if message.startswith("Inquiry Type:"):
            # Extract the inquiry type from the message
            first_line_end = message.find("\n\n")
            if first_line_end != -1:
                inquiry_type = message[13:first_line_end].strip()
                # Remove the inquiry type prefix from the message for cleaner display
                message = message[first_line_end + 2:]

        # Save to file with error handling
        try:
            with open(filename, 'w') as f:
                f.write(f"Name: {name}\n")
                f.write(f"Email: {email}\n")
                if inquiry_type:
                    f.write(f"Inquiry Type: {inquiry_type}\n")
                f.write(f"Message:\n{message}\n\n")
                f.write(f"Received on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            logger.info(f"Form data saved to {filename}")
        except Exception as file_error:
            logger.error(f"Error saving form data to file: {str(file_error)}")
            # Continue with email sending even if file save fails

        # Try to send email if configured
        if EMAIL_SENDER != "<EMAIL>" and EMAIL_PASSWORD != "your_app_password":
            try:
                msg = EmailMessage()

                # Prepare email content
                email_content = f"Name: {name}\nEmail: {email}\n"
                if inquiry_type:
                    email_content += f"Inquiry Type: {inquiry_type}\n"
                email_content += f"Message:\n{message}\n\n"
                email_content += f"Received on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

                msg.set_content(email_content)

                # Set subject with inquiry type if available
                subject = f"New Contact Request from {name}"
                if inquiry_type:
                    subject = f"New {inquiry_type} Request from {name}"

                msg["Subject"] = subject
                msg["From"] = EMAIL_SENDER
                msg["Reply-To"] = email
                msg["To"] = EMAIL_RECIPIENT

                # Email server connection
                server = smtplib.SMTP_SSL(SMTP_SERVER, SMTP_PORT)
                server.login(EMAIL_SENDER, EMAIL_PASSWORD)
                server.send_message(msg)
                server.quit()

                logger.info(f"Email sent successfully from {email}")
            except smtplib.SMTPAuthenticationError:
                logger.error("SMTP Authentication Error: Invalid email credentials")
                return False, "Email server authentication failed. Please contact support."
            except smtplib.SMTPException as smtp_error:
                logger.error(f"SMTP Error: {str(smtp_error)}")
                return False, f"Email server error: {str(smtp_error)}"
            except Exception as email_error:
                logger.error(f"Error sending email: {str(email_error)}")
                return False, f"Error sending email: {str(email_error)}"
        else:
            logger.info(f"Email not sent - using file storage only")

        return True, None
    except Exception as e:
        logger.error(f"Error processing form: {str(e)}")
        return False, str(e)

@app.route("/")
def index():
    """Render home page"""
    calendly_link = os.environ.get('CALENDLY_LINK', 'https://calendly.com/vijay-kodam98/demo-call-with-ziantrix')
    return render_template("index.html", calendly_link=calendly_link)

@app.route("/landing")
def landing():
    """Render landing/demo page"""
    calendly_link = os.environ.get('CALENDLY_LINK', 'https://calendly.com/vijay-kodam98/demo-call-with-ziantrix')
    return render_template("landing.html", calendly_link=calendly_link)

@app.route("/demo")
def demo():
    """Redirect to landing page with demo modal"""
    return redirect(url_for('landing', _anchor='demoModal'))

@app.route("/offline")
def offline():
    """Serve offline page"""
    return app.send_static_file("offline.html")

@app.route("/submit_form", methods=["POST"])
def submit_form():
    """Handle form submission"""
    # Get form data
    name = request.form.get("name", "").strip()
    email = request.form.get("email", "").strip()
    message = request.form.get("message", "").strip()
    company = request.form.get("company", "").strip()
    phone = request.form.get("phone", "").strip()
    inquiry_type = request.form.get("inquiry_type", "").strip()
    source_page = request.form.get("source", "landing")  # Track which page the form was submitted from

    # Validate form data
    errors = validate_form_data(name, email, message)

    if errors:
        for error in errors:
            flash(error, "error")
        return redirect(url_for(source_page))

    # Save form submission to database
    submission, db_error = FormManager.save_form_submission(
        form_type="contact" if source_page != "demo" else "demo",
        name=name,
        email=email,
        message=message,
        company=company,
        phone=phone,
        inquiry_type=inquiry_type,
        source_page=source_page,
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )

    if db_error:
        logger.error(f"Failed to save form submission: {db_error}")

    # Prepare message with inquiry type if available
    formatted_message = message
    if inquiry_type:
        formatted_message = f"Inquiry Type: {inquiry_type}\n\n{message}"

    # Add company and phone info if available
    if company:
        formatted_message = f"Company: {company}\n\n{formatted_message}"
    if phone:
        formatted_message = f"Phone: {phone}\n\n{formatted_message}"

    # Send email
    success, error = send_email(name, email, formatted_message)

    if success:
        flash("Thank you for your message! We'll get back to you soon.", "success")
        # Redirect back to the source page
        if source_page == "index":
            return redirect(url_for("index", _anchor="contact"))
        else:
            return redirect(url_for(source_page))
    else:
        flash(f"Error sending message. Please try again later or contact us directly.", "error")
        logger.error(f"Failed to send email: {error}")
        return redirect(url_for(source_page))

@app.route("/submit_demo", methods=["POST"])
def submit_demo():
    """Handle demo request form submission"""
    # Get form data
    name = request.form.get("name", "").strip()
    email = request.form.get("email", "").strip()
    company = request.form.get("company", "").strip()
    message = request.form.get("message", "").strip()

    # Validate form data
    errors = []
    if not name or len(name.strip()) < 2:
        errors.append("Name must be at least 2 characters long")
    if not email or not validate_email(email):
        errors.append("Please enter a valid email address")
    if not company or len(company.strip()) < 2:
        errors.append("Company name is required")

    if errors:
        for error in errors:
            flash(error, "error")
        return redirect(url_for("index"))

    # Save demo request to database
    submission, db_error = FormManager.save_form_submission(
        form_type="demo",
        name=name,
        email=email,
        message=message,
        company=company,
        source_page="index",
        ip_address=request.remote_addr,
        user_agent=request.headers.get('User-Agent')
    )

    if db_error:
        logger.error(f"Failed to save demo request: {db_error}")

    # Prepare demo request message
    demo_message = f"Demo Request from {name} at {company}\n\n"
    if message:
        demo_message += f"Goals: {message}\n\n"
    demo_message += f"Contact: {email}"

    # Send email
    try:
        success, error = send_email(name, email, demo_message)

        if success:
            # Return JSON response for AJAX handling
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({"success": True, "message": "Thank you for requesting a demo! We'll contact you soon."})

            # Regular form submission
            flash("Thank you for requesting a demo! We'll contact you soon.", "success")
            return redirect(url_for("index"))
        else:
            error_message = f"Error processing your request: {error}" if error else "Error processing your request. Please try again later."

            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({"success": False, "message": error_message})

            flash(error_message, "error")
            logger.error(f"Failed to send demo request email: {error}")
            return redirect(url_for("index"))
    except Exception as e:
        logger.error(f"Exception in submit_demo: {str(e)}")
        error_message = "An unexpected error occurred. Please try again later."

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({"success": False, "message": error_message})

        flash(error_message, "error")
        return redirect(url_for("index"))

@app.route("/api/validate-email", methods=["POST"])
def validate_email_api():
    """API endpoint to validate email format"""
    data = request.get_json()
    email = data.get("email", "")

    is_valid = validate_email(email)
    return jsonify({"valid": is_valid})

@app.route("/api/user/submissions")
def api_user_submissions():
    """API endpoint to get user's form submissions"""
    if not session.get("logged_in"):
        return jsonify({"success": False, "error": "Not logged in"}), 401

    user_id = session.get("user_id")
    if not user_id:
        return jsonify({"success": False, "error": "Invalid session"}), 401

    try:
        submissions = FormManager.get_user_submissions(user_id)
        return jsonify({
            "success": True,
            "submissions": submissions
        })
    except Exception as e:
        logger.error(f"Error getting user submissions: {str(e)}")
        return jsonify({"success": False, "error": "Failed to load submissions"}), 500

@app.route("/api/user/chats")
def api_user_chats():
    """API endpoint to get user's chat conversations"""
    if not session.get("logged_in"):
        return jsonify({"success": False, "error": "Not logged in"}), 401

    user_id = session.get("user_id")
    if not user_id:
        return jsonify({"success": False, "error": "Invalid session"}), 401

    try:
        conversations = ChatConversation.query.filter_by(user_id=user_id)\
            .order_by(ChatConversation.created_at.desc())\
            .limit(20).all()

        conversations_data = [conv.to_dict() for conv in conversations]

        return jsonify({
            "success": True,
            "conversations": conversations_data
        })
    except Exception as e:
        logger.error(f"Error getting user chats: {str(e)}")
        return jsonify({"success": False, "error": "Failed to load chat history"}), 500

# Login and registration routes removed - authentication disabled

@app.route("/features")
def features():
    """Why Ziantrix page"""
    return redirect(url_for('index', _anchor='features'))

@app.route("/solutions")
def solutions():
    """Solutions page"""
    return redirect(url_for('index', _anchor='solutions'))

@app.route("/customers")
def customers():
    """Customers page"""
    return redirect(url_for('index', _anchor='testimonials'))

@app.route("/resources")
def resources():
    """Resources page"""
    return redirect(url_for('index', _anchor='faq'))

@app.route("/faq")
def faq():
    """FAQ page"""
    return render_template("faq.html")

@app.route("/faq_new")
def faq_new():
    """New FAQ page with updated design"""
    return render_template("faq_new.html")

@app.route("/about")
def about():
    """About page"""
    return redirect(url_for('index', _anchor='about'))

@app.route("/support")
def support():
    """Support page"""
    return render_template("support.html")

@app.route("/services")
def services():
    """Services page"""
    return redirect(url_for('index', _anchor='services'))

@app.route("/chatbot-services")
def chatbot_services():
    """Chatbot Services page"""
    return redirect(url_for('index', _anchor='services'))

@app.route("/ai-services")
def ai_services():
    """AI Services page"""
    return redirect(url_for('index', _anchor='services'))

@app.route("/ai-agents")
def ai_agents():
    """AI Agents page"""
    return redirect(url_for('index', _anchor='services'))

# User profile, dashboard, settings, and authentication routes removed - authentication disabled

# Chatbot API endpoint
@app.route("/api/chatbot", methods=["POST"])
def chatbot_api():
    """API endpoint for chatbot interactions"""
    data = request.get_json()

    if not data:
        return jsonify({"error": "No data provided"}), 400

    message = data.get("message", "").strip()

    if not message:
        return jsonify({"error": "No message provided"}), 400

    # Get or create device ID from session
    device_id = session.get("device_id")
    if not device_id:
        device_id = str(uuid.uuid4())
        session["device_id"] = device_id

    # Get or create conversation (anonymous only)
    conversation_id = session.get("conversation_id")
    if not conversation_id:
        conversation = ChatManager.create_conversation(
            session_id=session.get("session_id", str(uuid.uuid4())),
            user_id=None,  # Always anonymous
            device_id=device_id
        )
        if conversation:
            conversation_id = conversation.id
            session["conversation_id"] = conversation_id

    try:
        # Log the incoming message
        logger.info(f"Chatbot message from {device_id}: {message}")

        # Save user message to database
        if conversation_id:
            ChatManager.save_message(
                conversation_id=conversation_id,
                message_type="user",
                content=message
            )

        def save_bot_response_and_return(response_text, contact_prompt=False):
            """Helper function to save bot response and return JSON"""
            if conversation_id:
                ChatManager.save_message(
                    conversation_id=conversation_id,
                    message_type="bot",
                    content=response_text
                )
            return jsonify({
                "response": response_text,
                "contact_prompt": contact_prompt
            })

        # Check if the message is about pricing
        if any(keyword in message.lower() for keyword in ["price", "pricing", "cost", "how much", "package", "subscription"]):
            return save_bot_response_and_return(
                "For detailed pricing information, we'd need to understand your specific requirements. "
                "Our pricing is customized based on your business needs, volume of conversations, and "
                "required features. Would you like to speak with our sales team? We can arrange a call to "
                "discuss your needs and provide a tailored quote.",
                contact_prompt=True
            )

        # Check if it's about products/services
        elif any(keyword in message.lower() for keyword in ["product", "service", "offer", "provide", "chatbot", "ai", "agent"]):
            # Use the services information to provide a response
            if "chatbot" in message.lower() or "chat" in message.lower():
                return jsonify({
                    "response": "Our Chatbot Services provide 24/7 customer support with natural language understanding and "
                               "seamless integration with your existing systems. Features include natural language processing, "
                               "multi-channel deployment, custom knowledge base integration, and comprehensive analytics. "
                               "Would you like to know more about any specific feature?",
                    "contact_prompt": False
                })
            elif "ai" in message.lower() or "artificial intelligence" in message.lower():
                return jsonify({
                    "response": "Our AI Services help businesses automate processes, gain insights from data, and create "
                               "personalized customer experiences. We offer predictive analytics, machine learning models, "
                               "data processing and analysis, and AI strategy consulting. Is there a particular AI service "
                               "you're interested in?",
                    "contact_prompt": False
                })
            elif "agent" in message.lower():
                return jsonify({
                    "response": "Our AI Agents can handle complex workflows, make decisions, and execute tasks with minimal "
                               "human intervention. They excel at task automation, decision-making, process optimization, "
                               "and continuous learning. Would you like to see a demo of our AI agents in action?",
                    "contact_prompt": False
                })
            else:
                return jsonify({
                    "response": "At Ziantrix, we offer three main service categories:\n\n"
                               "1. Chatbot Services: Advanced conversational AI for 24/7 customer support\n"
                               "2. AI Services: Cutting-edge artificial intelligence solutions for business automation\n"
                               "3. AI Agents: Autonomous agents for handling complex tasks and workflows\n\n"
                               "Which of these services would you like to learn more about?",
                    "contact_prompt": False
                })

        # Implementation timeline questions
        elif any(keyword in message.lower() for keyword in ["timeline", "implement", "deployment", "how long", "time frame", "timeframe"]):
            return jsonify({
                "response": "Our implementation timelines vary based on your business needs:\n\n"
                           "For SMBs:\n"
                           "- Simple cases: 3-5 days\n"
                           "- Custom solutions: 7-15 days\n\n"
                           "For Enterprises:\n"
                           "- MVP deployment: 4-6 weeks\n"
                           "- Full deployment: 8-12+ weeks\n\n"
                           "Would you like to discuss your specific implementation needs?",
                "contact_prompt": True
            })

        # Questions about getting started
        elif any(keyword in message.lower() for keyword in ["start", "begin", "setup", "set up", "integrate", "integration"]):
            return jsonify({
                "response": "Getting started with Ziantrix is easy! Here's the process:\n\n"
                           "1. Initial consultation to understand your needs\n"
                           "2. Solution design and customization\n"
                           "3. Implementation and integration with your systems\n"
                           "4. Testing and optimization\n"
                           "5. Training and deployment\n\n"
                           "Would you like to schedule a consultation to get started?",
                "contact_prompt": True
            })

        # Questions about features
        elif any(keyword in message.lower() for keyword in ["feature", "capability", "function", "can it", "does it"]):
            return jsonify({
                "response": "Our AI solutions come with numerous features including:\n\n"
                           "- Natural language understanding\n"
                           "- Multi-channel deployment (web, mobile, social media)\n"
                           "- Custom knowledge base integration\n"
                           "- Analytics and reporting\n"
                           "- Human handoff capabilities\n"
                           "- Continuous learning and improvement\n\n"
                           "Is there a specific feature you'd like to know more about?",
                "contact_prompt": False
            })

        # General greeting or hello
        elif any(keyword in message.lower() for keyword in ["hello", "hi", "hey", "greetings", "good morning", "good afternoon", "good evening"]):
            return jsonify({
                "response": "Hello! I'm the Ziantrix Assistant. I can help you learn about our AI chatbots, services, and agents. What would you like to know about?",
                "contact_prompt": False
            })

        # For other inquiries, provide a general response
        else:
            return jsonify({
                "response": "Thank you for your question. Ziantrix offers AI-powered chatbot solutions that can automate up to 70% of support queries, reduce costs by 60%, and deliver 24/7 customer service. Our solutions include chatbot services, AI services, and AI agents. How can I help you learn more about our offerings?",
                "contact_prompt": False
            })

    except Exception as e:
        logger.error(f"Error in chatbot API: {str(e)}")
        return jsonify({
            "response": "I'm sorry, I encountered an error processing your request. Please try again or contact our support team.",
            "contact_prompt": True
        })

@app.errorhandler(404)
def page_not_found(_):
    """Handle 404 errors"""
    return render_template("404.html"), 404

@app.errorhandler(500)
def server_error(error):
    """Handle 500 errors"""
    logger.error(f"Server error: {str(error)}")
    return render_template("500.html"), 500

if __name__ == "__main__":
    # Initialize database on startup
    with app.app_context():
        try:
            # Test database connection
            db.session.execute(db.text('SELECT 1'))
            logger.info("✅ Database connection successful")

            # Create tables if they don't exist
            db.create_all()
            logger.info("✅ Database tables ready")

            # Check if we need to migrate JSON data
            import json
            from pathlib import Path

            data_dir = Path("data")
            users_file = data_dir / "users.json"
            profiles_file = data_dir / "user_profiles.json"

            # JSON migration removed - authentication disabled

        except Exception as e:
            logger.error(f"Database initialization error: {str(e)}")
            logger.info("Continuing with file-based storage as fallback")

    # Use environment variable for debug mode
    debug_mode = os.environ.get("FLASK_DEBUG", "True").lower() == "true"
    # Get port from environment variable (Railway sets this automatically)
    port = int(os.environ.get("PORT", 5000))
    app.run(host="0.0.0.0", port=port, debug=debug_mode)
