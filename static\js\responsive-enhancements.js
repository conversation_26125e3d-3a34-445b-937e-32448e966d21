/**
 * Responsive Enhancements JavaScript
 * Handles responsive behavior and mobile optimizations
 */

(function() {
    'use strict';

    // Responsive utilities object
    const ResponsiveUtils = {
        // Breakpoints
        breakpoints: {
            mobile: 767,
            tablet: 1023,
            desktop: 1024
        },

        // Get current breakpoint
        getCurrentBreakpoint: function() {
            const width = window.innerWidth;
            if (width <= this.breakpoints.mobile) return 'mobile';
            if (width <= this.breakpoints.tablet) return 'tablet';
            return 'desktop';
        },

        // Check if mobile
        isMobile: function() {
            return window.innerWidth <= this.breakpoints.mobile;
        },

        // Check if tablet
        isTablet: function() {
            return window.innerWidth > this.breakpoints.mobile && window.innerWidth <= this.breakpoints.tablet;
        },

        // Check if desktop
        isDesktop: function() {
            return window.innerWidth > this.breakpoints.tablet;
        },

        // Debounce function for resize events
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    };

    // Responsive image loading
    function handleResponsiveImages() {
        const images = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for browsers without IntersectionObserver
            images.forEach(img => {
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                img.classList.add('loaded');
            });
        }
    }

    // Handle responsive video sizing
    function handleResponsiveVideos() {
        const videos = document.querySelectorAll('video');
        
        videos.forEach(video => {
            // Ensure videos are responsive
            video.style.maxWidth = '100%';
            video.style.height = 'auto';
            
            // Handle video play/pause on mobile for better performance
            if (ResponsiveUtils.isMobile()) {
                video.preload = 'metadata';
                
                // Pause video when not in viewport on mobile
                if ('IntersectionObserver' in window) {
                    const videoObserver = new IntersectionObserver((entries) => {
                        entries.forEach(entry => {
                            if (entry.isIntersecting) {
                                if (video.paused && video.autoplay) {
                                    video.play().catch(() => {
                                        // Handle autoplay failure silently
                                    });
                                }
                            } else {
                                if (!video.paused) {
                                    video.pause();
                                }
                            }
                        });
                    }, { threshold: 0.5 });
                    
                    videoObserver.observe(video);
                }
            }
        });
    }

    // Handle responsive navigation
    function handleResponsiveNavigation() {
        const navbar = document.querySelector('.navbar');
        const navLinks = document.querySelector('.nav-links');
        const mobileToggle = document.querySelector('.mobile-menu-toggle');

        if (!navbar || !navLinks) return;

        // Handle scroll behavior
        let lastScrollTop = 0;
        const handleScroll = ResponsiveUtils.debounce(() => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (ResponsiveUtils.isMobile()) {
                // Hide navbar on scroll down, show on scroll up (mobile)
                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    navbar.style.transform = 'translateY(0)';
                }
            } else {
                // Always show navbar on desktop
                navbar.style.transform = 'translateY(0)';
            }
            
            lastScrollTop = scrollTop;
        }, 10);

        window.addEventListener('scroll', handleScroll);

        // Handle mobile menu
        if (mobileToggle) {
            // Close mobile menu when clicking on nav links
            const navLinkItems = navLinks.querySelectorAll('a');
            navLinkItems.forEach(link => {
                link.addEventListener('click', () => {
                    if (ResponsiveUtils.isMobile()) {
                        mobileToggle.classList.remove('active');
                        navLinks.classList.remove('mobile-active');
                        document.body.classList.remove('menu-open');
                    }
                });
            });
        }
    }

    // Handle responsive forms
    function handleResponsiveForms() {
        const forms = document.querySelectorAll('form');
        
        forms.forEach(form => {
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                // Prevent zoom on iOS when focusing inputs
                if (ResponsiveUtils.isMobile()) {
                    if (input.type !== 'file' && input.type !== 'range') {
                        const currentFontSize = window.getComputedStyle(input).fontSize;
                        const fontSize = parseFloat(currentFontSize);
                        
                        if (fontSize < 16) {
                            input.style.fontSize = '16px';
                        }
                    }
                }
                
                // Handle input focus for better mobile UX
                input.addEventListener('focus', () => {
                    if (ResponsiveUtils.isMobile()) {
                        // Scroll input into view on mobile
                        setTimeout(() => {
                            input.scrollIntoView({ 
                                behavior: 'smooth', 
                                block: 'center' 
                            });
                        }, 300);
                    }
                });
            });
        });
    }

    // Handle responsive cards and grids
    function handleResponsiveLayouts() {
        const grids = document.querySelectorAll('.services-grid, .features-grid, .solutions-grid');
        
        grids.forEach(grid => {
            // Ensure proper grid behavior on different screen sizes
            const updateGridColumns = () => {
                const breakpoint = ResponsiveUtils.getCurrentBreakpoint();
                
                switch(breakpoint) {
                    case 'mobile':
                        grid.style.gridTemplateColumns = '1fr';
                        break;
                    case 'tablet':
                        if (grid.classList.contains('services-grid')) {
                            grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
                        } else {
                            grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
                        }
                        break;
                    case 'desktop':
                        if (grid.classList.contains('services-grid')) {
                            grid.style.gridTemplateColumns = 'repeat(3, 1fr)';
                        } else if (grid.classList.contains('features-grid')) {
                            grid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(280px, 1fr))';
                        } else {
                            grid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(250px, 1fr))';
                        }
                        break;
                }
            };

            updateGridColumns();
            window.addEventListener('resize', ResponsiveUtils.debounce(updateGridColumns, 250));
        });
    }

    // Handle responsive chatbot
    function handleResponsiveChatbot() {
        const chatbotLauncher = document.querySelector('.chatbot-launcher');
        const chatbotWidget = document.querySelector('.chatbot-widget');

        if (!chatbotLauncher || !chatbotWidget) return;

        const updateChatbotPosition = () => {
            if (ResponsiveUtils.isMobile()) {
                // Mobile positioning
                chatbotLauncher.style.bottom = '20px';
                chatbotLauncher.style.right = '20px';
                chatbotLauncher.style.width = '60px';
                chatbotLauncher.style.height = '60px';

                chatbotWidget.style.width = 'calc(100vw - 20px)';
                chatbotWidget.style.height = '70vh';
                chatbotWidget.style.bottom = '10px';
                chatbotWidget.style.left = '10px';
                chatbotWidget.style.right = '10px';
            } else {
                // Desktop positioning
                chatbotLauncher.style.bottom = '30px';
                chatbotLauncher.style.right = '30px';
                chatbotLauncher.style.width = '70px';
                chatbotLauncher.style.height = '70px';

                chatbotWidget.style.width = '400px';
                chatbotWidget.style.height = '600px';
                chatbotWidget.style.bottom = '30px';
                chatbotWidget.style.right = '30px';
                chatbotWidget.style.left = 'auto';
            }
        };

        updateChatbotPosition();
        window.addEventListener('resize', ResponsiveUtils.debounce(updateChatbotPosition, 250));
    }

    // Handle responsive performance optimizations
    function handleResponsivePerformance() {
        // Reduce animations on mobile for better performance
        if (ResponsiveUtils.isMobile()) {
            const style = document.createElement('style');
            style.textContent = `
                @media (max-width: 767px) {
                    *, *::before, *::after {
                        animation-duration: 0.1s !important;
                        transition-duration: 0.1s !important;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // Handle reduced motion preference
        if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
            const style = document.createElement('style');
            style.textContent = `
                *, *::before, *::after {
                    animation: none !important;
                    transition: none !important;
                }
            `;
            document.head.appendChild(style);
        }
    }

    // Initialize all responsive enhancements
    function initResponsiveEnhancements() {
        handleResponsiveImages();
        handleResponsiveVideos();
        handleResponsiveNavigation();
        handleResponsiveForms();
        handleResponsiveLayouts();
        handleResponsiveChatbot();
        handleResponsivePerformance();

        // Add responsive class to body for CSS targeting
        document.body.classList.add('responsive-enhanced');
        document.body.classList.add(`breakpoint-${ResponsiveUtils.getCurrentBreakpoint()}`);

        // Update breakpoint class on resize
        window.addEventListener('resize', ResponsiveUtils.debounce(() => {
            document.body.className = document.body.className.replace(/breakpoint-\w+/, '');
            document.body.classList.add(`breakpoint-${ResponsiveUtils.getCurrentBreakpoint()}`);
        }, 250));
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initResponsiveEnhancements);
    } else {
        initResponsiveEnhancements();
    }

    // Expose ResponsiveUtils globally for other scripts
    window.ResponsiveUtils = ResponsiveUtils;

})();
