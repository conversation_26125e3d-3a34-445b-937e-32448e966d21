/**
 * Consistent Mode Appearance
 * Ensures images and text appear consistently in both light and dark modes
 */



/* Fix for feature metrics */
.feature-metric {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
}

/* Light mode specific feature metric styles */
body:not(.dark-theme) .feature-metric {
  color: #2563eb;
  background-color: #ffffff;
  text-shadow: 0 0 10px rgba(37, 99, 235, 0.2);
}

/* Dark mode specific feature metric styles */
.dark-theme .feature-metric {
  color: #4299e1;
  background-color: #1a1f36;
  text-shadow: 0 0 10px rgba(66, 153, 225, 0.3);
}

/* Fix for feature metric labels */
.feature-metric-label {
  font-size: 0.9rem;
  line-height: 1.4;
}

/* Light mode specific feature metric label styles */
body:not(.dark-theme) .feature-metric-label {
  color: #666;
  background-color: #ffffff;
}

/* Dark mode specific feature metric label styles */
.dark-theme .feature-metric-label {
  color: #bcccdc;
  background-color: #1a1f36;
}

/* Fix for metric wrappers */
.metric-wrapper {
  padding: 8px;
  margin: 10px auto;
  width: 90%;
  border-radius: 8px;
}

/* Light mode specific metric wrapper styles */
body:not(.dark-theme) .metric-wrapper {
  background-color: #ffffff;
  box-shadow: 0 0 0 4px #ffffff;
}

/* Dark mode specific metric wrapper styles */
.dark-theme .metric-wrapper {
  background-color: #1a1f36;
  box-shadow: 0 0 0 4px #1a1f36;
}

/* Fix for hero title */
.hero-content h1 {
  font-size: 3.5rem;
  line-height: 1.2;
  margin-bottom: var(--spacing-24);
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
}

/* Light mode specific hero title styles */
body:not(.dark-theme) .hero-content h1 {
  background: linear-gradient(90deg, #1d4ed8 0%, #3b82f6 50%, #1d4ed8 100%);
  background-size: 200% auto;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  animation: gradientText 5s ease infinite;
  text-shadow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* Dark mode specific hero title styles */
.dark-theme .hero-content h1 {
  background: linear-gradient(90deg, #3182ce 0%, #63b3ed 50%, #3182ce 100%);
  background-size: 200% auto;
  color: transparent;
  -webkit-background-clip: text;
  background-clip: text;
  animation: gradientText 5s ease infinite;
  text-shadow: 0 0 20px rgba(66, 153, 225, 0.3);
}

/* Fix for hero subtitle */
.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* Light mode specific hero subtitle styles */
body:not(.dark-theme) .hero-subtitle {
  color: #2d3a58;
}

/* Dark mode specific hero subtitle styles */
.dark-theme .hero-subtitle {
  color: #d9e2ec;
}

/* Fix for feature cards */
.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
}

/* Light mode specific feature card styles */
body:not(.dark-theme) .feature-card {
  background-color: #ffffff;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(37, 99, 235, 0.1);
}

body:not(.dark-theme) .feature-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15), 0 0 20px rgba(37, 99, 235, 0.15);
  border-color: rgba(37, 99, 235, 0.2);
  transform: translateY(-10px);
}

/* Dark mode specific feature card styles */
.dark-theme .feature-card {
  background-color: #1a1f36;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(66, 153, 225, 0.15);
}

.dark-theme .feature-card:hover {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.4), 0 0 20px rgba(66, 153, 225, 0.25);
  border-color: rgba(66, 153, 225, 0.3);
  transform: translateY(-10px);
}

/* Fix for feature icons */
.feature-icon {
  transition: color 0.3s ease, filter 0.3s ease, opacity 0.3s ease, transform 0.3s ease;
}

/* Light mode specific feature icon styles */
body:not(.dark-theme) .feature-icon {
  color: #2563eb;
  filter: drop-shadow(0 0 8px rgba(37, 99, 235, 0.3));
  opacity: 0.9;
}

body:not(.dark-theme) .feature-card:hover .feature-icon {
  color: #1d4ed8;
  filter: drop-shadow(0 0 12px rgba(37, 99, 235, 0.5));
  opacity: 1;
  transform: scale(1.05);
}

/* Dark mode specific feature icon styles */
.dark-theme .feature-icon {
  color: #4299e1;
  filter: drop-shadow(0 0 8px rgba(66, 153, 225, 0.4));
  opacity: 0.9;
}

.dark-theme .feature-card:hover .feature-icon {
  color: #63b3ed;
  filter: drop-shadow(0 0 12px rgba(66, 153, 225, 0.6));
  opacity: 1;
  transform: scale(1.05);
}

/* Fix for about section image */
.about-image img {
  max-width: 100%;
  height: auto;
  transition: filter 0.3s ease;
}

/* Light mode specific about image styles */
body:not(.dark-theme) .about-image img {
  filter: none;
}

/* Dark mode specific about image styles */
.dark-theme .about-image img {
  filter: drop-shadow(0 0 15px rgba(100, 181, 246, 0.3));
}

/* Fix for stat numbers */
.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  transition: color 0.3s ease, text-shadow 0.3s ease;
}

/* Light mode specific stat number styles */
body:not(.dark-theme) .stat-number {
  color: #2563eb;
  text-shadow: 0 0 10px rgba(37, 99, 235, 0.3);
}

/* Dark mode specific stat number styles */
.dark-theme .stat-number {
  color: #64b5f6;
  text-shadow: 0 0 10px rgba(100, 181, 246, 0.4);
}



/* Fix for solution cards */
.solution-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  padding: 20px;
  margin: 10px;
}

/* Light mode specific solution card styles */
body:not(.dark-theme) .solution-card {
  background-color: #ffffff;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(37, 99, 235, 0.1);
}

/* Dark mode specific solution card styles */
.dark-theme .solution-card {
  background-color: #1a1f36;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(66, 153, 225, 0.1);
}

/* Fix for buttons */
.btn,
.cta-btn,
.service-cta-button,
.enhanced-cta-btn,
.bubble-cta-btn {
  transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease;
  padding: 10px 20px;
  height: auto;
  line-height: 1.5;
}

/* Fix for section headers */
.section-header h2 {
  font-size: 2.25rem;
  line-height: 1.3;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease, text-shadow 0.3s ease;
}

.section-header p {
  font-size: 1.1rem;
  line-height: 1.5;
  transition: color 0.3s ease;
}

/* Light mode specific section header styles */
body:not(.dark-theme) .section-header h2 {
  color: #1a1f36;
}

body:not(.dark-theme) .section-header p {
  color: #2d3a58;
}

/* Dark mode specific section header styles */
.dark-theme .section-header h2 {
  color: #f8fafc;
}

.dark-theme .section-header p {
  color: #cbd5e0;
}

/* Fix for FAQ items */
.faq-question {
  padding: 15px;
  font-size: 1.1rem;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.faq-answer {
  padding: 0 15px 15px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Light mode specific FAQ styles */
body:not(.dark-theme) .faq-question {
  background-color: #e6edf5;
  color: #1a1f36;
  border-bottom: 1px solid rgba(37, 99, 235, 0.1);
  border-radius: 1.5rem; /* Much more rounded edges for questions */
}

body:not(.dark-theme) .faq-answer {
  background-color: #ffffff;
  color: #2d3a58;
}

/* Dark mode specific FAQ styles */
.dark-theme .faq-question {
  background-color: #1a1f36;
  color: #f0f4f8;
  border-bottom: 1px solid rgba(66, 153, 225, 0.2);
}

.dark-theme .faq-answer {
  background-color: #1a1f36;
  color: #d9e2ec;
}

/* Fix for contact form */
.contact-form input,
.contact-form textarea {
  padding: 8px 12px; /* Reduced padding to decrease height */
  margin-bottom: 8px; /* Reduced margin to decrease height */
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Light mode specific contact form styles */
body:not(.dark-theme) .contact-form input,
body:not(.dark-theme) .contact-form textarea {
  background-color: #ffffff;
  color: #1a1f36;
  border: 1px solid rgba(37, 99, 235, 0.2);
}

/* Dark mode specific contact form styles */
.dark-theme .contact-form input,
.dark-theme .contact-form textarea {
  background-color: #2f3a5f;
  color: #f0f4f8;
  border: 1px solid #3a4a80;
}

/* Fix for footer */
.footer-section {
  padding: 20px;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Light mode specific footer styles */
body:not(.dark-theme) .footer-section {
  background-color: #e6edf5;
  color: #2d3a58;
  border-top: 1px solid rgba(37, 99, 235, 0.1);
}

/* Dark mode specific footer styles */
.dark-theme .footer-section {
  background-color: #1a1f36;
  color: #d9e2ec;
  border-top: 1px solid rgba(66, 153, 225, 0.1);
}

/* Fix for modal content */
.modal-content {
  padding: 20px;
  width: 80%;
  max-width: 400px;
  transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Light mode specific modal styles */
body:not(.dark-theme) .modal-content {
  background-color: #ffffff;
  color: #1a1f36;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(37, 99, 235, 0.1);
}

/* Dark mode specific modal styles */
.dark-theme .modal-content {
  background-color: #1a1f36;
  color: #f0f4f8;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(66, 153, 225, 0.2);
}

/* Fix for preloader */
.preloader {
  transition: background-color 0.3s ease;
}

/* Light mode specific preloader styles */
body:not(.dark-theme) .preloader {
  background-color: #ffffff;
}

/* Dark mode specific preloader styles */
.dark-theme .preloader {
  background-color: #1a1f36;
}
