/**
 * FAQ Mode Consistency
 * Ensures FAQ boxes appear consistently in both light and dark modes
 */

/* Light mode FAQ styles */
body:not(.dark-theme) .faq-item {
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: none;
}

body:not(.dark-theme) .faq-question {
  color: #2d3748;
  background: none;
}

body:not(.dark-theme) .faq-question:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

body:not(.dark-theme) .question-icon {
  background-color: rgba(49, 130, 206, 0.1);
  color: #3182ce;
}

body:not(.dark-theme) .faq-answer {
  background: none;
}

body:not(.dark-theme) .answer-content {
  color: #4a5568;
}

/* Dark mode FAQ styles */
.dark-theme .faq-item {
  border: 1px solid rgba(255, 255, 255, 0.1);
  background: none;
}

.dark-theme .faq-question {
  color: #e2e8f0;
  background: none;
}

.dark-theme .faq-question:hover {
  background-color: rgba(255, 255, 255, 0.02);
}

.dark-theme .question-icon {
  background-color: rgba(66, 153, 225, 0.2);
  color: #63b3ed;
}

.dark-theme .faq-answer {
  background: none;
}

.dark-theme .answer-content {
  color: #a0aec0;
}

/* Ensure toggle icons are consistent */
.faq-toggle {
  background: none;
  border: 1px solid var(--color-border);
}

.dark-theme .faq-toggle {
  border: 1px solid var(--color-border-medium);
}

/* Ensure consistent spacing */
.faq-container {
  max-width: 650px;
}

.faq-list, .faq-accordion {
  gap: 0.75rem;
}

.faq-item {
  margin-bottom: 0.75rem;
  border-radius: 1.5rem; /* Much more rounded edges */
}

.faq-question {
  padding: 1rem;
  font-size: 1rem;
}

.question-icon, .faq-icon {
  width: 2rem;
  height: 2rem;
  margin-right: 0.75rem;
}

.answer-content {
  padding: 0 1rem 1rem 3.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .faq-container {
    max-width: 600px;
  }

  .question-icon, .faq-icon {
    width: 1.75rem;
    height: 1.75rem;
  }

  .answer-content {
    padding: 0 0.9rem 0.9rem 3.25rem;
  }
}

@media (max-width: 480px) {
  .faq-container {
    max-width: 100%;
  }

  .question-icon, .faq-icon {
    width: 1.5rem;
    height: 1.5rem;
    margin-right: 0.6rem;
  }

  .answer-content {
    padding: 0 0.75rem 0.75rem 2.75rem;
  }
}
