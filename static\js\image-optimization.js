/**
 * Image Optimization and Lazy Loading
 * Optimized image loading with WebP support and performance monitoring
 */

(function() {
    'use strict';

    // Configuration
    const CONFIG = {
        rootMargin: '50px',
        threshold: 0.1,
        enableWebP: true,
        enableAVIF: true,
        placeholderClass: 'img-placeholder',
        loadedClass: 'img-loaded',
        errorClass: 'img-error'
    };

    // Feature detection
    const FEATURES = {
        webp: false,
        avif: false,
        intersectionObserver: 'IntersectionObserver' in window
    };

    // Detect WebP support
    function detectWebP() {
        return new Promise(resolve => {
            const webp = new Image();
            webp.onload = webp.onerror = () => {
                FEATURES.webp = webp.height === 2;
                resolve(FEATURES.webp);
            };
            webp.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        });
    }

    // Detect AVIF support
    function detectAVIF() {
        return new Promise(resolve => {
            const avif = new Image();
            avif.onload = avif.onerror = () => {
                FEATURES.avif = avif.height === 2;
                resolve(FEATURES.avif);
            };
            avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
        });
    }

    // Get optimized image source
    function getOptimizedSrc(originalSrc) {
        if (!originalSrc) return originalSrc;

        // Convert to WebP or AVIF if supported
        if (FEATURES.avif && CONFIG.enableAVIF) {
            return originalSrc.replace(/\.(jpg|jpeg|png)$/i, '.avif');
        } else if (FEATURES.webp && CONFIG.enableWebP) {
            return originalSrc.replace(/\.(jpg|jpeg|png)$/i, '.webp');
        }

        return originalSrc;
    }

    // Create placeholder for image
    function createPlaceholder(img) {
        const placeholder = document.createElement('div');
        placeholder.className = CONFIG.placeholderClass;
        const width = img.getAttribute('width') || '100%';
        const height = img.getAttribute('height') || '200px';
        const aspectRatio = img.getAttribute('style')?.match(/aspect-ratio:\s*(\d+\/\d+)/)?.[1] || '16/9';
        
        placeholder.style.cssText = `
            width: ${width};
            height: ${height};
            aspect-ratio: ${aspectRatio};
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 4px;
            contain: layout size;
            will-change: transform;
            transform: translateZ(0);
        `;
        
        return placeholder;
    }

    // Load image with error handling
    function loadImage(img) {
        return new Promise((resolve, reject) => {
            const newImg = new Image();
            
            newImg.onload = () => {
                // Ensure image dimensions match placeholder
                const width = img.getAttribute('width');
                const height = img.getAttribute('height');
                if (width && height) {
                    newImg.width = parseInt(width);
                    newImg.height = parseInt(height);
                }
                
                img.src = newImg.src;
                img.classList.add(CONFIG.loadedClass);
                img.classList.remove(CONFIG.placeholderClass);
                resolve(img);
            };
            
            newImg.onerror = () => {
                // Fallback to original source
                if (newImg.src !== img.dataset.src) {
                    newImg.src = img.dataset.src;
                } else {
                    img.classList.add(CONFIG.errorClass);
                    reject(new Error('Failed to load image'));
                }
            };
            
            // Try optimized format first
            const optimizedSrc = getOptimizedSrc(img.dataset.src);
            newImg.src = optimizedSrc;
        });
    }

    // Intersection Observer implementation
    function setupIntersectionObserver() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    loadImage(img).catch(console.warn);
                    observer.unobserve(img);
                }
            });
        }, {
            rootMargin: CONFIG.rootMargin,
            threshold: CONFIG.threshold
        });

        return observer;
    }

    // Fallback for browsers without Intersection Observer
    function setupScrollFallback() {
        let scrollTimeout;
        
        function checkImages() {
            const images = document.querySelectorAll('img[data-src]:not(.img-loaded)');
            const windowHeight = window.innerHeight;
            const scrollTop = window.pageYOffset;
            
            images.forEach(img => {
                const rect = img.getBoundingClientRect();
                if (rect.top < windowHeight + 100 && rect.bottom > -100) {
                    loadImage(img).catch(console.warn);
                }
            });
        }
        
        function handleScroll() {
            if (scrollTimeout) {
                clearTimeout(scrollTimeout);
            }
            scrollTimeout = setTimeout(checkImages, 100);
        }
        
        window.addEventListener('scroll', handleScroll, { passive: true });
        window.addEventListener('resize', handleScroll, { passive: true });
        
        // Initial check
        checkImages();
    }

    // Initialize lazy loading
    function initializeLazyLoading() {
        const images = document.querySelectorAll('img[data-src]');
        
        if (images.length === 0) return;
        
        // Add shimmer animation CSS
        if (!document.querySelector('#shimmer-styles')) {
            const style = document.createElement('style');
            style.id = 'shimmer-styles';
            style.textContent = `
                @keyframes shimmer {
                    0% { background-position: -200% 0; }
                    100% { background-position: 200% 0; }
                }
                .${CONFIG.placeholderClass} {
                    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                    background-size: 200% 100%;
                    animation: shimmer 1.5s infinite;
                    contain: layout size;
                    will-change: transform;
                    transform: translateZ(0);
                }
                .${CONFIG.loadedClass} {
                    opacity: 1;
                    transition: opacity 0.3s ease;
                    contain: layout size;
                }
                .${CONFIG.errorClass} {
                    background: #f5f5f5;
                    color: #999;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    contain: layout size;
                }
            `;
            document.head.appendChild(style);
        }
        
        if (FEATURES.intersectionObserver) {
            const observer = setupIntersectionObserver();
            images.forEach(img => {
                // Create and insert placeholder before loading
                const placeholder = createPlaceholder(img);
                img.parentNode.insertBefore(placeholder, img);
                img.classList.add(CONFIG.placeholderClass);
                observer.observe(img);
            });
        } else {
            setupScrollFallback();
        }
    }

    // Preload critical images
    function preloadCriticalImages() {
        const criticalImages = document.querySelectorAll('img[data-critical]');
        
        criticalImages.forEach(img => {
            if (img.dataset.src) {
                loadImage(img).catch(console.warn);
            }
        });
    }

    // Convert existing images to lazy loading
    function convertExistingImages() {
        const images = document.querySelectorAll('img:not([data-src]):not([data-critical])');
        
        images.forEach(img => {
            if (img.src && !img.complete) {
                img.dataset.src = img.src;
                img.removeAttribute('src');
                img.classList.add(CONFIG.placeholderClass);
            }
        });
    }

    // Performance monitoring
    function monitorImagePerformance() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    if (entry.initiatorType === 'img') {
                        console.log(`Image loaded: ${entry.name} in ${entry.duration}ms`);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['resource'] });
        }
    }

    // Initialize everything
    async function initialize() {
        // Detect format support
        await Promise.all([
            detectWebP(),
            detectAVIF()
        ]);
        
        // Convert existing images
        convertExistingImages();
        
        // Preload critical images
        preloadCriticalImages();
        
        // Initialize lazy loading
        initializeLazyLoading();
        
        // Monitor performance in development
        if (window.location.hostname === 'localhost') {
            monitorImagePerformance();
        }
        
        console.log('Image optimization initialized', {
            webp: FEATURES.webp,
            avif: FEATURES.avif,
            intersectionObserver: FEATURES.intersectionObserver
        });
    }

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // Export for manual control
    window.imageOptimization = {
        loadImage,
        preloadCriticalImages,
        getOptimizedSrc,
        features: FEATURES
    };

})();
