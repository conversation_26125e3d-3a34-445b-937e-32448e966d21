/**
 * Mobile Menu Toggle
 * Styling for the mobile hamburger menu button
 */

/* Mobile menu toggle button */
.mobile-menu-toggle {
    display: none;
    width: 30px;
    height: 24px;
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    z-index: 1000;
    padding: 0;
    background: transparent;
    border: none;
}

/* Hamburger lines */
.mobile-menu-toggle span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: #1976d2;
    border-radius: 3px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: .25s ease-in-out;
}

/* Dark theme hamburger lines */
.dark-theme .mobile-menu-toggle span {
    background: #64b5f6;
}

/* Position the hamburger lines */
.mobile-menu-toggle span:nth-child(1) {
    top: 0px;
}

.mobile-menu-toggle span:nth-child(2) {
    top: 10px;
}

.mobile-menu-toggle span:nth-child(3) {
    top: 20px;
}

/* Active state (when menu is open) */
.mobile-menu-toggle.active span:nth-child(1) {
    top: 10px;
    transform: rotate(135deg);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
    left: -60px;
}

.mobile-menu-toggle.active span:nth-child(3) {
    top: 10px;
    transform: rotate(-135deg);
}

/* Mobile navigation styles */
@media (max-width: 768px) {
    /* Show mobile menu toggle */
    .mobile-menu-toggle {
        display: block;
    }
    
    /* Adjust logo position when mobile menu is active */
    .mobile-menu-toggle.active + .navbar-logo {
        opacity: 0.5;
    }
    
    /* Mobile navigation container */
    .nav-links {
        display: none !important;
        position: fixed;
        top: 60px;
        left: 0;
        width: 100%;
        height: auto;
        background-color: #ffffff;
        flex-direction: column !important;
        padding: 20px 0;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        z-index: 999;
        overflow-y: auto;
        max-height: calc(100vh - 60px);
    }
    
    /* Dark theme mobile navigation */
    .dark-theme .nav-links {
        background-color: #0f172a;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
    }
    
    /* Show mobile navigation when active */
    .nav-links.mobile-active {
        display: flex !important;
    }
    
    /* Adjust navigation links for mobile */
    .navbar-link,
    .dropdown-toggle {
        width: 100%;
        text-align: center;
        padding: 15px 0 !important;
        margin: 0 !important;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    /* Dark theme border */
    .dark-theme .navbar-link,
    .dark-theme .dropdown-toggle {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    /* Adjust dropdown menu for mobile */
    .navbar-dropdown .dropdown-menu {
        position: static !important;
        width: 100%;
        box-shadow: none !important;
        border-radius: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        display: none;
    }
    
    /* Show dropdown menu when active */
    .navbar-dropdown.active .dropdown-menu {
        display: block !important;
    }
    
    /* Adjust dropdown items for mobile */
    .dropdown-item {
        width: 100%;
        text-align: center;
        padding: 15px 0 !important;
        background-color: rgba(0, 0, 0, 0.05) !important;
    }
    
    /* Dark theme dropdown items */
    .dark-theme .dropdown-item {
        background-color: rgba(255, 255, 255, 0.05) !important;
    }
    
    /* Login button in mobile menu */
    .login-btn {
        margin: 15px auto !important;
        display: block !important;
        width: 80% !important;
    }
    
    /* User menu in mobile navigation */
    .user-menu {
        width: 100%;
        text-align: center;
        margin: 15px 0 !important;
    }
    
    /* User dropdown in mobile navigation */
    .user-dropdown {
        position: static !important;
        width: 100%;
        box-shadow: none !important;
        margin: 0 !important;
    }
}
