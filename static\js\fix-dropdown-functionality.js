/**
 * Fix Dropdown Functionality
 * Ensures dropdown menus work correctly while preventing highlighting
 */
(function() {
    // Function to fix dropdown functionality
    function fixDropdownFunctionality() {
        // Get all dropdown toggles
        const dropdownToggles = document.querySelectorAll('.dropdown-toggle, .navbar-link.dropdown-toggle');

        // Add click event listeners to each toggle
        dropdownToggles.forEach(toggle => {
            // Remove any existing click event listeners
            const newToggle = toggle.cloneNode(true);
            toggle.parentNode.replaceChild(newToggle, toggle);

            // Add new click event listener
            newToggle.addEventListener('click', function(e) {
                // Find the dropdown menu
                const dropdownMenu = this.nextElementSibling ||
                                    this.parentElement.querySelector('.dropdown-menu');

                if (dropdownMenu) {
                    // Toggle dropdown visibility
                    if (dropdownMenu.style.display === 'block') {
                        dropdownMenu.style.display = 'none';
                    } else {
                        dropdownMenu.style.display = 'block';
                    }

                    // Prevent default behavior
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        });

        // Get all dropdown menus
        const dropdownMenus = document.querySelectorAll('.dropdown-menu');

        // Add mouseenter event listener to each menu
        dropdownMenus.forEach(menu => {
            menu.addEventListener('mouseenter', function() {
                this.style.display = 'block';
            });

            // Add mouseleave event listener to each menu
            menu.addEventListener('mouseleave', function() {
                // Only hide if not clicked
                if (!this.classList.contains('clicked')) {
                    this.style.display = 'none';
                }
            });
        });

        // Get all dropdown containers
        const dropdownContainers = document.querySelectorAll('.navbar-dropdown');

        // Add mouseenter event listener to each container
        dropdownContainers.forEach(container => {
            container.addEventListener('mouseenter', function() {
                const menu = this.querySelector('.dropdown-menu');
                if (menu) {
                    menu.style.display = 'block';
                }
            });

            // Add mouseleave event listener to each container
            container.addEventListener('mouseleave', function() {
                const menu = this.querySelector('.dropdown-menu');
                if (menu && !menu.classList.contains('clicked')) {
                    menu.style.display = 'none';
                }
            });
        });

        // Get all dropdown items
        const dropdownItems = document.querySelectorAll('.dropdown-menu a, .dropdown-item');

        // Add click event listeners to each item
        dropdownItems.forEach(item => {
            // Remove any existing click event listeners
            const newItem = item.cloneNode(true);
            item.parentNode.replaceChild(newItem, item);

            // Add new click event listener
            newItem.addEventListener('click', function(e) {
                // Allow the click to proceed
                e.stopPropagation();
            });
        });

        // Specifically fix Resources dropdown
        document.querySelectorAll('.navbar-link').forEach(link => {
            if (link.textContent.trim() === 'Resources') {
                // Remove any existing event listeners
                const newLink = link.cloneNode(true);
                link.parentNode.replaceChild(newLink, link);

                // Add new event listeners
                newLink.addEventListener('click', function(e) {
                    // Find the dropdown menu
                    const dropdownMenu = this.nextElementSibling ||
                                        this.parentElement.querySelector('.dropdown-menu');

                    if (dropdownMenu) {
                        // Toggle dropdown visibility
                        if (dropdownMenu.style.display === 'block') {
                            dropdownMenu.style.display = 'none';
                        } else {
                            dropdownMenu.style.display = 'block';
                        }

                        // Prevent default behavior
                        e.preventDefault();
                        e.stopPropagation();
                    }
                });

                // Add mouseenter event listener
                newLink.addEventListener('mouseenter', function() {
                    // Find the dropdown menu
                    const dropdownMenu = this.nextElementSibling ||
                                        this.parentElement.querySelector('.dropdown-menu');

                    if (dropdownMenu) {
                        dropdownMenu.style.display = 'block';
                    }
                });
            }
        });

        // COMPLETELY DISABLE DROPDOWN JAVASCRIPT - LET CSS HANDLE IT
        // No JavaScript interference with dropdown hover behavior
    }

    // Run when DOM is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            fixDropdownFunctionality();
            setTimeout(fixDropdownFunctionality, 100);
            setTimeout(fixDropdownFunctionality, 500); // Additional delay for user dropdown
        });
    } else {
        fixDropdownFunctionality();
        setTimeout(fixDropdownFunctionality, 100);
        setTimeout(fixDropdownFunctionality, 500); // Additional delay for user dropdown
    }

    // Run when window is loaded
    window.addEventListener('load', function() {
        fixDropdownFunctionality();
        setTimeout(fixDropdownFunctionality, 100);
        setTimeout(fixDropdownFunctionality, 500); // Additional delay for user dropdown
    });
})();
