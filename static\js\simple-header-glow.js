/**
 * Simple Header Highlighting
 * Highlights section headers when their sections are in view
 * Works in coordination with the master navigation handler
 */
(function() {
    'use strict';

    let sectionHeaders = [];
    let isInitialized = false;

    // Initialize header highlighting
    function init() {
        if (isInitialized) return;

        // Add the section-header-glow class to all section headers
        sectionHeaders = Array.from(document.querySelectorAll('section h2, section h3'));

        sectionHeaders.forEach(header => {
            header.classList.add('section-header-glow');

            // Store the section ID as a data attribute on the header
            const section = header.closest('section');
            if (section && section.id) {
                header.setAttribute('data-section', section.id);
            }
        });

        // Set up click handlers
        setupClickHandlers();

        // Set up scroll handler
        setupScrollHandler();

        isInitialized = true;
        console.log('Header highlighting initialized');
    }

    // Set up click handlers for section headers
    function setupClickHandlers() {
        sectionHeaders.forEach(header => {
            header.addEventListener('click', function() {
                const sectionId = this.getAttribute('data-section');
                if (sectionId) {
                    // Use the navigation handler to scroll to section
                    if (window.navigationHandler) {
                        window.navigationHandler.scrollToSection(sectionId);
                    } else {
                        // Fallback scroll behavior
                        const section = document.getElementById(sectionId);
                        if (section) {
                            const headerOffset = 80;
                            const elementPosition = section.getBoundingClientRect().top;
                            const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                            window.scrollTo({
                                top: offsetPosition,
                                behavior: 'smooth'
                            });
                        }
                    }

                    // Highlight this header
                    highlightHeader(sectionId);
                }
            });
        });
    }

    // Set up optimized scroll handler
    function setupScrollHandler() {
        let ticking = false;

        function handleScroll() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    updateHeaderHighlighting();
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', handleScroll, { passive: true });
    }

    // Update header highlighting based on scroll position
    function updateHeaderHighlighting() {
        const sections = document.querySelectorAll('section[id]');
        let currentSection = null;
        let maxVisibility = 0;

        // Find the section that's most visible in the viewport
        sections.forEach(section => {
            const rect = section.getBoundingClientRect();
            const sectionTop = rect.top;
            const sectionBottom = rect.bottom;
            const viewportHeight = window.innerHeight;

            // Calculate visible area
            const visibleTop = Math.max(sectionTop, 0);
            const visibleBottom = Math.min(sectionBottom, viewportHeight);
            const visibleHeight = Math.max(0, visibleBottom - visibleTop);
            const totalHeight = rect.height;

            // Calculate visibility ratio
            const visibilityRatio = totalHeight > 0 ? visibleHeight / totalHeight : 0;

            // Use the section with the highest visibility ratio
            if (visibilityRatio > maxVisibility && visibilityRatio > 0.3) {
                maxVisibility = visibilityRatio;
                currentSection = section.id;
            }
        });

        // Update header highlighting
        if (currentSection) {
            highlightHeader(currentSection);
        } else {
            clearAllHeaders();
        }
    }

    // Highlight a specific header
    function highlightHeader(sectionId) {
        // Remove active class from all headers
        sectionHeaders.forEach(header => {
            if (header.classList.contains('active')) {
                header.classList.remove('active');
            }
        });

        // Add active class to the header in the current section
        const currentSectionElement = document.getElementById(sectionId);
        if (currentSectionElement) {
            const currentHeader = currentSectionElement.querySelector('h2, h3');
            if (currentHeader && !currentHeader.classList.contains('active')) {
                currentHeader.classList.add('active');
            }
        }
    }

    // Clear all header highlighting
    function clearAllHeaders() {
        sectionHeaders.forEach(header => {
            if (header.classList.contains('active')) {
                header.classList.remove('active');
            }
        });
    }

    // Public API
    window.headerHighlighting = {
        init: init,
        highlight: highlightHeader,
        clear: clearAllHeaders
    };

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Re-initialize if needed (for dynamic content)
    window.addEventListener('load', () => {
        if (!isInitialized) {
            init();
        }
    });

})();
