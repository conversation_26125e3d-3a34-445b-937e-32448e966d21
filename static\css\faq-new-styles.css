/**
 * New FAQ Styles - Completely redesigned
 */

/* Container */
.faq-container {
    max-width: 550px !important;
    margin: 1rem auto !important;
    padding: 1rem !important;
    border: 3px solid #4f46e5 !important;
    border-radius: 8px !important;
}

/* FAQ Items */
.faq-item {
    border-radius: 1.5rem !important; /* Much more rounded edges */
    background: rgba(255, 255, 255, 0.5) !important; /* Subtle background */
    border: 1px solid rgba(0, 0, 0, 0.05) !important; /* Very subtle border */
    overflow: hidden !important;
    transition: all 0.3s ease !important;
    margin-bottom: 0.75rem !important;
    max-width: 100% !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important; /* Subtle shadow */
}

.dark-theme .faq-item {
    background: rgba(30, 30, 30, 0.5) !important; /* Subtle dark background */
    border: 1px solid rgba(255, 255, 255, 0.05) !important; /* Very subtle border for dark mode */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2) !important; /* Subtle shadow for dark mode */
}

/* Questions */
.faq-question {
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    padding: 0.75rem !important;
    background: #f7fafc !important;
    border: none !important;
    border-radius: 1.5rem !important; /* Much more rounded edges for questions */
    text-align: left !important;
    cursor: pointer !important;
    font-weight: 600 !important;
    font-size: 0.95rem !important;
    color: #2d3748 !important;
    transition: all 0.3s ease !important;
}

.dark-theme .faq-question {
    color: #e2e8f0 !important;
    background: #1e293b !important;
    border-radius: 1.5rem !important; /* Much more rounded edges for questions in dark mode */
}

/* Question Icons */
.question-icon {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    width: 1.75rem !important;
    height: 1.75rem !important;
    border-radius: 50% !important;
    margin-right: 0.75rem !important;
    background-color: #4f46e5 !important;
    color: white !important;
    flex-shrink: 0 !important;
}

.dark-theme .question-icon {
    background-color: #818cf8 !important;
    color: #1e1b4b !important;
}

/* Answer Content */
.answer-content {
    padding: 0 1rem 1rem 3rem !important;
    color: #4a5568 !important;
    font-size: 0.9rem !important;
    background-color: #f8fafc !important;
    border-top: 1px dashed #e2e8f0 !important;
}

.dark-theme .answer-content {
    color: #a0aec0 !important;
    background-color: #1a202c !important;
    border-top: 1px dashed #2d3748 !important;
}

/* Search Box */
.search-wrapper {
    max-width: 500px !important;
}

#faqSearch {
    border: 2px solid #4f46e5 !important;
    height: 40px !important;
}

.search-button {
    background-color: #4f46e5 !important;
    height: 32px !important;
}

/* Dark Mode Toggle */
.theme-toggle-container {
    position: absolute !important;
    top: 20px !important;
    right: 20px !important;
    z-index: 100 !important;
}

/* Force these styles with !important to override any existing styles */
