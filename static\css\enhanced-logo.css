/**
 * Enhanced <PERSON><PERSON> Styling
 * Black SVG logo with torch icon replacing first 'i' in Ziantrix
 * Works perfectly in both light and dark modes
 */

/* Logo container styling */
.navbar-logo {
    /* Position and layout */
    position: absolute !important;
    left: 25px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;

    /* Display properties */
    display: flex !important;
    align-items: center !important;
    height: 100% !important;

    /* Text styling for logo */
    font-size: 2.5rem !important;
    font-weight: 900 !important;
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif !important;
    color: #000000 !important;
    letter-spacing: -0.02em !important;

    /* Force black color and remove all background effects */
    background: none !important;
    background-image: none !important;
    background-color: transparent !important;
    -webkit-background-clip: initial !important;
    background-clip: initial !important;
    text-shadow: none !important;

    /* Transitions */
    transition: transform 0.3s ease, filter 0.3s ease !important;

    /* Ensure visibility */
    opacity: 1 !important;
    visibility: visible !important;
    text-decoration: none !important;
}

/* Image Logo styling - Works for both PNG and SVG */
.navbar-logo img,
.navbar-logo .logo-svg {
    height: 46px !important;
    width: auto !important;

    /* Remove filters to allow natural colors to show */
    filter: none !important;

    /* Smooth transitions */
    transition: filter 0.3s ease, transform 0.3s ease !important;
}

/* Hover effect for both modes */
.navbar-logo:hover {
    transform: translateY(calc(-50% - 2px)) !important;
}

.navbar-logo:hover img,
.navbar-logo:hover .logo-svg {
    /* Subtle hover effect without changing colors */
    filter: brightness(1.1) !important;
}

/* DARK THEME - Keep natural colors */
.dark-theme .navbar-logo img,
.dark-theme .navbar-logo .logo-svg {
    /* Allow image/SVG to handle its own colors */
    filter: none !important;
}

.dark-theme .navbar-logo:hover img,
.dark-theme .navbar-logo:hover .logo-svg {
    /* Subtle hover effect */
    filter: brightness(1.1) !important;
}

/* Adjust navbar height to accommodate larger logo */
.navbar {
    height: 60px !important;
}

/* Adjust navbar links vertical alignment */
.nav-links {
    height: 60px !important;
}

.navbar-link {
    line-height: 60px !important;
}

/* Force black color overrides for all states */
.navbar-logo,
.navbar-logo:hover,
.navbar-logo:focus,
.navbar-logo:active,
.dark-theme .navbar-logo,
.dark-theme .navbar-logo:hover,
.dark-theme .navbar-logo:focus,
.dark-theme .navbar-logo:active {
    color: #000000 !important;
    background: none !important;
    background-image: none !important;
    background-color: transparent !important;
    -webkit-background-clip: initial !important;
    background-clip: initial !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .navbar-logo {
        left: 10px !important;
        color: #000000 !important;
        background: none !important;
        background-image: none !important;
    }

    .navbar-logo img,
    .navbar-logo .logo-svg {
        height: 32px !important; /* 25% smaller for mobile */
    }

    .navbar {
        height: 50px !important;
    }

    .nav-links {
        height: 50px !important;
    }

    .navbar-link {
        line-height: 50px !important;
    }
}
