/**
 * Chatbot But<PERSON> Fix
 * This CSS ensures the chatbot buttons work properly and have the right size
 */

/* Ensure chatbot control buttons are properly sized and clickable */
.chatbot-controls button {
    font-size: 22px !important;
    width: 36px !important;
    height: 36px !important;
    cursor: pointer !important;
    z-index: 1001 !important;
    position: relative !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(255, 255, 255, 0.3) !important;
    border: none !important;
    color: white !important;
    border-radius: 50% !important;
    margin-left: 8px !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3) !important;
    pointer-events: auto !important;
}

.chatbot-controls button:hover {
    background-color: rgba(255, 255, 255, 0.5) !important;
    transform: scale(1.1) !important;
}

.chatbot-controls button:active {
    transform: scale(0.95) !important;
}

/* Increase text size in chatbot messages */
.message-content p {
    font-size: 16px !important;
    line-height: 1.5 !important;
}

/* Ensure the chatbot widget is properly positioned and visible when active */
.chatbot-widget.active {
    transform: translateY(0) !important;
    opacity: 1 !important;
    pointer-events: all !important;
}

/* Fix for chatbot launcher to ensure it's visible when needed */
.chatbot-launcher {
    display: flex !important;
    pointer-events: auto !important;
}

.chatbot-launcher.hidden {
    display: none !important;
}
