#!/usr/bin/env python3
"""
Test script for Ziantrix Dynamic App database functionality
"""
import os
import sys
import logging
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app
from models import db, FormSubmission, ChatConversation, ChatMessage
from database import FormManager, ChatManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_database_connection():
    """Test basic database connection"""
    logger.info("🔍 Testing database connection...")

    with app.app_context():
        try:
            db.session.execute(db.text('SELECT 1'))
            logger.info("✅ Database connection successful")
            return True
        except Exception as e:
            logger.error(f"❌ Database connection failed: {str(e)}")
            return False

def test_user_operations():
    """User operations test removed - authentication disabled"""
    logger.info("👤 User operations test skipped - authentication disabled")
    return True

def test_form_operations():
    """Test form submission operations"""
    logger.info("📝 Testing form operations...")

    with app.app_context():
        try:
            # Test form submission
            submission, error = FormManager.save_form_submission(
                form_type="contact",
                name="Test Submitter",
                email="<EMAIL>",
                message="This is a test message",
                inquiry_type="General",
                source_page="test",
                ip_address="127.0.0.1",
                user_agent="Test Agent"
            )

            if error:
                logger.error(f"❌ Form submission failed: {error}")
                return False

            logger.info(f"✅ Form submission created: ID {submission.id}")

            # Test getting submissions
            submissions = FormManager.get_user_submissions(submission.user_id if submission.user_id else 0)
            logger.info(f"✅ Retrieved {len(submissions)} submissions")

            # Clean up
            db.session.delete(submission)
            db.session.commit()
            logger.info("🧹 Test submission cleaned up")

            return True

        except Exception as e:
            logger.error(f"❌ Form operations test failed: {str(e)}")
            return False

def test_chat_operations():
    """Test chat conversation operations"""
    logger.info("💬 Testing chat operations...")

    with app.app_context():
        try:
            # Test conversation creation
            session_id = "test_session_123"
            device_id = "test_device_456"

            conversation = ChatManager.create_conversation(
                session_id=session_id,
                device_id=device_id
            )

            if not conversation:
                logger.error("❌ Conversation creation failed")
                return False

            logger.info(f"✅ Conversation created: ID {conversation.id}")

            # Test message saving
            user_message = ChatManager.save_message(
                conversation_id=conversation.id,
                message_type="user",
                content="Hello, this is a test message"
            )

            if not user_message:
                logger.error("❌ User message saving failed")
                return False

            bot_message = ChatManager.save_message(
                conversation_id=conversation.id,
                message_type="bot",
                content="Hello! This is a test response"
            )

            if not bot_message:
                logger.error("❌ Bot message saving failed")
                return False

            logger.info("✅ Messages saved successfully")

            # Test conversation retrieval
            retrieved_conversation = ChatConversation.query.get(conversation.id)
            if len(retrieved_conversation.messages) != 2:
                logger.error("❌ Message count mismatch")
                return False

            logger.info("✅ Conversation retrieval successful")

            # Clean up
            db.session.delete(conversation)
            db.session.commit()
            logger.info("🧹 Test conversation cleaned up")

            return True

        except Exception as e:
            logger.error(f"❌ Chat operations test failed: {str(e)}")
            return False

def test_database_schema():
    """Test database schema and table creation"""
    logger.info("🏗️ Testing database schema...")

    with app.app_context():
        try:
            # Check if all tables exist
            tables = db.engine.table_names()
            expected_tables = ['users', 'form_submissions', 'chat_conversations', 'chat_messages', 'analytics']

            missing_tables = [table for table in expected_tables if table not in tables]
            if missing_tables:
                logger.error(f"❌ Missing tables: {missing_tables}")
                return False

            logger.info("✅ All expected tables found")

            # Test table constraints and relationships
            user_count = User.query.count()
            form_count = FormSubmission.query.count()
            chat_count = ChatConversation.query.count()

            logger.info(f"📊 Current record counts:")
            logger.info(f"   Users: {user_count}")
            logger.info(f"   Form submissions: {form_count}")
            logger.info(f"   Chat conversations: {chat_count}")

            return True

        except Exception as e:
            logger.error(f"❌ Schema test failed: {str(e)}")
            return False

def run_all_tests():
    """Run all database tests"""
    logger.info("🚀 Starting comprehensive database tests...")

    tests = [
        ("Database Connection", test_database_connection),
        ("Database Schema", test_database_schema),
        ("User Operations", test_user_operations),
        ("Form Operations", test_form_operations),
        ("Chat Operations", test_chat_operations),
    ]

    results = {}

    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running: {test_name}")
        logger.info(f"{'='*50}")

        try:
            results[test_name] = test_func()
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {str(e)}")
            results[test_name] = False

    # Print summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")

    passed = 0
    total = len(tests)

    for test_name, result in results.items():
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1

    logger.info(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 All tests passed! Database is working correctly.")
        return True
    else:
        logger.error("⚠️ Some tests failed. Please check the logs above.")
        return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Test Ziantrix database functionality")
    parser.add_argument("--connection", action="store_true", help="Test database connection only")
    parser.add_argument("--schema", action="store_true", help="Test database schema only")
    parser.add_argument("--users", action="store_true", help="Test user operations only")
    parser.add_argument("--forms", action="store_true", help="Test form operations only")
    parser.add_argument("--chats", action="store_true", help="Test chat operations only")
    parser.add_argument("--all", action="store_true", help="Run all tests (default)")

    args = parser.parse_args()

    if args.connection:
        test_database_connection()
    elif args.schema:
        test_database_schema()
    elif args.users:
        test_user_operations()
    elif args.forms:
        test_form_operations()
    elif args.chats:
        test_chat_operations()
    else:
        run_all_tests()
