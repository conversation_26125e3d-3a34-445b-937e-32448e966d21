/**
 * Modern FAQ Page Styles
 */

/* Base Styles */
body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8fafc;
    margin: 0;
    padding: 0;
}

/* Container */
.faq-container {
    max-width: 550px; /* Further reduced width */
    margin: 1rem auto;
    padding: 1rem;
    border: 2px solid #e2e8f0; /* Added visible border */
    border-radius: 8px;
}

/* Header */
.faq-header {
    text-align: center;
    margin-bottom: 3rem;
}

.faq-header h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #1a202c;
}

/* Search */
.search-container {
    margin: 2rem 0;
}

.search-wrapper {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
}

#faqSearch {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    font-size: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

#faqSearch:focus {
    outline: none;
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.2);
}

.search-button {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background-color: #4299e1;
    color: white;
    border: none;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    cursor: pointer;
}

.search-button:hover {
    background-color: #3182ce;
}

.clear-button {
    position: absolute;
    right: 5.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    display: none;
}

#faqSearch:not(:placeholder-shown) ~ .clear-button {
    display: block;
}

/* FAQ List */
.faq-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.faq-item {
    border-radius: 1.5rem; /* Much more rounded edges */
    background: rgba(255, 255, 255, 0.5); /* Subtle background */
    border: 1px solid rgba(0, 0, 0, 0.05); /* Very subtle border */
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 0.75rem; /* Reduced spacing */
    max-width: 100%; /* Ensure it doesn't overflow */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05); /* Subtle shadow */
}

.dark-theme .faq-item {
    background: rgba(30, 30, 30, 0.5); /* Subtle dark background */
    border: 1px solid rgba(255, 255, 255, 0.05); /* Very subtle border for dark mode */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* Subtle shadow for dark mode */
}

.faq-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
}

.dark-theme .faq-item:hover {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.faq-question {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0.75rem; /* Reduced padding */
    background: #f7fafc; /* Light background */
    border: none;
    border-radius: 1.5rem; /* Much more rounded edges for questions */
    text-align: left;
    cursor: pointer;
    font-weight: 600;
    font-size: 0.95rem; /* Smaller font */
    color: #2d3748;
    transition: all 0.3s ease;
}

.dark-theme .faq-question {
    color: #e2e8f0;
    background: #1e293b; /* Dark background */
    border-radius: 1.5rem; /* Much more rounded edges for questions in dark mode */
}

.faq-question:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.dark-theme .faq-question:hover {
    background-color: rgba(255, 255, 255, 0.02);
}

.question-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 1.75rem; /* Smaller icon */
    height: 1.75rem; /* Smaller icon */
    border-radius: 50%; /* Circular icon */
    margin-right: 0.75rem;
    background-color: #4f46e5; /* Bright purple background */
    color: white; /* White icon color */
    flex-shrink: 0;
}

.dark-theme .question-icon {
    background-color: #818cf8; /* Lighter purple for dark mode */
    color: #1e1b4b; /* Dark text for contrast */
}

.question-text {
    flex-grow: 1;
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease;
    background: none;
}

.faq-question[aria-expanded="true"] + .faq-answer {
    max-height: 800px;
}

.answer-content {
    padding: 0 1rem 1rem 3rem; /* Reduced left padding */
    color: #4a5568;
    font-size: 0.9rem; /* Smaller font */
    background-color: #f8fafc; /* Very light background */
    border-top: 1px dashed #e2e8f0; /* Dashed separator */
}

.dark-theme .answer-content {
    color: #a0aec0;
    background-color: #1a202c; /* Dark background */
    border-top: 1px dashed #2d3748; /* Darker dashed separator */
}

.answer-content p {
    margin-bottom: 1rem;
}

.answer-content ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.answer-content li {
    margin-bottom: 0.5rem;
}

.answer-content strong {
    color: #3182ce;
    font-weight: 600;
}

/* No Results */
.no-results {
    text-align: center;
    padding: 3rem 1rem;
    color: #718096;
    display: none;
}

.no-results.visible {
    display: block;
}

.no-results-icon {
    font-size: 2.5rem;
    color: #cbd5e0;
    margin-bottom: 1.5rem;
}

.no-results h3 {
    margin-bottom: 0.75rem;
    color: #2d3748;
    font-size: 1.25rem;
}

.reset-button {
    margin-top: 1rem;
    padding: 0.75rem 1.5rem;
    background-color: #4299e1;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
}

.reset-button:hover {
    background-color: #3182ce;
}

/* Responsive */
@media (max-width: 768px) {
    .faq-container {
        padding: 1.25rem;
        max-width: 600px;
    }

    .faq-header h1 {
        font-size: 1.8rem;
    }

    .question-icon {
        width: 1.75rem;
        height: 1.75rem;
    }

    .faq-question {
        font-size: 0.95rem;
        padding: 0.9rem;
    }

    .answer-content {
        padding: 0 0.9rem 0.9rem 3.25rem;
    }
}

@media (max-width: 480px) {
    .faq-container {
        padding: 1rem;
        max-width: 100%;
    }

    .faq-header h1 {
        font-size: 1.6rem;
    }

    .search-button {
        padding: 0.4rem 0.6rem;
        font-size: 0.875rem;
    }

    .clear-button {
        right: 4.25rem;
    }

    #faqSearch {
        padding-right: 5rem;
    }

    .question-icon {
        width: 1.5rem;
        height: 1.5rem;
        margin-right: 0.6rem;
    }

    .answer-content {
        padding: 0 0.75rem 0.75rem 2.75rem;
    }
}
