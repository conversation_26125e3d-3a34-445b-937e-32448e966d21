/**
 * Modern Feature Grid
 * Clean, minimalist, and professional feature cards
 * Following SaaS aesthetic similar to Stripe, Linear, or Intercom
 */

/* Features Section */
.features-section {
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
  background-color: var(--color-bg-primary);
}

/* Section Header */
.features-section .section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 3rem auto;
}

.features-section .section-title {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--color-text-primary);
}

.features-section .section-description {
  font-size: 1.125rem;
  color: var(--color-text-secondary);
  max-width: 600px;
  margin: 0 auto;
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(3, minmax(0, 360px));
  gap: 1.5rem;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Feature Card */
.feature-card {
  background-color: transparent;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  padding: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  overflow: hidden;
  max-width: 360px;
  margin: 0 auto;
  align-items: center;
}

.feature-card:hover {
  background-color: transparent;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  transform: translateY(-2px);
}

/* Feature Icon */
.feature-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem auto;
  color: #4f46e5;
  background-color: #eef2ff;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
}

.feature-icon i {
  font-size: 1.5rem;
}

/* Feature Title */
.feature-title {
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  margin-bottom: 0.75rem;
  color: #0f172a;
  text-align: center;
}

/* Feature Description */
.feature-description {
  font-size: 0.875rem; /* text-sm */
  line-height: 1.5;
  color: #475569;
  margin-bottom: 1rem;
  text-align: center;
  max-width: 90%;
  background: transparent;
}

/* Feature Metric */
.feature-metric {
  font-size: 2rem;
  font-weight: 600;
  color: #4f46e5;
  margin-bottom: 0.25rem;
  line-height: 1.2;
  text-align: center;
  background: transparent;
  margin-top: 0.75rem;
}

.feature-metric-label {
  font-size: 0.75rem; /* text-xs */
  font-weight: 500;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: #64748b; /* muted color */
  margin-bottom: 0.5rem;
  text-align: center;
  background: transparent;
}

.feature-proof-point {
  font-size: 0.75rem;
  font-style: italic;
  color: #64748b;
  text-align: center;
  margin-bottom: 1.5rem;
  max-width: 90%;
  background: transparent;
}

/* Feature Link */
.feature-link {
  display: inline-flex;
  align-items: center;
  color: #4f46e5;
  font-weight: 500;
  font-size: 0.875rem; /* text-sm */
  text-decoration: none;
  transition: all 0.2s ease;
  margin: 0 auto;
  justify-content: center;
  width: 100%;
  text-align: center;
  margin-top: auto;
  background: transparent;
  padding: 0;
}

.feature-link i {
  margin-left: 0.5rem;
  transition: transform 0.2s ease;
}

.feature-card:hover .feature-link {
  color: #4338ca;
}

.feature-card:hover .feature-link i {
  transform: translateX(4px);
}

.feature-link:hover {
  text-decoration: underline;
}

/* Dark Mode Styles */
.dark-theme .feature-card {
  background-color: transparent;
  border-color: #3c3c3c;
}

.dark-theme .feature-card:hover {
  background-color: transparent;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.dark-theme .feature-icon {
  background-color: #312e81;
  color: #818cf8;
}

.dark-theme .feature-title {
  color: #f8fafc;
}

.dark-theme .feature-description {
  color: #cbd5e1;
  background: transparent;
}

.dark-theme .feature-metric {
  color: #818cf8;
  background: transparent;
}

.dark-theme .feature-metric-label {
  color: #94a3b8;
  background: transparent;
}

.dark-theme .feature-proof-point {
  color: #94a3b8;
  background: transparent;
}

.dark-theme .feature-link {
  color: #818cf8;
  background: transparent;
}

.dark-theme .feature-card:hover .feature-link {
  color: #a5b4fc;
}

/* Responsive Styles */
@media (max-width: 1023px) {
  .features-grid {
    grid-template-columns: repeat(2, minmax(0, 360px));
  }
}

@media (max-width: 767px) {
  .features-grid {
    grid-template-columns: minmax(0, 360px);
  }

  .features-section {
    padding: 3rem 0;
  }

  .feature-card {
    padding: 1.5rem;
  }
}

/* Animation for scroll effects */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.feature-card {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
  will-change: transform, opacity;
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease, background-color 0.3s ease;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }

/* Add smooth transition for all elements inside the card */
.feature-card *,
.feature-card *::before,
.feature-card *::after {
  transition: all 0.3s ease;
}
