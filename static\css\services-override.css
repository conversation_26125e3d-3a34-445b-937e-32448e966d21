/**
 * Services Override CSS
 * This file contains high-specificity overrides for the services section
 * to ensure our professional styling is applied correctly
 */

/* Services Section Container */
section.services-section.section-spacing {
  padding: 80px 0 !important;
  width: 100% !important;
  background-color: var(--color-background) !important;
}

.dark-theme section.services-section.section-spacing {
  background-color: var(--color-background-dark) !important;
}

/* Section Header */
section.services-section .section-header {
  text-align: center !important;
  margin-bottom: 60px !important;
}

section.services-section .section-title {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 16px !important;
  color: var(--color-heading) !important;
  position: relative !important;
  display: inline-block !important;
}

section.services-section .section-title::after {
  content: '' !important;
  position: absolute !important;
  bottom: -12px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 80px !important;
  height: 3px !important;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-light) 100%) !important;
  border-radius: 3px !important;
}

.dark-theme section.services-section .section-title {
  color: var(--color-heading-dark) !important;
}

section.services-section .section-description {
  font-size: 1.2rem !important;
  color: var(--color-text-light) !important;
  max-width: 700px !important;
  margin: 0 auto !important;
}

.dark-theme section.services-section .section-description {
  color: var(--color-text-light-dark) !important;
}

/* Services Grid */
section.services-section .services-grid {
  display: grid !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 30px !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* Service Card */
section.services-section .service-card {
  display: flex !important;
  flex-direction: column !important;
  background-color: var(--color-card-bg) !important;
  border-radius: 12px !important;
  border: 1px solid var(--color-border) !important;
  overflow: hidden !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  height: 100% !important;
  position: relative !important;
  min-height: 420px !important; /* Ensure consistent height */
}

section.services-section .service-card::before {
  display: none !important;
  content: none !important;
}

section.services-section .service-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
}

.dark-theme section.services-section .service-card {
  background-color: var(--color-card-bg-dark) !important;
  border-color: var(--color-border-dark) !important;
}

.dark-theme section.services-section .service-card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
}

/* Service Card Header */
section.services-section .service-card-header {
  display: flex !important;
  align-items: flex-start !important;
  padding: 24px 24px 20px !important;
  border-bottom: 1px solid var(--color-border) !important;
  text-align: left !important;
  background-color: transparent !important;
}

.dark-theme section.services-section .service-card-header {
  border-color: var(--color-border-dark) !important;
  background-color: transparent !important;
}

section.services-section .service-icon {
  width: 56px !important;
  height: 56px !important;
  margin-right: 16px !important;
  margin-bottom: 0 !important;
  margin-left: 0 !important;
  margin-top: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

section.services-section .service-icon img {
  width: 100% !important;
  height: auto !important;
}

section.services-section .service-header-text {
  display: flex !important;
  flex-direction: column !important;
  text-align: left !important;
}

section.services-section .service-title {
  font-size: 1.5rem !important;
  font-weight: 700 !important;
  margin-bottom: 4px !important;
  color: var(--color-primary) !important;
  text-align: left !important;
}

.dark-theme section.services-section .service-title {
  color: var(--color-primary-light) !important;
}

section.services-section .service-subtitle {
  font-size: 1rem !important;
  color: var(--color-text-light) !important;
  margin: 0 !important;
  text-align: left !important;
}

.dark-theme section.services-section .service-subtitle {
  color: var(--color-text-light-dark) !important;
}

/* Service Card Body */
section.services-section .service-card-body {
  padding: 24px !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
  padding-bottom: 80px !important; /* Add space for footer */
  flex-grow: 1 !important;
}

/* Service Features List */
section.services-section .service-features-list {
  list-style-type: none !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
}

section.services-section .service-features-list li {
  display: flex !important;
  align-items: flex-start !important;
  margin-bottom: 16px !important;
  text-align: left !important;
  padding: 0 !important;
  width: 100% !important;
  justify-content: flex-start !important;
  padding-left: 24px !important;
}

section.services-section .service-features-list li:last-child {
  margin-bottom: 0 !important;
}

section.services-section .service-features-list .feature-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-right: 16px !important;
  color: var(--color-primary) !important;
  background: none !important;
  width: 28px !important;
  height: 28px !important;
  flex-shrink: 0 !important;
  text-align: center !important;
  min-width: 28px !important;
}

.dark-theme section.services-section .service-features-list .feature-icon {
  color: var(--color-primary-light) !important;
}

section.services-section .service-features-list .feature-text {
  font-size: 1rem !important;
  line-height: 1.5 !important;
  color: var(--color-text) !important;
  text-align: left !important;
  display: inline-block !important;
  vertical-align: middle !important;
  flex: 1 !important;
  padding-top: 2px !important;
  padding-bottom: 2px !important;
}

.dark-theme section.services-section .service-features-list .feature-text {
  color: var(--color-text-dark) !important;
}

/* Service Card Footer */
section.services-section .service-card-footer {
  padding: 0 24px 24px !important;
  margin-top: auto !important;
  position: absolute !important;
  bottom: 24px !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  box-sizing: border-box !important;
  border-top: none !important;
  background: transparent !important;
}

section.services-section .service-cta-button {
  display: flex !important;
  width: 100% !important;
  padding: 14px 20px !important;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  text-align: center !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  border: none !important;
  cursor: pointer !important;
  min-height: 48px !important;
  line-height: 1.2 !important;
  box-sizing: border-box !important;
  justify-content: center !important;
  align-items: center !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
}

section.services-section .service-cta-button:hover {
  background: linear-gradient(90deg, var(--color-primary-dark) 0%, var(--color-primary) 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 15px rgba(var(--color-primary-rgb), 0.3) !important;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  section.services-section .services-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    padding: 0 20px !important;
  }
}

@media (max-width: 992px) {
  section.services-section .services-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 768px) {
  section.services-section {
    padding: 60px 0 !important;
  }

  section.services-section .section-title {
    font-size: 2rem !important;
  }

  section.services-section .section-description {
    font-size: 1rem !important;
  }

  section.services-section .service-card-header {
    padding: 20px 20px 16px !important;
  }

  section.services-section .service-card-body {
    padding: 20px !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-bottom: 80px !important;
  }

  section.services-section .service-card-footer {
    padding: 0 20px 20px !important;
  }

  section.services-section .services-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (max-width: 480px) {
  section.services-section {
    padding: 40px 0 !important;
  }

  section.services-section .section-title {
    font-size: 1.75rem !important;
  }

  section.services-section .service-icon {
    width: 48px !important;
    height: 48px !important;
  }

  section.services-section .service-title {
    font-size: 1.25rem !important;
  }

  section.services-section .service-subtitle {
    font-size: 0.875rem !important;
  }
}
