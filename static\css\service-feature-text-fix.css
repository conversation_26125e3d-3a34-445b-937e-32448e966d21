/**
 * Service Feature Text Fix
 * This CSS ensures the feature text doesn't overlap with the buttons
 */

/* Ensure proper spacing between feature list and footer */
.service-card-body {
  padding-bottom: 100px !important; /* Increase space for footer */
}

/* Hide the last feature text that's overlapping with buttons */
.service-features-list li:last-child .feature-text,
.service-features-list li:nth-last-child(1) .feature-text {
  visibility: visible !important;
  opacity: 1 !important;
  margin-bottom: 0 !important;
}

/* Ensure feature icons are properly aligned */
.service-features-list .feature-icon {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-right: 16px !important;
  width: 28px !important;
  height: 28px !important;
  min-width: 28px !important;
  flex-shrink: 0 !important;
  background: none !important;
  background-color: transparent !important;
  vertical-align: middle !important;
}

/* Ensure feature text is properly aligned */
.service-features-list .feature-text {
  display: inline-block !important;
  flex: 1 !important;
  text-align: left !important;
  padding-top: 2px !important;
  padding-bottom: 2px !important;
  line-height: 1.5 !important;
  vertical-align: middle !important;
}

/* Ensure service card footer is properly positioned */
.service-card-footer {
  position: absolute !important;
  bottom: 24px !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  box-sizing: border-box !important;
  padding: 0 24px 24px !important;
  margin-top: auto !important;
  border-top: none !important;
  background: transparent !important;
  z-index: 10 !important; /* Ensure footer is above other content */
}

/* Ensure service CTA buttons are properly styled */
.service-cta-button {
  display: flex !important;
  width: 100% !important;
  min-width: 260px !important;
  box-sizing: border-box !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 14px 20px !important;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  text-align: center !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  border: none !important;
  cursor: pointer !important;
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
  z-index: 20 !important; /* Ensure button is above other content */
}

/* Add a white background behind the button to hide any text that might be behind it */
.service-cta-button::before {
  content: "" !important;
  position: absolute !important;
  top: -10px !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: var(--color-card-bg) !important;
  z-index: -1 !important;
}

/* Dark theme adjustments */
.dark-theme .service-cta-button::before {
  background-color: var(--color-card-bg-dark) !important;
}
