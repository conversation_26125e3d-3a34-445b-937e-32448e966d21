/**
 * Ziantrix Main CSS
 * Consolidated, optimized CSS for the Ziantrix web application
 * Following Speed-First Design principles
 */

/* Import design system variables */
@import 'design-system.css';

/* Critical CSS (will be inlined in the head) */
:root {
  /* Core colors for critical rendering */
  --color-primary: #4f46e5;
  --color-primary-light: #818cf8;
  --color-primary-dark: #3730a3;
  --color-accent: #a855f7;
  --color-bg-light: #ffffff;
  --color-bg-dark: #0f172a;
  --color-text-light: #111827;
  --color-text-dark: #f8fafc;
}

/* Base styles for critical rendering */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  scroll-padding-top: 80px; /* For fixed header */
  height: 100%;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: all 0.3s ease;
  overflow-x: hidden;
  min-height: 100%;
  position: relative;
}

/* Light Theme Background */
body {
  background-color: #f8fafc;
  background-attachment: fixed;
  background-size: cover;
}

/* Dark Theme Background */
.dark-theme body {
  background-color: #0f172a;
  background-attachment: fixed;
  background-size: cover;
}

/* Modern Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background-color: #f8fafc;
  transition: all 0.3s ease;
  height: 42px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0;
}

.navbar.scrolled {
  background-color: #f8fafc;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  height: 36px;
}

.dark-theme .navbar {
  background-color: #0f172a;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.dark-theme .navbar.scrolled {
  background-color: #0f172a;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.navbar-container {
  display: flex;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 clamp(1rem, 3vw, 2rem);
  height: 100%;
  justify-content: center;
}

/* Logo */
.navbar-logo {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: 800;
  letter-spacing: -0.025em;
  background: linear-gradient(90deg, var(--color-primary-700), var(--color-primary-500));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  transition: all 0.3s ease;
  position: absolute;
  left: 4px;
}

.navbar-logo:hover {
  transform: translateY(-1px);
  text-shadow: 0 2px 10px rgba(99, 102, 241, 0.3);
}

.dark-theme .navbar-logo {
  background: linear-gradient(90deg, var(--color-primary-400), var(--color-primary-300));
  -webkit-background-clip: text;
  background-clip: text;
}

/* Navigation Links */
.navbar-link {
  display: flex;
  align-items: center;
  padding: 0 0.75rem;
  color: var(--color-text-secondary);
  font-weight: 500;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
  height: 100%;
  line-height: 42px;
  cursor: pointer;
}

.navbar-link:hover {
  color: var(--color-primary-600);
}

.navbar-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: var(--color-primary-600);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.navbar-link:hover::after,
.navbar-link.active::after {
  width: 100%;
}

.navbar-link.active {
  color: var(--color-primary-600);
  font-weight: 600;
  position: relative;
}

.navbar-link.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: var(--color-primary-600);
}

.dark-theme .navbar-link:hover {
  color: var(--color-primary-400);
}

.dark-theme .navbar-link.active {
  color: var(--color-primary-400);
}

.dark-theme .navbar-link.active::after {
  background-color: var(--color-primary-400);
  height: 2px;
}

/* Dropdown Menu */
.navbar-dropdown {
  position: relative;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.dropdown-toggle i {
  font-size: 0.75rem;
  transition: transform 0.3s ease;
}

.dropdown-toggle:hover i,
.navbar-dropdown:hover .dropdown-toggle i {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 220px;
  background-color: var(--color-bg-primary);
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 0.5rem;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.3s ease;
  z-index: 20;
  border: 1px solid var(--color-border);
  margin-top: 0.5rem;
}

.navbar-dropdown:hover .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  padding: 0.5rem 0.75rem;
  color: var(--color-text-secondary);
  text-decoration: none;
  border-radius: 0.25rem;
  transition: all 0.3s ease;
  font-weight: 400;
  font-size: 0.875rem;
}

.dropdown-item:hover {
  background-color: var(--color-bg-secondary);
  color: var(--color-primary-600);
}

.dark-theme .dropdown-menu {
  background-color: var(--color-bg-secondary);
  border-color: var(--color-border);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.dark-theme .dropdown-item:hover {
  background-color: var(--color-bg-tertiary);
  color: var(--color-primary-400);
}

/* Login Button */
.login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.1rem 0.5rem;
  border-radius: 0.375rem;
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-weight: 500;
  font-size: 0.85rem;
  text-decoration: none;
  white-space: nowrap;
  height: 22px;
  line-height: 22px;
  margin-top: 0;
  margin-bottom: 0;
}

.login-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-700) 0%, var(--color-primary-800) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
}

.dark-theme .login-btn {
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  margin-top: 0;
  margin-bottom: 0;
}

.dark-theme .login-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.25);
}

/* Enhanced Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: clamp(4rem, 10vh, 8rem) clamp(1rem, 3vw, 2rem) clamp(3rem, 8vh, 5rem);
  overflow: hidden;
  background-color: transparent;
  width: 100%;
}

/* Background Elements */
.hero-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  overflow: hidden;
}

.hero-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.15) 0%, transparent 40%),
    radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.15) 0%, transparent 40%);
  z-index: -1;
}

.hero-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.5;
  z-index: -2;
}

.dark-theme .hero-bg::before {
  background-image:
    radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.25) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.25) 0%, transparent 50%);
}

.dark-theme .hero-bg::after {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366F1' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.7;
}

/* Floating Elements */
.hero-shape {
  position: absolute;
  border-radius: 50%;
  filter: blur(40px);
  z-index: -1;
  opacity: 0.5;
}

.hero-shape-1 {
  width: 300px;
  height: 300px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.4), rgba(168, 85, 247, 0.4));
  top: 10%;
  left: -5%;
  animation: float 8s ease-in-out infinite alternate;
}

.hero-shape-2 {
  width: 200px;
  height: 200px;
  background: linear-gradient(135deg, rgba(20, 184, 166, 0.4), rgba(45, 212, 191, 0.4));
  bottom: 10%;
  right: -5%;
  animation: float 6s ease-in-out infinite alternate-reverse;
}

.hero-shape-3 {
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(20, 184, 166, 0.3));
  top: 50%;
  right: 15%;
  animation: float 10s ease-in-out infinite;
}

/* Hero Content */
.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  text-align: center;
  z-index: 1;
  padding: clamp(1rem, 3vw, 2rem);
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: clamp(1rem, 3vw, 1.5rem);
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-accent-600));
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  animation: textGradient 5s ease infinite;
  max-width: 900px;
  word-wrap: break-word;
  hyphens: auto;
}

.dark-theme .hero-title {
  background: linear-gradient(135deg, var(--color-primary-400), var(--color-accent-400));
  -webkit-background-clip: text;
  background-clip: text;
  text-shadow: 0 0 30px rgba(99, 102, 241, 0.3);
}

.hero-subtitle {
  font-size: clamp(1rem, 3vw, 1.25rem);
  color: var(--color-text-secondary);
  max-width: 700px;
  margin: 0 auto clamp(1.5rem, 4vw, 2rem);
  line-height: 1.625;
}

.dark-theme .hero-subtitle {
  color: var(--color-text-tertiary);
}

/* CTA Buttons */
.hero-cta {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.hero-cta .btn {
  padding: 0.75rem 1.5rem;
  font-size: 1.125rem;
  font-weight: 500;
  border-radius: 0.5rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-decoration: none;
}

.hero-cta .btn-primary {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  color: white;
  box-shadow: 0 4px 14px rgba(99, 102, 241, 0.4);
  border: none;
}

.hero-cta .btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-700), var(--color-primary-800));
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.5);
  transform: translateY(-3px);
}

.hero-cta .btn-secondary {
  background-color: transparent;
  color: var(--color-primary-600);
  border: 1px solid var(--color-primary-600);
}

.hero-cta .btn-secondary:hover {
  background-color: rgba(99, 102, 241, 0.05);
  color: var(--color-primary-700);
  border-color: var(--color-primary-700);
  transform: translateY(-3px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* ===== RESPONSIVE BREAKPOINTS ===== */

/* Mobile devices (up to 767px) */
@media (max-width: 767px) {
  .navbar {
    height: 60px;
  }

  .navbar-container {
    padding: 0 1rem;
  }

  .navbar-logo {
    left: 1rem;
  }

  .hero-section {
    min-height: 80vh;
    padding: 6rem 1rem 4rem;
  }

  .hero-cta {
    flex-direction: column;
    gap: 1rem;
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
  }

  .hero-cta .btn {
    width: 100%;
    max-width: none;
    padding: 1rem 1.5rem;
    font-size: 1rem;
  }

  .nav-links {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex !important;
  }
}

/* Tablets (768px to 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .navbar-container {
    padding: 0 1.5rem;
  }

  .hero-section {
    padding: 6rem 1.5rem 4rem;
  }

  .hero-cta {
    flex-direction: row;
    gap: 1rem;
  }

  .hero-cta .btn {
    flex: 1;
    max-width: 250px;
  }
}

/* Desktop (1024px and up) */
@media (min-width: 1024px) {
  .navbar-container {
    padding: 0 2rem;
  }

  .hero-section {
    padding: 8rem 2rem 5rem;
  }

  .hero-cta {
    flex-direction: row;
    gap: 1.5rem;
  }

  .hero-cta .btn {
    flex: 0 0 auto;
    min-width: 200px;
  }
}

/* Large desktop (1280px and up) */
@media (min-width: 1280px) {
  .navbar-container {
    max-width: 1400px;
  }

  .hero-content {
    max-width: 1400px;
  }
}

.dark-theme .hero-cta .btn-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  box-shadow: 0 4px 14px rgba(99, 102, 241, 0.5);
}

.dark-theme .hero-cta .btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  box-shadow: 0 6px 20px rgba(99, 102, 241, 0.6);
}

.dark-theme .hero-cta .btn-secondary {
  color: var(--color-primary-400);
  border-color: var(--color-primary-400);
}

.dark-theme .hero-cta .btn-secondary:hover {
  background-color: rgba(99, 102, 241, 0.1);
  color: var(--color-primary-300);
  border-color: var(--color-primary-300);
}

/* Hero Video Container */
.hero-video {
  margin-top: 3rem;
  position: relative;
  max-width: 800px;
  width: 100%;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
}

.hero-video video {
  width: 100%;
  display: block;
}

.hero-video::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.02) 100%);
  pointer-events: none;
}

.video-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background-color: var(--color-primary-600);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  z-index: 2;
}

.video-play-button:hover {
  transform: translate(-50%, -50%) scale(1.1);
  background-color: var(--color-primary-700);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.video-play-button i {
  color: white;
  font-size: 2rem;
  margin-left: 0.25rem;
}

.dark-theme .hero-video {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  border-color: var(--color-border);
}

.dark-theme .video-play-button {
  background-color: var(--color-primary-500);
}

.dark-theme .video-play-button:hover {
  background-color: var(--color-primary-600);
}



/* Animations */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

@keyframes textGradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 0.7; transform: translateY(0); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Responsive Styles */
@media (max-width: 1023px) {
  .hero-title {
    font-size: 2.75rem;
  }

  .hero-subtitle {
    font-size: 1.125rem;
  }

  .hero-cta {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }

  .hero-cta .btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 767px) {
  .hero-section {
    padding-top: 6rem;
    min-height: auto;
  }

  .hero-title {
    font-size: 2.25rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-shape-1 {
    width: 150px;
    height: 150px;
  }

  .hero-shape-2 {
    width: 100px;
    height: 100px;
  }

  .hero-shape-3 {
    width: 80px;
    height: 80px;
  }

  .client-logos {
    gap: 1.5rem;
  }

  .client-logo {
    height: 30px;
  }
}
