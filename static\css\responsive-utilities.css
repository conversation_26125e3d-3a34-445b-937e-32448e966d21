/**
 * Responsive Utilities CSS
 * Additional utility classes and fixes for responsive design
 */

/* ===== RESPONSIVE DISPLAY UTILITIES ===== */

.show-mobile { display: none; }
.show-tablet { display: none; }
.show-desktop { display: block; }

.hide-mobile { display: block; }
.hide-tablet { display: block; }
.hide-desktop { display: none; }

/* ===== RESPONSIVE TEXT UTILITIES ===== */

.text-responsive {
    font-size: clamp(0.875rem, 2vw, 1rem);
    line-height: 1.6;
}

.heading-responsive {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    line-height: 1.2;
}

.subheading-responsive {
    font-size: clamp(1.125rem, 3vw, 1.5rem);
    line-height: 1.3;
}

/* ===== RESPONSIVE SPACING UTILITIES ===== */

.spacing-responsive {
    padding: clamp(1rem, 3vw, 2rem);
}

.margin-responsive {
    margin: clamp(1rem, 3vw, 2rem);
}

.gap-responsive {
    gap: clamp(0.5rem, 2vw, 1rem);
}

/* ===== RESPONSIVE CONTAINER UTILITIES ===== */

.container-fluid {
    width: 100%;
    padding: 0 clamp(1rem, 3vw, 2rem);
    margin: 0 auto;
}

.container-narrow {
    width: 100%;
    max-width: 800px;
    padding: 0 clamp(1rem, 3vw, 2rem);
    margin: 0 auto;
}

.container-wide {
    width: 100%;
    max-width: 1400px;
    padding: 0 clamp(1rem, 3vw, 2rem);
    margin: 0 auto;
}

/* ===== RESPONSIVE FLEX UTILITIES ===== */

.flex-responsive {
    display: flex;
    flex-wrap: wrap;
    gap: clamp(0.5rem, 2vw, 1rem);
}

.flex-responsive-center {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: clamp(0.5rem, 2vw, 1rem);
}

.flex-responsive-between {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: clamp(0.5rem, 2vw, 1rem);
}

/* ===== RESPONSIVE GRID UTILITIES ===== */

.grid-responsive {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: clamp(1rem, 3vw, 2rem);
}

.grid-responsive-small {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: clamp(0.5rem, 2vw, 1rem);
}

.grid-responsive-large {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: clamp(1.5rem, 4vw, 3rem);
}

/* ===== RESPONSIVE BUTTON UTILITIES ===== */

.btn-responsive {
    padding: clamp(0.5rem, 2vw, 0.75rem) clamp(1rem, 3vw, 1.5rem);
    font-size: clamp(0.875rem, 2vw, 1rem);
    min-height: 44px;
    border-radius: clamp(4px, 1vw, 8px);
}

.btn-responsive-large {
    padding: clamp(0.75rem, 3vw, 1rem) clamp(1.5rem, 4vw, 2rem);
    font-size: clamp(1rem, 2.5vw, 1.125rem);
    min-height: 48px;
    border-radius: clamp(6px, 1.5vw, 10px);
}

/* ===== RESPONSIVE CARD UTILITIES ===== */

.card-responsive {
    padding: clamp(1rem, 3vw, 2rem);
    border-radius: clamp(8px, 2vw, 12px);
    margin-bottom: clamp(1rem, 3vw, 1.5rem);
}

.card-responsive-compact {
    padding: clamp(0.75rem, 2vw, 1.5rem);
    border-radius: clamp(6px, 1.5vw, 10px);
    margin-bottom: clamp(0.75rem, 2vw, 1rem);
}

/* ===== RESPONSIVE FORM UTILITIES ===== */

.form-responsive input,
.form-responsive textarea,
.form-responsive select {
    width: 100%;
    padding: clamp(0.75rem, 2vw, 1rem);
    font-size: clamp(0.875rem, 2vw, 1rem);
    border-radius: clamp(4px, 1vw, 8px);
    min-height: 44px;
}

.form-responsive label {
    font-size: clamp(0.875rem, 2vw, 1rem);
    margin-bottom: clamp(0.25rem, 1vw, 0.5rem);
    display: block;
}

/* ===== RESPONSIVE BREAKPOINT UTILITIES ===== */

/* Mobile (up to 767px) */
@media (max-width: 767px) {
    .show-mobile { display: block; }
    .hide-mobile { display: none; }
    
    .flex-responsive,
    .flex-responsive-center,
    .flex-responsive-between {
        flex-direction: column;
        align-items: stretch;
    }
    
    .grid-responsive {
        grid-template-columns: 1fr;
    }
    
    .text-center-mobile {
        text-align: center;
    }
    
    .full-width-mobile {
        width: 100%;
    }
    
    .no-padding-mobile {
        padding: 0;
    }
    
    .compact-spacing-mobile {
        padding: 0.5rem;
        margin: 0.5rem 0;
    }
}

/* Tablet (768px to 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
    .show-tablet { display: block; }
    .hide-tablet { display: none; }
    
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .text-center-tablet {
        text-align: center;
    }
    
    .half-width-tablet {
        width: 50%;
    }
}

/* Desktop (1024px and up) */
@media (min-width: 1024px) {
    .show-desktop { display: block; }
    .hide-desktop { display: none; }
    
    .grid-responsive {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .text-left-desktop {
        text-align: left;
    }
    
    .auto-width-desktop {
        width: auto;
    }
}

/* ===== RESPONSIVE OVERFLOW FIXES ===== */

.overflow-hidden {
    overflow: hidden;
}

.overflow-x-hidden {
    overflow-x: hidden;
}

.overflow-y-auto {
    overflow-y: auto;
}

.overflow-responsive {
    overflow-x: hidden;
    overflow-y: auto;
}

/* ===== RESPONSIVE POSITIONING UTILITIES ===== */

.position-responsive {
    position: relative;
}

@media (max-width: 767px) {
    .position-responsive {
        position: static;
    }
    
    .absolute-to-relative-mobile {
        position: relative !important;
        top: auto !important;
        left: auto !important;
        right: auto !important;
        bottom: auto !important;
    }
}

/* ===== RESPONSIVE ACCESSIBILITY UTILITIES ===== */

.focus-visible-responsive:focus {
    outline: 3px solid var(--color-primary, #4f46e5);
    outline-offset: 2px;
}

.touch-target-responsive {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* ===== RESPONSIVE PERFORMANCE UTILITIES ===== */

.gpu-accelerated-responsive {
    transform: translateZ(0);
    will-change: transform;
}

.contain-layout-responsive {
    contain: layout;
}

.contain-paint-responsive {
    contain: paint;
}

/* ===== RESPONSIVE ANIMATION UTILITIES ===== */

@media (prefers-reduced-motion: reduce) {
    .animation-responsive {
        animation: none !important;
        transition: none !important;
    }
}

.transition-responsive {
    transition: all 0.3s ease;
}

.hover-lift-responsive:hover {
    transform: translateY(-2px);
}

@media (max-width: 767px) {
    .hover-lift-responsive:hover {
        transform: none;
    }
}

/* ===== RESPONSIVE SCROLL UTILITIES ===== */

.scroll-smooth-responsive {
    scroll-behavior: smooth;
}

.scroll-snap-responsive {
    scroll-snap-type: y mandatory;
}

.scroll-snap-item-responsive {
    scroll-snap-align: start;
}

/* ===== RESPONSIVE BORDER UTILITIES ===== */

.border-responsive {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: clamp(4px, 1vw, 8px);
}

.border-radius-responsive {
    border-radius: clamp(4px, 1vw, 12px);
}

/* ===== RESPONSIVE SHADOW UTILITIES ===== */

.shadow-responsive {
    box-shadow: 0 clamp(2px, 1vw, 4px) clamp(8px, 2vw, 12px) rgba(0, 0, 0, 0.1);
}

.shadow-hover-responsive:hover {
    box-shadow: 0 clamp(4px, 2vw, 8px) clamp(16px, 4vw, 25px) rgba(0, 0, 0, 0.15);
}

@media (max-width: 767px) {
    .shadow-hover-responsive:hover {
        box-shadow: 0 clamp(2px, 1vw, 4px) clamp(8px, 2vw, 12px) rgba(0, 0, 0, 0.1);
    }
}
