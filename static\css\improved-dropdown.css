/**
 * Improved Dropdown Styling
 * Enhances dropdown menu behavior and highlighting
 */

/* Improved dropdown toggle */
.navbar-dropdown .navbar-link.dropdown-toggle {
    cursor: pointer !important;
    position: relative !important;
    padding: 4px 8px !important;
    transition: all 0.3s ease !important;
}

/* Improved dropdown menu */
.navbar-dropdown .dropdown-menu {
    display: none !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 200px !important;
    background-color: #ffffff !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    border-radius: 4px !important;
    z-index: 1000 !important;
    padding: 8px 0 !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* Show dropdown on hover */
.navbar-dropdown:hover .dropdown-menu {
    display: block !important;
}

/* Improved dropdown items */
.dropdown-item {
    display: block !important;
    width: 100% !important;
    padding: 10px 16px !important;
    color: #333 !important;
    text-decoration: none !important;
    transition: all 0.2s ease !important;
    white-space: nowrap !important;
    cursor: pointer !important;
}

.dropdown-item:hover {
    background-color: rgba(25, 118, 210, 0.1) !important;
    color: #1976d2 !important;
}

/* Active dropdown toggle */
.navbar-dropdown .navbar-link.dropdown-toggle.active,
.navbar-dropdown .navbar-link.dropdown-toggle:hover {
    color: #1976d2 !important;
    border-bottom: 2px solid #1976d2 !important;
    font-weight: 600 !important;
}

/* Dark theme styles */
.dark-theme .navbar-dropdown .dropdown-menu {
    background-color: #1e293b !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.dark-theme .dropdown-item {
    color: #e2e8f0 !important;
}

.dark-theme .dropdown-item:hover {
    background-color: rgba(100, 181, 246, 0.1) !important;
    color: #64b5f6 !important;
}

.dark-theme .navbar-dropdown .navbar-link.dropdown-toggle.active,
.dark-theme .navbar-dropdown .navbar-link.dropdown-toggle:hover {
    color: #64b5f6 !important;
    border-bottom: 2px solid #64b5f6 !important;
}

/* Ensure Services dropdown is highlighted when a service page is active */
body[data-current-page="chatbot-services"] .navbar-link:contains("Services"),
body[data-current-page="ai-services"] .navbar-link:contains("Services"),
body[data-current-page="ai-agents"] .navbar-link:contains("Services") {
    color: #1976d2 !important;
    border-bottom: 2px solid #1976d2 !important;
    font-weight: 600 !important;
}

.dark-theme body[data-current-page="chatbot-services"] .navbar-link:contains("Services"),
.dark-theme body[data-current-page="ai-services"] .navbar-link:contains("Services"),
.dark-theme body[data-current-page="ai-agents"] .navbar-link:contains("Services") {
    color: #64b5f6 !important;
    border-bottom: 2px solid #64b5f6 !important;
}
