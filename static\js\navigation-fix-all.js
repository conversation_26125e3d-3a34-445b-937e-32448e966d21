/**
 * Complete Navigation Fix for All Links
 * Ensures all navigation links work correctly
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get all navigation links
    const navLinks = document.querySelectorAll('.navbar-link, .dropdown-item');
    
    // Add click event listeners to all navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            
            // Skip if no href or it's a JavaScript function
            if (!href || href.startsWith('javascript:')) {
                return;
            }
            
            // Handle hash navigation (section links on the homepage)
            if (href.startsWith('/#')) {
                e.preventDefault();
                
                const targetId = href.replace('/#', '');
                const targetSection = document.getElementById(targetId);
                
                if (targetSection) {
                    // Scroll to the section
                    const headerOffset = 60;
                    const elementPosition = targetSection.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
                    
                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });
                    
                    // Update URL
                    history.pushState(null, null, href);
                } else {
                    // If section not found, navigate to the homepage
                    window.location.href = '/';
                }
            }
            // Handle direct links to other pages
            else if (href.startsWith('/')) {
                // Let the default navigation happen
                return;
            }
        });
    });
    
    // Special handling for Resources dropdown
    const resourcesDropdown = document.querySelector('.navbar-dropdown');
    if (resourcesDropdown) {
        const dropdownToggle = resourcesDropdown.querySelector('.dropdown-toggle');
        const dropdownMenu = resourcesDropdown.querySelector('.dropdown-menu');
        const faqItem = dropdownMenu.querySelector('.dropdown-item');
        
        // Make sure the dropdown menu is visible on hover
        resourcesDropdown.addEventListener('mouseenter', function() {
            dropdownMenu.style.display = 'block';
        });
        
        resourcesDropdown.addEventListener('mouseleave', function() {
            dropdownMenu.style.display = 'none';
        });
        
        // Make the FAQ item clickable
        if (faqItem) {
            faqItem.addEventListener('click', function(e) {
                e.preventDefault();
                
                const href = this.getAttribute('href');
                if (href && href.startsWith('/#')) {
                    const targetId = href.replace('/#', '');
                    const targetSection = document.getElementById(targetId);
                    
                    if (targetSection) {
                        // Scroll to the section
                        const headerOffset = 60;
                        const elementPosition = targetSection.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
                        
                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });
                        
                        // Update URL
                        history.pushState(null, null, href);
                    } else {
                        // If section not found, navigate to the homepage
                        window.location.href = '/';
                    }
                }
            });
        }
    }
    
    // Handle direct navigation to sections via URL hash
    function handleHashNavigation() {
        const hash = window.location.hash;
        if (hash && hash.startsWith('#')) {
            const targetId = hash.substring(1);
            const targetSection = document.getElementById(targetId);
            
            if (targetSection) {
                setTimeout(function() {
                    // Scroll to the section
                    const headerOffset = 60;
                    const elementPosition = targetSection.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;
                    
                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });
                }, 100);
            }
        }
    }
    
    // Run hash navigation on page load
    handleHashNavigation();
    
    // Also run it after a short delay to ensure all content is loaded
    setTimeout(handleHashNavigation, 500);
});
