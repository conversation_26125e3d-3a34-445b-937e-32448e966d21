/**
 * Flash Message Enhancements
 * Improves the appearance and functionality of flash messages
 */

/* Flash Messages Container */
.flash-messages {
    position: fixed;
    top: calc(70px + var(--spacing-lg)); /* Account for navbar height + spacing */
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 500px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

/* Flash Message Styling */
.flash-message {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-md);
    transition: opacity 0.3s ease;
    animation: fadeIn 0.3s ease-out;
}

/* Message Types */
.flash-message.success {
    background-color: var(--color-success);
    color: white;
}

.flash-message.error {
    background-color: var(--color-error);
    color: white;
}

.flash-message.info {
    background-color: var(--color-info);
    color: white;
}

.flash-message.warning {
    background-color: var(--color-warning);
    color: white;
}

/* Close Button - Increased Size */
.flash-message .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 28px; /* Increased from default */
    cursor: pointer;
    line-height: 1;
    width: 36px; /* Fixed width */
    height: 36px; /* Fixed height */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
    margin-left: 10px;
}

.flash-message .close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Fade out animation */
.flash-message.fade-out {
    opacity: 0;
    transform: translateY(-10px);
}

/* Animation keyframes */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark theme adjustments */
.dark-theme .flash-message {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

/* Make sure the message text has enough space */
.flash-message .message-text {
    flex: 1;
    padding-right: 10px;
}
