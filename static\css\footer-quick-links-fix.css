/**
 * Footer Quick Links Fix
 * Removes spacing between Quick Links items and removes all highlighting effects
 */

/* Remove spacing between Quick Links items */
.footer-section ul {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.footer-section ul li {
    margin-bottom: 0 !important;
    margin-top: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1.2 !important;
}

/* Override the style.css spacing with higher specificity */
footer .footer-section ul li {
    margin-bottom: 0 !important;
    margin-top: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Remove all highlighting and hover effects from footer links */
footer .footer-section a,
footer .footer-section a:hover,
footer .footer-section a:focus,
footer .footer-section a:active,
footer .footer-section a:visited,
.footer-section a,
.footer-section a:hover,
.footer-section a:focus,
.footer-section a:active,
.footer-section a:visited {
    color: var(--color-text-light, #666) !important;
    text-decoration: none !important;
    background: transparent !important;
    background-color: transparent !important;
    border: none !important;
    border-bottom: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    transition: none !important;
    transform: none !important;
    font-weight: normal !important;
    outline: none !important;
}

/* Dark theme footer links */
.dark-theme footer .footer-section a,
.dark-theme footer .footer-section a:hover,
.dark-theme footer .footer-section a:focus,
.dark-theme footer .footer-section a:active,
.dark-theme footer .footer-section a:visited,
.dark-theme .footer-section a,
.dark-theme .footer-section a:hover,
.dark-theme .footer-section a:focus,
.dark-theme .footer-section a:active,
.dark-theme .footer-section a:visited {
    color: var(--color-text-dark, #f8f9fa) !important;
    text-decoration: none !important;
    background: transparent !important;
    background-color: transparent !important;
    border: none !important;
    border-bottom: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
    padding: 0 !important;
    margin: 0 !important;
    transition: none !important;
    transform: none !important;
    font-weight: normal !important;
    outline: none !important;
}

/* Remove any pseudo-elements that might create underlines or highlights */
footer .footer-section a::before,
footer .footer-section a::after,
.footer-section a::before,
.footer-section a::after {
    display: none !important;
    content: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    border: none !important;
}

/* Ensure no active states for footer links */
footer .footer-section a.active,
.footer-section a.active {
    color: var(--color-text-light, #666) !important;
    font-weight: normal !important;
    background: transparent !important;
    border: none !important;
    text-decoration: none !important;
}

.dark-theme footer .footer-section a.active,
.dark-theme .footer-section a.active {
    color: var(--color-text-dark, #f8f9fa) !important;
    font-weight: normal !important;
    background: transparent !important;
    border: none !important;
    text-decoration: none !important;
}

/* Override any navigation highlighting that might affect footer */
footer .footer-section a[data-target],
.footer-section a[data-target] {
    color: var(--color-text-light, #666) !important;
    font-weight: normal !important;
    background: transparent !important;
    border: none !important;
    text-decoration: none !important;
}

.dark-theme footer .footer-section a[data-target],
.dark-theme .footer-section a[data-target] {
    color: var(--color-text-dark, #f8f9fa) !important;
    font-weight: normal !important;
    background: transparent !important;
    border: none !important;
    text-decoration: none !important;
}

/* Force override with high specificity */
html body footer .footer-section ul li a,
html body footer .footer-section ul li a:hover,
html body footer .footer-section ul li a:focus,
html body footer .footer-section ul li a:active,
html body .footer-section ul li a,
html body .footer-section ul li a:hover,
html body .footer-section ul li a:focus,
html body .footer-section ul li a:active {
    color: var(--color-text-light, #666) !important;
    text-decoration: none !important;
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
    font-weight: normal !important;
    transition: none !important;
    transform: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
}

html body .dark-theme footer .footer-section ul li a,
html body .dark-theme footer .footer-section ul li a:hover,
html body .dark-theme footer .footer-section ul li a:focus,
html body .dark-theme footer .footer-section ul li a:active,
html body .dark-theme .footer-section ul li a,
html body .dark-theme .footer-section ul li a:hover,
html body .dark-theme .footer-section ul li a:focus,
html body .dark-theme .footer-section ul li a:active {
    color: var(--color-text-dark, #f8f9fa) !important;
    text-decoration: none !important;
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
    margin: 0 !important;
    font-weight: normal !important;
    transition: none !important;
    transform: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
}

/* Additional overrides to ensure no spacing and no highlighting */
.footer-content .footer-section ul li {
    margin: 0 !important;
    padding: 0 !important;
    margin-bottom: 0 !important;
    margin-top: 0 !important;
}

.footer-content .footer-section ul li a {
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1.2 !important;
    color: inherit !important;
    text-decoration: none !important;
    background: transparent !important;
    border: none !important;
    transition: none !important;
}

.footer-content .footer-section ul li a:hover,
.footer-content .footer-section ul li a:focus,
.footer-content .footer-section ul li a:active {
    color: inherit !important;
    text-decoration: none !important;
    background: transparent !important;
    border: none !important;
    transform: none !important;
    box-shadow: none !important;
}

/* Force remove any CSS variables that might add spacing */
.footer-section ul li {
    margin-bottom: 0 !important;
    --spacing-sm: 0 !important;
}
