/**
 * Remove All Navigation Borders
 * This CSS file completely removes all borders, outlines, and highlighting from navigation elements
 */

/* Target ALL possible navigation elements and states */
.navbar-link,
.navbar-link.active,
.navbar-link:hover,
.navbar-link:focus,
.navbar-link:active,
.navbar-link:visited,
.nav-item,
.nav-item.active,
.nav-item:hover,
.nav-item:focus,
.nav-item:active,
.nav-item:visited,
.dropdown-item,
.dropdown-item.active,
.dropdown-item:hover,
.dropdown-item:focus,
.dropdown-item:active,
.dropdown-item:visited,
a[data-target],
a[data-target].active,
a[data-target]:hover,
a[data-target]:focus,
a[data-target]:active,
a[data-target]:visited,
#services-nav-link,
#services-nav-link.active,
#services-nav-link:hover,
#services-nav-link:focus,
#services-nav-link:active,
#services-nav-link:visited,
#about-nav-link,
#about-nav-link.active,
#about-nav-link:hover,
#about-nav-link:focus,
#about-nav-link:active,
#about-nav-link:visited,
#support-nav-link,
#support-nav-link.active,
#support-nav-link:hover,
#support-nav-link:focus,
#support-nav-link:active,
#support-nav-link:visited,
.navbar-dropdown .navbar-link,
.navbar-dropdown .navbar-link.active,
.navbar-dropdown .navbar-link:hover,
.navbar-dropdown .navbar-link:focus,
.navbar-dropdown .navbar-link:active,
.navbar-dropdown .navbar-link:visited,
.navbar-dropdown .dropdown-toggle,
.navbar-dropdown .dropdown-toggle.active,
.navbar-dropdown .dropdown-toggle:hover,
.navbar-dropdown .dropdown-toggle:focus,
.navbar-dropdown .dropdown-toggle:active,
.navbar-dropdown .dropdown-toggle:visited {
    /* Remove all borders */
    border: none !important;
    border-top: none !important;
    border-right: none !important;
    border-bottom: none !important;
    border-left: none !important;
    border-width: 0 !important;
    border-style: none !important;
    border-color: transparent !important;
    
    /* Remove all outlines */
    outline: none !important;
    outline-width: 0 !important;
    outline-style: none !important;
    outline-color: transparent !important;
    outline-offset: 0 !important;
    
    /* Remove all box shadows */
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    -moz-box-shadow: none !important;
    
    /* Remove all background colors */
    background-color: transparent !important;
    background: transparent !important;
    background-image: none !important;
    
    /* Remove all transforms */
    transform: none !important;
    -webkit-transform: none !important;
    -moz-transform: none !important;
    -o-transform: none !important;
    
    /* Remove all text shadows */
    text-shadow: none !important;
    
    /* Reset font weight */
    font-weight: normal !important;
    
    /* Reset padding and margin */
    padding: 0.5rem 0.75rem !important;
    margin: 0 !important;
}

/* Target ALL pseudo-elements */
.navbar-link::before,
.navbar-link::after,
.navbar-link.active::before,
.navbar-link.active::after,
.navbar-link:hover::before,
.navbar-link:hover::after,
.navbar-link:focus::before,
.navbar-link:focus::after,
.navbar-link:active::before,
.navbar-link:active::after,
.nav-item::before,
.nav-item::after,
.nav-item.active::before,
.nav-item.active::after,
.nav-item:hover::before,
.nav-item:hover::after,
.nav-item:focus::before,
.nav-item:focus::after,
.nav-item:active::before,
.nav-item:active::after,
.dropdown-item::before,
.dropdown-item::after,
.dropdown-item.active::before,
.dropdown-item.active::after,
.dropdown-item:hover::before,
.dropdown-item:hover::after,
.dropdown-item:focus::before,
.dropdown-item:focus::after,
.dropdown-item:active::before,
.dropdown-item:active::after,
a[data-target]::before,
a[data-target]::after,
a[data-target].active::before,
a[data-target].active::after,
a[data-target]:hover::before,
a[data-target]:hover::after,
a[data-target]:focus::before,
a[data-target]:focus::after,
a[data-target]:active::before,
a[data-target]:active::after,
#services-nav-link::before,
#services-nav-link::after,
#services-nav-link.active::before,
#services-nav-link.active::after,
#services-nav-link:hover::before,
#services-nav-link:hover::after,
#services-nav-link:focus::before,
#services-nav-link:focus::after,
#services-nav-link:active::before,
#services-nav-link:active::after,
#about-nav-link::before,
#about-nav-link::after,
#about-nav-link.active::before,
#about-nav-link.active::after,
#about-nav-link:hover::before,
#about-nav-link:hover::after,
#about-nav-link:focus::before,
#about-nav-link:focus::after,
#about-nav-link:active::before,
#about-nav-link:active::after,
#support-nav-link::before,
#support-nav-link::after,
#support-nav-link.active::before,
#support-nav-link.active::after,
#support-nav-link:hover::before,
#support-nav-link:hover::after,
#support-nav-link:focus::before,
#support-nav-link:focus::after,
#support-nav-link:active::before,
#support-nav-link:active::after,
.navbar-dropdown .navbar-link::before,
.navbar-dropdown .navbar-link::after,
.navbar-dropdown .navbar-link.active::before,
.navbar-dropdown .navbar-link.active::after,
.navbar-dropdown .navbar-link:hover::before,
.navbar-dropdown .navbar-link:hover::after,
.navbar-dropdown .navbar-link:focus::before,
.navbar-dropdown .navbar-link:focus::after,
.navbar-dropdown .navbar-link:active::before,
.navbar-dropdown .navbar-link:active::after,
.navbar-dropdown .dropdown-toggle::before,
.navbar-dropdown .dropdown-toggle::after,
.navbar-dropdown .dropdown-toggle.active::before,
.navbar-dropdown .dropdown-toggle.active::after,
.navbar-dropdown .dropdown-toggle:hover::before,
.navbar-dropdown .dropdown-toggle:hover::after,
.navbar-dropdown .dropdown-toggle:focus::before,
.navbar-dropdown .dropdown-toggle:focus::after,
.navbar-dropdown .dropdown-toggle:active::before,
.navbar-dropdown .dropdown-toggle:active::after {
    /* Completely remove pseudo-elements */
    display: none !important;
    content: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    width: 0 !important;
    height: 0 !important;
    border: none !important;
    background: none !important;
    box-shadow: none !important;
}

/* Dark theme overrides */
.dark-theme .navbar-link,
.dark-theme .navbar-link.active,
.dark-theme .navbar-link:hover,
.dark-theme .navbar-link:focus,
.dark-theme .navbar-link:active,
.dark-theme .nav-item,
.dark-theme .nav-item.active,
.dark-theme .nav-item:hover,
.dark-theme .nav-item:focus,
.dark-theme .nav-item:active,
.dark-theme .dropdown-item,
.dark-theme .dropdown-item.active,
.dark-theme .dropdown-item:hover,
.dark-theme .dropdown-item:focus,
.dark-theme .dropdown-item:active,
.dark-theme a[data-target],
.dark-theme a[data-target].active,
.dark-theme a[data-target]:hover,
.dark-theme a[data-target]:focus,
.dark-theme a[data-target]:active,
.dark-theme #services-nav-link,
.dark-theme #services-nav-link.active,
.dark-theme #services-nav-link:hover,
.dark-theme #services-nav-link:focus,
.dark-theme #services-nav-link:active,
.dark-theme #about-nav-link,
.dark-theme #about-nav-link.active,
.dark-theme #about-nav-link:hover,
.dark-theme #about-nav-link:focus,
.dark-theme #about-nav-link:active,
.dark-theme #support-nav-link,
.dark-theme #support-nav-link.active,
.dark-theme #support-nav-link:hover,
.dark-theme #support-nav-link:focus,
.dark-theme #support-nav-link:active,
.dark-theme .navbar-dropdown .navbar-link,
.dark-theme .navbar-dropdown .navbar-link.active,
.dark-theme .navbar-dropdown .navbar-link:hover,
.dark-theme .navbar-dropdown .navbar-link:focus,
.dark-theme .navbar-dropdown .navbar-link:active,
.dark-theme .navbar-dropdown .dropdown-toggle,
.dark-theme .navbar-dropdown .dropdown-toggle.active,
.dark-theme .navbar-dropdown .dropdown-toggle:hover,
.dark-theme .navbar-dropdown .dropdown-toggle:focus,
.dark-theme .navbar-dropdown .dropdown-toggle:active {
    /* Remove all borders */
    border: none !important;
    border-bottom: none !important;
    
    /* Remove all outlines */
    outline: none !important;
    
    /* Remove all box shadows */
    box-shadow: none !important;
    
    /* Remove all background colors */
    background-color: transparent !important;
    background: transparent !important;
    
    /* Remove all transforms */
    transform: none !important;
    
    /* Remove all text shadows */
    text-shadow: none !important;
    
    /* Reset font weight */
    font-weight: normal !important;
}
