/**
 * Uniform Button Sizing
 * Makes all CTA buttons the same size across the site
 */

/* Hero Section Button Standardization */
.hero-cta {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.hero-cta .btn,
.enhanced-cta-btn,
.cta-btn,
.cta-button {
    /* Uniform sizing */
    min-width: 280px;
    max-width: 320px;
    width: 100%;
    height: 56px;
    
    /* Consistent padding and spacing */
    padding: 14px 20px !important;
    
    /* Typography */
    font-size: 1rem !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    text-align: center !important;
    
    /* Layout */
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    
    /* Styling */
    border-radius: 8px !important;
    text-decoration: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    
    /* Text handling */
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* Primary Button Styling */
.hero-cta .btn-primary,
.enhanced-cta-btn-primary,
.cta-btn.primary,
.cta-button.primary {
    background: linear-gradient(135deg, #4f46e5, #3730a3) !important;
    color: white !important;
    border: none !important;
    box-shadow: 0 4px 14px rgba(79, 70, 229, 0.4) !important;
}

.hero-cta .btn-primary:hover,
.enhanced-cta-btn-primary:hover,
.cta-btn.primary:hover,
.cta-button.primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.5) !important;
}

/* Secondary Button Styling */
.hero-cta .btn-secondary,
.enhanced-cta-btn-secondary,
.cta-btn.secondary,
.cta-button.secondary {
    background: transparent !important;
    color: #4f46e5 !important;
    border: 2px solid #4f46e5 !important;
    box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1) !important;
}

.hero-cta .btn-secondary:hover,
.enhanced-cta-btn-secondary:hover,
.cta-btn.secondary:hover,
.cta-button.secondary:hover {
    background: #4f46e5 !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(79, 70, 229, 0.3) !important;
}

/* Dark Theme Support */
.dark-theme .hero-cta .btn-secondary,
.dark-theme .enhanced-cta-btn-secondary,
.dark-theme .cta-btn.secondary,
.dark-theme .cta-button.secondary {
    color: #818cf8 !important;
    border-color: #818cf8 !important;
}

.dark-theme .hero-cta .btn-secondary:hover,
.dark-theme .enhanced-cta-btn-secondary:hover,
.dark-theme .cta-btn.secondary:hover,
.dark-theme .cta-button.secondary:hover {
    background: #818cf8 !important;
    color: #1a202c !important;
}

/* Experience AI Section Button */
.experience-ai-section .cta-button,
.experience-ai-section button {
    min-width: 280px !important;
    max-width: 320px !important;
    width: 100% !important;
    height: 56px !important;
    padding: 14px 20px !important;
    font-size: 1rem !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    background: linear-gradient(135deg, #7c3aed, #5b21b6) !important;
    color: white !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 14px rgba(124, 58, 237, 0.4) !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    text-decoration: none !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

.experience-ai-section .cta-button:hover,
.experience-ai-section button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(124, 58, 237, 0.5) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-cta {
        flex-direction: column;
        align-items: center;
        gap: 0.75rem;
    }
    
    .hero-cta .btn,
    .enhanced-cta-btn,
    .cta-btn,
    .cta-button,
    .experience-ai-section .cta-button,
    .experience-ai-section button {
        min-width: 260px !important;
        max-width: 300px !important;
        width: 90% !important;
        font-size: 0.95rem !important;
    }
}

@media (max-width: 480px) {
    .hero-cta .btn,
    .enhanced-cta-btn,
    .cta-btn,
    .cta-button,
    .experience-ai-section .cta-button,
    .experience-ai-section button {
        min-width: 240px !important;
        max-width: 280px !important;
        width: 85% !important;
        font-size: 0.9rem !important;
        padding: 12px 16px !important;
        height: 52px !important;
    }
}

/* Enhanced CTA Section Specific Fixes */
.enhanced-cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.enhanced-cta-buttons .enhanced-cta-btn {
    flex: 0 0 auto;
}

/* Remove any conflicting styles */
.hero-cta .btn i,
.enhanced-cta-btn i,
.cta-btn i,
.cta-button i {
    margin-left: 8px;
    font-size: 0.9em;
}

/* Ensure consistent appearance across all sections */
.section-spacing .cta-button,
.section-spacing button[onclick*="Modal"] {
    min-width: 280px !important;
    max-width: 320px !important;
    height: 56px !important;
}
