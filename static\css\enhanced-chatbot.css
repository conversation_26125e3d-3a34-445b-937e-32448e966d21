/**
 * Enhanced <PERSON><PERSON><PERSON> <PERSON>yling
 * Improved UI/UX for the chatbot in both light and dark modes
 */

/* Chatbot Widget Container */
.chatbot-widget {
  position: fixed;
  bottom: 40px;
  right: 20px;
  width: 350px;
  height: 500px;
  background-color: var(--color-card-bg);
  border-radius: 12px;
  box-shadow: var(--shadow-xl);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1000;
  opacity: 0;
  transform: translateY(20px);
  pointer-events: none;
  transition: all 0.3s ease;
  border: 1px solid var(--color-border-light);
}

.chatbot-widget.active {
  opacity: 1;
  transform: translateY(0);
  pointer-events: all;
}

/* Chatbot Header */
.chatbot-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background-color: var(--color-bg-secondary);
  border-bottom: 1px solid var(--color-border-light);
}

.chatbot-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chatbot-logo img {
  width: 24px;
  height: 24px;
}

.chatbot-title span {
  font-weight: 600;
  color: var(--color-text-primary);
  font-size: 14px;
}

.chatbot-controls {
  display: flex;
  gap: 4px;
}

.chatbot-controls button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none;
  background-color: transparent;
  color: var(--color-text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.chatbot-controls button:hover {
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
}

/* Chatbot Body */
.chatbot-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chatbot-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  scroll-behavior: smooth;
}

/* Scrollbar Styling */
.chatbot-messages::-webkit-scrollbar {
  width: 6px;
}

.chatbot-messages::-webkit-scrollbar-track {
  background: var(--color-bg-secondary);
  border-radius: 3px;
}

.chatbot-messages::-webkit-scrollbar-thumb {
  background-color: var(--color-border-medium);
  border-radius: 3px;
}

.chatbot-messages::-webkit-scrollbar-thumb:hover {
  background-color: var(--color-border-dark);
}

/* Message Styling */
.message {
  display: flex;
  max-width: 85%;
  animation: messageAppear 0.3s ease forwards;
}

.user-message {
  align-self: flex-end;
}

.bot-message {
  align-self: flex-start;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--color-bg-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  color: var(--color-text-accent);
}

.message-content {
  padding: 10px 14px;
  border-radius: 18px;
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
  font-size: 14px;
  line-height: 1.5;
  box-shadow: var(--shadow-sm);
}

.user-message .message-content {
  border-bottom-right-radius: 4px;
  background-color: var(--color-text-accent);
  color: white;
}

.bot-message .message-content {
  border-bottom-left-radius: 4px;
}

/* Typing Indicator */
.typing-indicator-container {
  display: none;
  align-self: flex-start;
  margin-top: 8px;
  margin-bottom: 8px;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background-color: var(--color-bg-tertiary);
  border-radius: 18px;
  border-bottom-left-radius: 4px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: var(--color-text-tertiary);
  animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingBounce {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

/* Chatbot Input Area */
.chatbot-input {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid var(--color-border-light);
  background-color: var(--color-bg-secondary);
}

.chatbot-input-field {
  flex: 1;
  padding: 10px 14px;
  border-radius: 20px;
  border: 1px solid var(--color-border-medium);
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
}

.chatbot-input-field:focus {
  border-color: var(--color-text-accent);
  box-shadow: var(--shadow-outline);
}

.chatbot-send-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background-color: var(--color-text-accent);
  color: white;
  margin-left: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.chatbot-send-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.chatbot-send-btn:active {
  transform: translateY(0);
}

/* Chatbot Buttons */
.chatbot-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
}

.chatbot-button {
  padding: 8px 12px;
  border-radius: 16px;
  border: 1px solid var(--color-text-accent);
  background-color: transparent;
  color: var(--color-text-accent);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chatbot-button:hover {
  background-color: var(--color-text-accent);
  color: white;
}

/* Chatbot Launcher */
.chatbot-launcher {
  position: fixed;
  bottom: 40px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: var(--shadow-lg);
  z-index: 999;
  transition: all 0.3s ease;
}

.chatbot-launcher:hover {
  transform: scale(1.05) translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.chatbot-launcher.hidden {
  display: none;
}

/* Animations */
@keyframes messageAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-appear {
  animation: messageAppear 0.3s ease forwards;
}

/* Responsive Adjustments */
@media (max-width: 480px) {
  .chatbot-widget {
    width: calc(100% - 40px);
    height: 60vh;
    bottom: 30px;
    right: 10px;
    left: 10px;
  }

  .chatbot-launcher {
    width: 50px;
    height: 50px;
    bottom: 30px;
    right: 10px;
  }
}
