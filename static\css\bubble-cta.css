/* Bubble CTA Section Styles */
.bubble-cta-section {
    background: linear-gradient(135deg, #f0f7ff 0%, #e6f0ff 100%);
    padding: 60px 0;
    margin: 40px 0;
    position: relative;
    overflow: hidden;
    border-radius: 16px;
}

.dark-theme .bubble-cta-section {
    background: linear-gradient(135deg, #1a365d 0%, #2a4365 100%);
}

.bubble-cta-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    position: relative;
}

/* Decorative bubbles */
.bubble-cta-section::before,
.bubble-cta-section::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    z-index: 0;
}

.bubble-cta-section::before {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0) 70%);
    top: -100px;
    right: -50px;
}

.bubble-cta-section::after {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.08) 0%, rgba(59, 130, 246, 0) 70%);
    bottom: -80px;
    left: -30px;
}

.bubble-cta-content {
    text-align: center;
    max-width: 900px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.bubble-cta-title {
    font-size: 2.4rem;
    font-weight: 700;
    margin-bottom: 40px;
    color: #1a365d;
    line-height: 1.3;
    position: relative;
    display: inline-block;
}

.bubble-cta-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #60a5fa);
    border-radius: 2px;
}

.dark-theme .bubble-cta-title {
    color: #f0f7ff;
}

.dark-theme .bubble-cta-title::after {
    background: linear-gradient(90deg, #60a5fa, #93c5fd);
}

.bubble-features {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
    margin-bottom: 40px;
}

.bubble-feature {
    flex: 0 0 calc(50% - 25px);
    max-width: calc(50% - 25px);
    display: flex;
    align-items: center;
    text-align: left;
    padding: 20px;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    box-shadow: 0 10px 25px rgba(59, 130, 246, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.bubble-feature:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.15);
}

.dark-theme .bubble-feature {
    background-color: rgba(30, 58, 138, 0.4);
    box-shadow: 0 10px 25px rgba(30, 58, 138, 0.3);
}

.dark-theme .bubble-feature:hover {
    box-shadow: 0 15px 30px rgba(30, 58, 138, 0.4);
}

.bubble-feature::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0) 100%);
    z-index: 0;
}

.feature-icon-wrapper {
    width: 60px !important;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%) !important;
    border-radius: 50% !important;
    margin-right: 20px !important;
    flex-shrink: 0 !important;
    position: relative !important;
    z-index: 1 !important;
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.3) !important;
}

.dark-theme .feature-icon-wrapper {
    background: linear-gradient(135deg, #60a5fa 0%, #93c5fd 100%) !important;
    box-shadow: 0 5px 15px rgba(96, 165, 250, 0.3) !important;
}

.feature-icon {
    width: 30px;
    height: 30px;
    display: block;
    color: white;
}

.feature-text {
    font-size: 1.1rem;
    color: #334155;
    line-height: 1.5;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

.dark-theme .feature-text {
    color: #e2e8f0;
}

.bubble-cta-buttons {
    display: flex;
    justify-content: center;
    gap: 25px;
    margin-top: 30px;
}

.bubble-cta-btn {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    border-radius: 30px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.bubble-cta-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.bubble-cta-btn:hover::before {
    transform: translateX(0);
}

.bubble-cta-btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
    color: white;
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.3);
}

.bubble-cta-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(59, 130, 246, 0.4);
}

.bubble-cta-btn-secondary {
    background-color: rgba(255, 255, 255, 0.9);
    color: #3b82f6;
    border: 2px solid #3b82f6;
    box-shadow: 0 5px 15px rgba(59, 130, 246, 0.1);
}

.bubble-cta-btn-secondary:hover {
    background-color: rgba(59, 130, 246, 0.05);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(59, 130, 246, 0.15);
}

.dark-theme .bubble-cta-btn-primary {
    background: linear-gradient(135deg, #60a5fa 0%, #93c5fd 100%);
    box-shadow: 0 10px 20px rgba(96, 165, 250, 0.3);
}

.dark-theme .bubble-cta-btn-primary:hover {
    box-shadow: 0 15px 30px rgba(96, 165, 250, 0.4);
}

.dark-theme .bubble-cta-btn-secondary {
    background-color: rgba(30, 58, 138, 0.4);
    color: #93c5fd;
    border-color: #93c5fd;
}

.dark-theme .bubble-cta-btn-secondary:hover {
    background-color: rgba(96, 165, 250, 0.1);
}

.btn-text {
    position: relative;
    z-index: 1;
}

.btn-icon {
    margin-left: 10px;
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.bubble-cta-btn:hover .btn-icon {
    transform: translateX(5px);
}

/* Floating animation for decorative elements */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.floating-bubble {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0) 70%);
    z-index: 0;
    animation: float 6s ease-in-out infinite;
}

.bubble-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.bubble-2 {
    width: 60px;
    height: 60px;
    top: 60%;
    right: 15%;
    animation-delay: 1s;
}

.bubble-3 {
    width: 40px;
    height: 40px;
    bottom: 20%;
    left: 20%;
    animation-delay: 2s;
}

@media (max-width: 992px) {
    .bubble-feature {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .bubble-cta-title {
        font-size: 2rem;
    }

    .bubble-cta-buttons {
        flex-direction: column;
    }

    .bubble-cta-btn {
        width: 100%;
        justify-content: center;
    }
}
