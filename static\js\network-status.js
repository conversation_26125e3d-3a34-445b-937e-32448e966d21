// Network status monitoring
document.addEventListener('DOMContentLoaded', function() {
    // Function to check if we're online and update UI accordingly
    function updateOnlineStatus() {
        const statusElement = document.createElement('div');
        statusElement.className = 'network-status';
        statusElement.style.position = 'fixed';
        statusElement.style.bottom = '10px';
        statusElement.style.left = '10px';
        statusElement.style.padding = '8px 12px';
        statusElement.style.borderRadius = '4px';
        statusElement.style.fontSize = '14px';
        statusElement.style.zIndex = '9999';
        statusElement.style.transition = 'opacity 0.5s ease-in-out';
        
        if (navigator.onLine) {
            statusElement.textContent = 'Back online';
            statusElement.style.backgroundColor = '#4caf50';
            statusElement.style.color = 'white';
            
            // Remove any existing offline message
            const existingStatus = document.querySelector('.network-status');
            if (existingStatus) {
                existingStatus.remove();
            }
            
            // Show online message briefly then fade out
            document.body.appendChild(statusElement);
            setTimeout(function() {
                statusElement.style.opacity = '0';
                setTimeout(function() {
                    if (statusElement.parentNode) {
                        statusElement.parentNode.removeChild(statusElement);
                    }
                }, 500);
            }, 3000);
            
            // Reload resources that might have failed
            if (window.wasOffline) {
                window.wasOffline = false;
                // Reload non-critical resources
                const stylesheets = document.querySelectorAll('link[rel="stylesheet"]');
                stylesheets.forEach(function(link) {
                    const href = link.getAttribute('href');
                    if (href && !href.includes('font-awesome')) {
                        const newLink = document.createElement('link');
                        newLink.rel = 'stylesheet';
                        newLink.href = href + '?reload=' + new Date().getTime();
                        link.parentNode.replaceChild(newLink, link);
                    }
                });
            }
        } else {
            window.wasOffline = true;
            statusElement.textContent = 'You are offline. Some features may be limited.';
            statusElement.style.backgroundColor = '#f44336';
            statusElement.style.color = 'white';
            
            // Remove any existing status message
            const existingStatus = document.querySelector('.network-status');
            if (existingStatus) {
                existingStatus.remove();
            }
            
            // Show offline message
            document.body.appendChild(statusElement);
        }
    }

    // Listen for online/offline events
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);
    
    // Initial check
    updateOnlineStatus();
});
