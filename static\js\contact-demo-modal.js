/**
 * Contact Demo Modal JavaScript
 * Handles the contact form modal for "Contact Us for Demo" button
 */

(function() {
    'use strict';

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initContactDemoModal);
    } else {
        initContactDemoModal();
    }

    function initContactDemoModal() {
        createContactDemoModal();
        setupContactDemoHandlers();
    }

    function createContactDemoModal() {
        // Check if modal already exists
        if (document.getElementById('contactDemoModal')) {
            return;
        }

        const modalHTML = `
            <div id="contactDemoModal" class="contact-demo-modal">
                <div class="contact-demo-modal-content">
                    <button class="contact-demo-close" onclick="closeContactDemoModal()">&times;</button>
                    
                    <div class="contact-demo-success" id="contactDemoSuccess">
                        <i class="fas fa-check-circle"></i>
                        Thank you for your interest! We'll contact you within 24 hours to schedule your personalized demo.
                    </div>
                    
                    <h2>Contact Us for Demo</h2>
                    
                    <form id="contactDemoForm" class="contact-demo-form">
                        <div class="contact-demo-form-group">
                            <label for="contactDemoName">Full Name *</label>
                            <input type="text" id="contactDemoName" name="name" required>
                            <div class="contact-demo-error">Please enter your full name</div>
                        </div>
                        
                        <div class="contact-demo-form-group">
                            <label for="contactDemoEmail">Email Address *</label>
                            <input type="email" id="contactDemoEmail" name="email" required>
                            <div class="contact-demo-error">Please enter a valid email address</div>
                        </div>
                        
                        <div class="contact-demo-form-group">
                            <label for="contactDemoCompany">Company Name *</label>
                            <input type="text" id="contactDemoCompany" name="company" required>
                            <div class="contact-demo-error">Please enter your company name</div>
                        </div>
                        
                        <div class="contact-demo-form-group">
                            <label for="contactDemoPhone">Phone Number</label>
                            <input type="tel" id="contactDemoPhone" name="phone">
                        </div>
                        
                        <div class="contact-demo-form-group">
                            <label for="contactDemoIndustry">Industry</label>
                            <select id="contactDemoIndustry" name="industry">
                                <option value="">Select your industry</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="finance">Finance & Banking</option>
                                <option value="retail">Retail & E-commerce</option>
                                <option value="technology">Technology</option>
                                <option value="education">Education</option>
                                <option value="real-estate">Real Estate</option>
                                <option value="legal">Legal Services</option>
                                <option value="manufacturing">Manufacturing</option>
                                <option value="hospitality">Hospitality</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        
                        <div class="contact-demo-form-group">
                            <label for="contactDemoEmployees">Company Size</label>
                            <select id="contactDemoEmployees" name="employees">
                                <option value="">Select company size</option>
                                <option value="1-10">1-10 employees</option>
                                <option value="11-50">11-50 employees</option>
                                <option value="51-200">51-200 employees</option>
                                <option value="201-1000">201-1000 employees</option>
                                <option value="1000+">1000+ employees</option>
                            </select>
                        </div>
                        
                        <div class="contact-demo-form-group">
                            <label for="contactDemoMessage">Tell us about your needs</label>
                            <textarea id="contactDemoMessage" name="message" placeholder="What specific challenges are you looking to solve with AI chatbots?"></textarea>
                        </div>
                        
                        <button type="submit" class="contact-demo-submit-btn">
                            Request Demo
                        </button>
                    </form>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    function setupContactDemoHandlers() {
        // Handle form submission
        const form = document.getElementById('contactDemoForm');
        if (form) {
            form.addEventListener('submit', handleContactDemoSubmit);
        }

        // Handle modal close on background click
        const modal = document.getElementById('contactDemoModal');
        if (modal) {
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeContactDemoModal();
                }
            });
        }

        // Handle escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modal = document.getElementById('contactDemoModal');
                if (modal && modal.classList.contains('active')) {
                    closeContactDemoModal();
                }
            }
        });
    }

    function handleContactDemoSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        // Validate form
        if (!validateContactDemoForm(form)) {
            return;
        }
        
        // Show loading state
        const submitBtn = form.querySelector('.contact-demo-submit-btn');
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;
        
        // Simulate form submission (replace with actual API call)
        setTimeout(() => {
            // Hide loading state
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
            
            // Show success message
            showContactDemoSuccess();
            
            // Store submission data (for demo purposes)
            storeContactDemoSubmission(formData);
            
            // Reset form
            form.reset();
            clearContactDemoErrors();
            
        }, 2000);
    }

    function validateContactDemoForm(form) {
        let isValid = true;
        
        // Clear previous errors
        clearContactDemoErrors();
        
        // Validate required fields
        const requiredFields = ['contactDemoName', 'contactDemoEmail', 'contactDemoCompany'];
        
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            const value = field.value.trim();
            
            if (!value) {
                showContactDemoError(fieldId, 'This field is required');
                isValid = false;
            } else if (fieldId === 'contactDemoEmail' && !isValidEmail(value)) {
                showContactDemoError(fieldId, 'Please enter a valid email address');
                isValid = false;
            } else if (fieldId === 'contactDemoName' && value.length < 2) {
                showContactDemoError(fieldId, 'Name must be at least 2 characters');
                isValid = false;
            } else if (fieldId === 'contactDemoCompany' && value.length < 2) {
                showContactDemoError(fieldId, 'Company name must be at least 2 characters');
                isValid = false;
            }
        });
        
        return isValid;
    }

    function showContactDemoError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const formGroup = field.closest('.contact-demo-form-group');
        const errorElement = formGroup.querySelector('.contact-demo-error');
        
        formGroup.classList.add('error');
        errorElement.textContent = message;
    }

    function clearContactDemoErrors() {
        const errorGroups = document.querySelectorAll('.contact-demo-form-group.error');
        errorGroups.forEach(group => {
            group.classList.remove('error');
        });
    }

    function showContactDemoSuccess() {
        const successElement = document.getElementById('contactDemoSuccess');
        const form = document.getElementById('contactDemoForm');
        
        successElement.classList.add('show');
        form.style.display = 'none';
        
        // Hide success message and show form again after 5 seconds
        setTimeout(() => {
            successElement.classList.remove('show');
            form.style.display = 'flex';
            closeContactDemoModal();
        }, 5000);
    }

    function storeContactDemoSubmission(formData) {
        const submission = {
            type: 'contact-demo',
            timestamp: new Date().toISOString(),
            data: Object.fromEntries(formData)
        };
        
        // Store in localStorage for demo purposes
        const submissions = JSON.parse(localStorage.getItem('ziantrix_contact_submissions') || '[]');
        submissions.push(submission);
        localStorage.setItem('ziantrix_contact_submissions', JSON.stringify(submissions));
        
        console.log('Contact demo submission stored:', submission);
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Global functions for modal control
    window.openContactDemoModal = function() {
        const modal = document.getElementById('contactDemoModal');
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // Focus on first input
            setTimeout(() => {
                const firstInput = modal.querySelector('input');
                if (firstInput) {
                    firstInput.focus();
                }
            }, 300);
        }
    };

    window.closeContactDemoModal = function() {
        const modal = document.getElementById('contactDemoModal');
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = '';
            
            // Reset form and clear errors
            const form = document.getElementById('contactDemoForm');
            if (form) {
                form.reset();
                clearContactDemoErrors();
                form.style.display = 'flex';
            }
            
            // Hide success message
            const successElement = document.getElementById('contactDemoSuccess');
            if (successElement) {
                successElement.classList.remove('show');
            }
        }
    };

})();
