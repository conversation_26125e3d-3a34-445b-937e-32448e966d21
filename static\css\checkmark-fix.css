/**
 * Checkmark Fix
 * This file ensures consistent styling for all checkmarks in solution cards
 * and prevents duplicate checkmarks from appearing
 */

/* Ensure consistent styling for checkmarks */
.check-icon {
  color: var(--color-primary);
  font-weight: bold;
  margin-right: 0.75rem;
  flex-shrink: 0;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background-color: rgba(25, 118, 210, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

/* Ensure no duplicate checkmarks from pseudo-elements */
.solution-benefits li::before {
  content: none !important;
}

/* Fix for product features if they exist */
.product-features li {
  position: relative;
  padding-left: 1.5rem;
}

/* Ensure consistent styling for benefit text */
.benefit-text {
  font-size: 0.95rem;
  line-height: 1.5;
  color: var(--color-text);
  transition: color 0.3s ease;
}

/* Ensure consistent styling for solution-benefits list items */
.solution-benefits li {
  position: relative;
  padding: 0.75rem 0;
  margin-bottom: 0.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: flex-start;
  transition: all 0.3s ease;
}

.solution-benefits li:first-child {
  border-top: none;
}

/* Dark mode styles */
.dark-theme .check-icon {
  color: var(--color-primary-light);
  background-color: rgba(100, 181, 246, 0.15);
}

.dark-theme .solution-card:hover .check-icon {
  background-color: var(--color-primary-light);
  color: #121212;
  box-shadow: 0 0 10px rgba(100, 181, 246, 0.3);
}

.dark-theme .benefit-text {
  color: rgba(255, 255, 255, 0.8);
}

.dark-theme .solution-benefits li {
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

/* Hover effects */
.solution-card:hover .check-icon {
  background-color: var(--color-primary);
  color: white;
  transform: scale(1.1);
}

.solution-card:hover .benefit-text {
  color: var(--color-text);
  font-weight: 500;
}

.dark-theme .solution-card:hover .benefit-text {
  color: rgba(255, 255, 255, 0.95);
}
