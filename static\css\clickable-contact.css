/**
 * Clickable Contact Styling
 * Adds hover effects and cursor styling for clickable contact elements
 */

/* Clickable contact method styling */
.clickable-contact {
    cursor: pointer !important;
    transition: all 0.3s ease;
}

.clickable-contact:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
}

.dark-theme .clickable-contact:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
}

/* Contact icon hover effects */
.clickable-contact .contact-icon {
    transition: all 0.3s ease;
}

.clickable-contact:hover .contact-icon {
    color: var(--color-primary);
    transform: scale(1.1);
}

.dark-theme .clickable-contact:hover .contact-icon {
    color: var(--color-primary-light);
}

/* Contact details hover effects */
.clickable-contact:hover .contact-details h3 {
    color: var(--color-primary);
}

.dark-theme .clickable-contact:hover .contact-details h3 {
    color: var(--color-primary-light);
}

/* LinkedIn specific styling */
.clickable-contact[data-contact-type="linkedin"] .contact-icon {
    color: #0077b5; /* LinkedIn blue */
}

.dark-theme .clickable-contact[data-contact-type="linkedin"] .contact-icon {
    color: #0077b5;
}

.clickable-contact[data-contact-type="linkedin"]:hover .contact-icon {
    color: #005885; /* Darker LinkedIn blue on hover */
}

.dark-theme .clickable-contact[data-contact-type="linkedin"]:hover .contact-icon {
    color: #0099d4; /* Lighter LinkedIn blue on hover in dark mode */
}

/* Email specific styling */
.clickable-contact[data-contact-type="email"]:hover .contact-icon {
    color: #ea4335; /* Gmail red */
}

.dark-theme .clickable-contact[data-contact-type="email"]:hover .contact-icon {
    color: #ff6b6b; /* Lighter red in dark mode */
}

/* Phone specific styling */
.clickable-contact[data-contact-type="phone"]:hover .contact-icon {
    color: #34a853; /* Green for phone */
}

.dark-theme .clickable-contact[data-contact-type="phone"]:hover .contact-icon {
    color: #4caf50; /* Lighter green in dark mode */
}

/* Add subtle animation for better UX */
.clickable-contact:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

/* Ensure proper spacing and alignment */
.contact-info .clickable-contact {
    margin-bottom: 1rem;
}

.contact-info .clickable-contact:last-child {
    margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .clickable-contact:hover {
        transform: none; /* Disable transform on mobile for better touch experience */
    }
    
    .clickable-contact:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }
}

/* Focus styles for accessibility */
.clickable-contact:focus {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

.clickable-contact:focus-visible {
    outline: 2px solid var(--color-primary);
    outline-offset: 2px;
}

/* Ensure contact methods are keyboard accessible */
.clickable-contact {
    -webkit-tap-highlight-color: transparent;
}

/* Additional styling for contact info items in static version */
.contact-info-item.clickable-contact {
    cursor: pointer !important;
    transition: all 0.3s ease;
}

.contact-info-item.clickable-contact:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15) !important;
}

.dark-theme .contact-info-item.clickable-contact:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3) !important;
}
