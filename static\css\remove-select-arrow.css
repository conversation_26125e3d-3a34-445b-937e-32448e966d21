/**
 * Remove Default Select Arrow
 * Removes the default browser dropdown arrow from select elements
 * But allows custom select arrows to be displayed
 */

/* Remove default browser arrow */
select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
    background-image: none !important;
}

/* For IE10+ */
select::-ms-expand {
    display: none !important;
}

/* Make sure our custom select arrow is visible */
.select-arrow {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: none !important;
    position: absolute !important;
    right: 1rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: var(--color-text-secondary) !important;
    z-index: 20 !important;
}

.dark-theme .select-arrow {
    color: rgba(255, 255, 255, 0.6) !important;
}
