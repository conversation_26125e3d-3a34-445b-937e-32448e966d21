/**
 * Additional Search Box Fix
 * Ensures proper alignment in all browsers
 */

/* Force consistent box model */
.search-wrapper, .faq-search-wrapper,
#faqSearch, .faq-search,
.search-button, .clear-button, .search-clear {
    box-sizing: border-box !important;
}

/* Ensure search button has consistent height */
.search-button {
    margin: 0 !important;
    height: 36px !important;
    line-height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Ensure search input has proper padding */
#faqSearch, .faq-search {
    padding-right: 120px !important;
}

/* Fix for Firefox */
@-moz-document url-prefix() {
    .search-button {
        height: 36px !important;
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
}

/* Fix for Safari */
@media not all and (min-resolution:.001dpcm) {
    @supports (-webkit-appearance:none) {
        .search-button {
            height: 36px !important;
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }
    }
}
