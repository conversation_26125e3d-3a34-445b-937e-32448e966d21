/**
 * CRIT<PERSON>AL FIXES - FORCE EXACT BEHAVIOR
 * This file overrides everything to fix the exact issues
 */

/* ===== USER DROPDOWN FIX - NO FLICKERING ===== */
.user-menu {
    position: relative !important;
    display: inline-block !important;
}

.user-menu .user-dropdown {
    position: absolute !important;
    top: calc(100% + 2px) !important;
    right: 0 !important;
    width: 140px !important;
    background: #ffffff !important;
    border: 1px solid #e2e8f0 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    z-index: 9999 !important;
    display: none !important;
    padding: 6px 0 !important;
    margin: 0 !important;
    overflow: hidden !important;
}

.dark-theme .user-menu .user-dropdown {
    background: #2d3748 !important;
    border-color: #4a5568 !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
}

.user-menu .user-dropdown a {
    display: block !important;
    padding: 8px 16px !important;
    color: #2d3748 !important;
    text-decoration: none !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    white-space: nowrap !important;
    border: none !important;
    background: none !important;
    transition: background-color 0.15s ease !important;
}

.dark-theme .user-menu .user-dropdown a {
    color: #e2e8f0 !important;
}

.user-menu .user-dropdown a:hover {
    background: #f7fafc !important;
    color: #2d3748 !important;
}

.dark-theme .user-menu .user-dropdown a:hover {
    background: #4a5568 !important;
    color: #e2e8f0 !important;
}

/* SIMPLE HOVER - NO JAVASCRIPT INTERFERENCE */
.user-menu:hover .user-dropdown {
    display: block !important;
}

.user-dropdown:hover {
    display: block !important;
}

/* ===== BUTTON HEIGHT FIX - EXACT SAME HEIGHT ===== */
.hero-cta a, .cta-buttons a, .hero-section a, .btn, .cta-btn {
    height: 56px !important;
    min-height: 56px !important;
    max-height: 56px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0 20px !important;
    box-sizing: border-box !important;
    font-size: 15px !important;
    font-weight: 600 !important;
    line-height: 1.2 !important;
    text-align: center !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}

/* Primary button - Blue */
.hero-cta .btn-primary, .cta-buttons .btn-primary, .btn.btn-primary, .cta-btn.primary {
    background: #3b82f6 !important;
    background-color: #3b82f6 !important;
    background-image: none !important;
    color: #ffffff !important;
}

/* Secondary button - Purple */
.hero-cta .btn-secondary, .cta-buttons .btn-secondary, .btn.btn-secondary, .cta-btn.secondary {
    background: #8b5cf6 !important;
    background-color: #8b5cf6 !important;
    background-image: none !important;
    color: #ffffff !important;
}

/* Hover effects */
.hero-cta .btn-primary:hover, .cta-buttons .btn-primary:hover, .btn.btn-primary:hover, .cta-btn.primary:hover {
    background: #2563eb !important;
    background-color: #2563eb !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3) !important;
}

.hero-cta .btn-secondary:hover, .cta-buttons .btn-secondary:hover, .btn.btn-secondary:hover, .cta-btn.secondary:hover {
    background: #7c3aed !important;
    background-color: #7c3aed !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3) !important;
}

/* Container alignment */
.hero-cta, .cta-buttons {
    display: flex !important;
    justify-content: center !important;
    align-items: stretch !important;
    gap: 16px !important;
    flex-wrap: nowrap !important;
    max-width: 800px !important;
    margin: 0 auto !important;
}

/* ===== FORM IMPROVEMENTS ===== */
.form-group {
    margin-bottom: 16px !important;
}

.form-group label {
    display: block !important;
    margin-bottom: 6px !important;
    font-weight: 500 !important;
    font-size: 14px !important;
    color: #374151 !important;
}

.form-group input, .form-group textarea, .form-group select {
    width: 100% !important;
    padding: 10px 12px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    background: #ffffff !important;
    color: #374151 !important;
    box-sizing: border-box !important;
    transition: border-color 0.2s ease, box-shadow 0.2s ease !important;
}

.form-group input:focus, .form-group textarea:focus, .form-group select:focus {
    outline: none !important;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.form-group textarea {
    resize: vertical !important;
    min-height: 80px !important;
}

.form-group select {
    appearance: none !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 8px center !important;
    background-repeat: no-repeat !important;
    background-size: 16px !important;
    padding-right: 32px !important;
}

/* Dark theme form support */
.dark-theme .form-group label {
    color: #e5e7eb !important;
}

.dark-theme .form-group input,
.dark-theme .form-group textarea,
.dark-theme .form-group select {
    background: #374151 !important;
    border-color: #4b5563 !important;
    color: #f9fafb !important;
}

.dark-theme .form-group input:focus,
.dark-theme .form-group textarea:focus,
.dark-theme .form-group select:focus {
    border-color: #6366f1 !important;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1) !important;
}

/* ===== MOBILE RESPONSIVENESS ===== */
@media (max-width: 768px) {
    .hero-cta, .cta-buttons {
        gap: 12px !important;
        padding: 0 16px !important;
    }
    
    .hero-cta a, .cta-buttons a, .btn, .cta-btn {
        height: 48px !important;
        min-height: 48px !important;
        max-height: 48px !important;
        font-size: 14px !important;
        padding: 0 16px !important;
    }
    
    .user-menu .user-dropdown {
        width: 120px !important;
        right: -10px !important;
    }
}

@media (max-width: 480px) {
    .hero-cta a, .cta-buttons a, .btn, .cta-btn {
        height: 44px !important;
        min-height: 44px !important;
        max-height: 44px !important;
        font-size: 13px !important;
        padding: 0 12px !important;
    }
}

/* ===== LOGO VISIBILITY FIX - CRITICAL OVERRIDE ===== */
/* Override text-based logo styles for image logos */
.navbar-logo,
.navbar-logo:link,
.navbar-logo:visited,
.navbar-logo:hover,
.navbar-logo:focus,
.navbar-logo:active,
.dark-theme .navbar-logo,
.dark-theme .navbar-logo:link,
.dark-theme .navbar-logo:visited,
.dark-theme .navbar-logo:hover,
.dark-theme .navbar-logo:focus,
.dark-theme .navbar-logo:active {
    display: flex !important;
    align-items: center !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    left: 4px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    z-index: 100 !important;
    height: auto !important;
    width: auto !important;
    max-width: 200px !important;
    overflow: visible !important;
    /* CRITICAL: Remove text-based logo styles */
    background: none !important;
    background-image: none !important;
    background-color: transparent !important;
    background-clip: initial !important;
    -webkit-background-clip: initial !important;
    color: initial !important;
    text-shadow: none !important;
    filter: none !important;
    font-size: initial !important;
    font-weight: initial !important;
    letter-spacing: initial !important;
}

.navbar-logo img,
.dark-theme .navbar-logo img {
    width: 200px !important;
    height: 50px !important;
    object-fit: contain !important;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    max-width: none !important;
    min-width: 200px !important;
    filter: none !important;
    background: none !important;
    background-color: transparent !important;
    border: none !important;
    outline: none !important;
    box-shadow: none !important;
    transform: none !important;
    position: relative !important;
    z-index: 101 !important;
    flex-shrink: 0 !important;
}

/* Ensure navbar container doesn't restrict logo */
.navbar-container {
    overflow: visible !important;
}

/* Prevent any parent containers from collapsing the logo */
.navbar,
.navbar-container,
.navbar-logo {
    min-width: 0 !important;
    flex-shrink: 0 !important;
}
