/**
 * External Link Optimizer
 * Optimizes external links for better performance
 * - Preconnects to external domains
 * - Prefetches important external links
 * - Bypasses unnecessary event handlers for external links
 * - Implements optimal time and space complexity
 */

(function() {
    // Configuration
    const IMPORTANT_DOMAINS = [
        'calendar.app.google',
        'fonts.googleapis.com',
        'fonts.gstatic.com',
        'cdnjs.cloudflare.com'
    ];
    
    // Calendar link specific configuration
    const CALENDAR_LINK = 'https://calendar.app.google/uy4Szgfn4mdyZPLA6';
    
    // Preconnect to important domains
    function preconnectToDomains() {
        IMPORTANT_DOMAINS.forEach(domain => {
            if (!document.querySelector(`link[rel="preconnect"][href*="${domain}"]`)) {
                const link = document.createElement('link');
                link.rel = 'preconnect';
                link.href = `https://${domain}`;
                link.crossOrigin = 'anonymous';
                document.head.appendChild(link);
                
                // Also add DNS prefetch as fallback for browsers that don't support preconnect
                const dnsLink = document.createElement('link');
                dnsLink.rel = 'dns-prefetch';
                dnsLink.href = `https://${domain}`;
                document.head.appendChild(dnsLink);
            }
        });
    }
    
    // Prefetch the calendar link
    function prefetchCalendarLink() {
        if (!document.querySelector(`link[rel="prefetch"][href="${CALENDAR_LINK}"]`)) {
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = CALENDAR_LINK;
            document.head.appendChild(link);
        }
    }
    
    // Optimize calendar links
    function optimizeCalendarLinks() {
        // Find all calendar links
        const calendarLinks = document.querySelectorAll(`a[href="${CALENDAR_LINK}"]`);
        
        calendarLinks.forEach(link => {
            // Remove any existing click event listeners
            const newLink = link.cloneNode(true);
            if (link.parentNode) {
                link.parentNode.replaceChild(newLink, link);
            }
            
            // Add optimized attributes
            newLink.setAttribute('rel', 'noopener noreferrer');
            
            // Add a direct click handler with high performance
            newLink.addEventListener('click', function(e) {
                // Prevent any default handling that might cause delays
                e.stopPropagation();
                
                // Open the link directly without any additional processing
                window.open(CALENDAR_LINK, '_blank', 'noopener,noreferrer');
                
                // Prevent the default action to avoid double-opening
                e.preventDefault();
            }, { passive: false, capture: true });
        });
    }
    
    // Initialize optimizations
    function init() {
        // Run preconnect immediately
        preconnectToDomains();
        
        // Prefetch calendar link
        prefetchCalendarLink();
        
        // Wait for DOM to be ready before optimizing links
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', optimizeCalendarLinks);
        } else {
            optimizeCalendarLinks();
        }
    }
    
    // Run initialization
    init();
})();
