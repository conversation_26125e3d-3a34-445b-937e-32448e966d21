#!/bin/bash

# Ziantrix Dynamic App - Production Startup Script
# This script initializes the database and starts the Flask application

echo "🚀 Starting Ziantrix Dynamic App..."

# Set default port if not provided
export PORT=${PORT:-5000}

# Initialize database if needed
echo "📊 Initializing database..."
python init_db.py --init

# Start the application with gunicorn
echo "🌐 Starting web server on port $PORT..."
exec gunicorn app:app --bind 0.0.0.0:$PORT --workers 1 --timeout 120 --access-logfile - --error-logfile -
