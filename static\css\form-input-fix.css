/* Form Input Fix
 * Ensures form inputs are properly interactive
 * Fixes issue with form inputs not being clickable until after submit button is clicked
 */

/* Increase z-index for all form inputs */
input[type="text"],
input[type="email"],
input[type="password"],
textarea {
    position: relative !important;
    z-index: 100 !important;
}

/* Ensure form groups have proper stacking context */
.form-group {
    position: relative;
    z-index: 10;
}

/* Make sure labels are clickable */
label {
    position: relative;
    z-index: 101;
    cursor: pointer;
}

/* Ensure the contact form has proper stacking context */
.contact-form {
    position: relative;
    z-index: 5;
}

/* Fix for any potential overlays */
.contact-form::before,
.contact-form::after {
    z-index: 1 !important;
}

/* Ensure submit button doesn't overlap inputs */
.submit-btn {
    position: relative;
    z-index: 90;
}

/* Fix for dark theme */
.dark-theme input[type="text"],
.dark-theme input[type="email"],
.dark-theme input[type="password"],
.dark-theme textarea {
    position: relative !important;
    z-index: 100 !important;
}

/* Fix for any potential pointer-events issues */
.contact-form,
.form-group,
input[type="text"],
input[type="email"],
input[type="password"],
textarea,
label,
.submit-btn {
    pointer-events: auto !important;
}

/* Specific fixes for interactive form elements */
.interactive-form {
    position: relative;
    z-index: 1000 !important;
    pointer-events: auto !important;
}

.interactive-input {
    position: relative !important;
    z-index: 1001 !important;
    pointer-events: auto !important;
    cursor: text !important;
}

.interactive-form label {
    position: relative !important;
    z-index: 1002 !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* Remove any potential overlays */
.interactive-form::before,
.interactive-form::after {
    display: none !important;
}

/* Ensure inputs are visible and interactive */
.interactive-input:focus {
    outline: 2px solid var(--color-primary) !important;
    box-shadow: 0 0 0 4px rgba(25, 118, 210, 0.3) !important;
}
