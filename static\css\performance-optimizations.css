/**
 * Performance Optimizations
 * Optimized CSS for 60fps scroll performance and reduced rendering lag
 */

/* Hardware acceleration for critical elements */
.hero-section,
.feature-card,
.service-card,
.solution-card,
.testimonial-card {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize animations to use only transform and opacity */
@keyframes optimizedFadeIn {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

@keyframes optimizedFloat {
  0% { transform: translate3d(0, 0, 0); }
  50% { transform: translate3d(0, -10px, 0); }
  100% { transform: translate3d(0, 0, 0); }
}

/* Replace expensive box-shadow animations with transform-based alternatives */
.service-card,
.feature-card,
.solution-card {
  transition: transform 0.2s ease-out;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.service-card:hover,
.feature-card:hover,
.solution-card:hover {
  transform: translate3d(0, -4px, 0);
  /* Remove box-shadow changes on hover for better performance */
}

/* Optimize scroll-triggered elements */
.scroll-optimized {
  contain: layout style paint;
  will-change: transform;
}

/* Remove expensive filters and use optimized alternatives */
.feature-icon,
.solution-icon,
.service-icon {
  transition: transform 0.2s ease-out;
  /* Remove filter: drop-shadow for better performance */
}

.feature-icon:hover,
.solution-icon:hover,
.service-icon:hover {
  transform: scale(1.05) translate3d(0, 0, 0);
}

/* Optimize text animations */
.hero-title {
  /* Remove expensive text-shadow and gradient animations */
  animation: none;
  background: var(--color-primary);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

/* Optimize background patterns */
.hero-section::before,
.features-section::before,
.services-section::before {
  /* Remove complex background animations */
  animation: none;
  background-attachment: fixed;
}

/* Lightweight hover effects */
.btn,
.cta-btn,
.service-cta-button {
  transition: transform 0.15s ease-out, opacity 0.15s ease-out;
}

.btn:hover,
.cta-btn:hover,
.service-cta-button:hover {
  transform: translate3d(0, -2px, 0);
  /* Remove box-shadow changes */
}

/* Optimize grid layouts for better rendering */
.services-grid,
.features-grid,
.solutions-grid {
  contain: layout;
  will-change: auto; /* Only set will-change when needed */
}

/* Reduce repaints during scroll */
.navbar {
  contain: layout style;
  transform: translateZ(0);
}



/* Remove expensive pseudo-element animations */
.feature-card::before,
.feature-card::after,
.service-card::before,
.service-card::after,
.solution-card::before,
.solution-card::after {
  /* Simplify or remove complex pseudo-element animations */
  transition: transform 0.2s ease-out;
}

/* Optimize form elements */
input,
textarea,
select {
  contain: layout style;
}

/* Reduce animation complexity on mobile */
@media (max-width: 768px) {
  .hero-section,
  .feature-card,
  .service-card,
  .solution-card {
    /* Reduce animations on mobile for better performance */
    animation: none;
    transition: none;
  }

  .service-card:hover,
  .feature-card:hover,
  .solution-card:hover {
    transform: none;
  }
}

/* Critical rendering optimizations */
.above-fold {
  contain: layout style paint;
}

/* Optimize video elements */
video {
  contain: layout style;
  transform: translateZ(0);
}

/* Reduce layout thrashing */
.section-header,
.hero-content {
  contain: layout style;
}

/* Optimize FAQ accordion */
.faq-item {
  contain: layout;
}

/* Performance-focused utility classes */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.no-animation {
  animation: none !important;
  transition: none !important;
}

.lightweight-hover {
  transition: transform 0.15s ease-out;
}

.lightweight-hover:hover {
  transform: translate3d(0, -2px, 0);
}

/* Optimize contact form */
.contact-form {
  contain: layout style;
}

/* Reduce paint complexity */
.about-content {
  contain: layout style paint;
}

/* Optimize footer */
.footer {
  contain: layout style;
}

/* Critical path optimizations */
.critical-section {
  contain: layout style paint;
  will-change: auto;
}

/* Disable expensive effects during scroll */
.scrolling .expensive-animation {
  animation-play-state: paused;
}

.scrolling .expensive-transition {
  transition: none;
}

/* Optimize image containers */
.hero-image,
.about-image,
.testimonial-company-logo {
  contain: layout style;
}

/* Performance monitoring helpers */
.perf-monitor {
  contain: strict;
}

/* Optimize chatbot */
.chatbot-launcher {
  contain: layout style;
  transform: translateZ(0);
}

/* Reduce complexity of gradients */
.gradient-optimized {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
  /* Simplified gradient for better performance */
}

/* Optimize modal elements */
.modal-content {
  contain: layout style paint;
  transform: translateZ(0);
}

/* Lightweight focus indicators */
.focus-optimized:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  /* Simple outline instead of complex box-shadow */
}

/* Optimize theme transitions */
.theme-transition-optimized {
  transition: background-color 0.2s ease, color 0.2s ease;
  /* Reduced transition properties */
}

/* Performance-first responsive design */
@media (prefers-reduced-motion: reduce) {
  * {
    animation: none !important;
    transition: none !important;
  }
}

/* Optimize scroll indicators */
.scroll-indicator {
  contain: layout style;
  transform: translateZ(0);
}

/* Reduce reflow during resize */
.resize-optimized {
  contain: layout size;
}

/* Optimize loading states */
.loading-optimized {
  contain: layout style paint;
  will-change: opacity;
}

/* Critical performance fixes */
.performance-critical {
  contain: strict;
  will-change: transform;
  transform: translateZ(0);
}
