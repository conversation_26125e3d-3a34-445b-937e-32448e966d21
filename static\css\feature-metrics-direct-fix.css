/**
 * Direct fix for feature card metrics
 * This file uses extremely specific selectors with !important flags
 * to override any other styles that might be affecting the metrics
 * and completely remove white boxes in both light and dark mode
 *
 * UPDATED: Added even more aggressive targeting to ensure all backgrounds are removed
 */

/* Light mode metrics - Completely remove white boxes */
body:not(.dark-theme) .feature-card .metric-wrapper,
html body:not(.dark-theme) .feature-card .metric-wrapper,
:not(.dark-theme) .feature-card .metric-wrapper,
body:not(.dark-theme) div.feature-card div.metric-wrapper,
body:not(.dark-theme) .feature-card div[class*="metric"],
body:not(.dark-theme) .feature-card *[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 10 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  margin: 0 auto !important;
  width: auto !important;
  max-width: 100% !important;
  box-shadow: none !important;
  border: none !important;
}

body:not(.dark-theme) .feature-card .feature-metric,
html body:not(.dark-theme) .feature-card .feature-metric,
:not(.dark-theme) .feature-card .feature-metric,
body:not(.dark-theme) div.feature-card div.metric-wrapper div.feature-metric,
body:not(.dark-theme) .feature-card div.feature-metric,
body:not(.dark-theme) .feature-card > .feature-metric {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  color: #1976d2 !important;
  opacity: 1 !important;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  display: block !important;
  padding: 0 !important;
  margin: 1rem auto 0.25rem auto !important;
  text-align: center !important;
  border: none !important;
  box-shadow: none !important;
  width: auto !important;
  max-width: 100% !important;
}

body:not(.dark-theme) .feature-card .feature-metric-label,
html body:not(.dark-theme) .feature-card .feature-metric-label,
:not(.dark-theme) .feature-card .feature-metric-label,
body:not(.dark-theme) div.feature-card div.metric-wrapper p.feature-metric-label,
body:not(.dark-theme) .feature-card p.feature-metric-label,
body:not(.dark-theme) .feature-card > .feature-metric-label {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  color: #333333 !important;
  opacity: 1 !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  display: block !important;
  padding: 0 !important;
  margin: 0.25rem auto !important;
  text-align: center !important;
  border: none !important;
  box-shadow: none !important;
  width: auto !important;
  max-width: 100% !important;
}

body:not(.dark-theme) .feature-card .feature-proof-point,
html body:not(.dark-theme) .feature-card .feature-proof-point,
:not(.dark-theme) .feature-card .feature-proof-point,
body:not(.dark-theme) div.feature-card div.metric-wrapper p.feature-proof-point,
body:not(.dark-theme) .feature-card p.feature-proof-point,
body:not(.dark-theme) .feature-card > .feature-proof-point {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  color: #555555 !important;
  opacity: 1 !important;
  font-size: 0.875rem !important;
  font-style: italic !important;
  display: block !important;
  padding: 0 !important;
  margin: 0.25rem auto 1rem auto !important;
  text-align: center !important;
  border: none !important;
  box-shadow: none !important;
  width: auto !important;
  max-width: 100% !important;
}

/* Dark mode metrics - Completely remove white boxes */
body.dark-theme .feature-card .metric-wrapper,
html body.dark-theme .feature-card .metric-wrapper,
.dark-theme .feature-card .metric-wrapper,
body.dark-theme div.feature-card div.metric-wrapper,
body.dark-theme .feature-card div[class*="metric"],
body.dark-theme .feature-card *[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 10 !important;
  padding: 0 !important;
  border-radius: 0 !important;
  margin: 0 auto !important;
  width: auto !important;
  max-width: 100% !important;
  box-shadow: none !important;
  border: none !important;
}

body.dark-theme .feature-card .feature-metric,
html body.dark-theme .feature-card .feature-metric,
.dark-theme .feature-card .feature-metric,
body.dark-theme div.feature-card div.metric-wrapper div.feature-metric,
body.dark-theme .feature-card div.feature-metric,
body.dark-theme .feature-card > .feature-metric {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  color: #64b5f6 !important;
  opacity: 1 !important;
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  text-shadow: 0 0 10px rgba(100, 181, 246, 0.4) !important;
  display: block !important;
  padding: 0 !important;
  margin: 1rem auto 0.25rem auto !important;
  text-align: center !important;
  border: none !important;
  box-shadow: none !important;
  width: auto !important;
  max-width: 100% !important;
}

body.dark-theme .feature-card .feature-metric-label,
html body.dark-theme .feature-card .feature-metric-label,
.dark-theme .feature-card .feature-metric-label,
body.dark-theme div.feature-card div.metric-wrapper p.feature-metric-label,
body.dark-theme .feature-card p.feature-metric-label,
body.dark-theme .feature-card > .feature-metric-label {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  color: #e0e0e0 !important;
  opacity: 1 !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  display: block !important;
  padding: 0 !important;
  margin: 0.25rem auto !important;
  text-align: center !important;
  border: none !important;
  box-shadow: none !important;
  width: auto !important;
  max-width: 100% !important;
}

body.dark-theme .feature-card .feature-proof-point,
html body.dark-theme .feature-card .feature-proof-point,
.dark-theme .feature-card .feature-proof-point,
body.dark-theme div.feature-card div.metric-wrapper p.feature-proof-point,
body.dark-theme .feature-card p.feature-proof-point,
body.dark-theme .feature-card > .feature-proof-point {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  color: #b0b0b0 !important;
  opacity: 1 !important;
  font-size: 0.875rem !important;
  font-style: italic !important;
  display: block !important;
  padding: 0 !important;
  margin: 0.25rem auto 1rem auto !important;
  text-align: center !important;
  border: none !important;
  box-shadow: none !important;
  width: auto !important;
  max-width: 100% !important;
}

/* Target any inline styles */
.feature-card [style*="background"],
.feature-card [style*="background-color"],
.feature-card [style*="box-shadow"],
.feature-card [style*="border"] {
  background: transparent !important;
  background-color: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

/* Target any pseudo-elements */
.feature-card *::before,
.feature-card *::after {
  background-color: transparent !important;
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

/* Target the specific second feature card with "40+" */
.feature-card:nth-child(2) .feature-metric,
.feature-card:nth-child(2) .feature-metric-label,
.feature-card:nth-child(2) .feature-proof-point,
.feature-card:nth-child(2) .metric-wrapper,
.feature-card:nth-child(2) [class*="metric"],
.feature-card:nth-child(2) *[class*="metric"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 auto !important;
  width: auto !important;
  max-width: 100% !important;
}
