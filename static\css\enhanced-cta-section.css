/* Enhanced CTA Section Styles */
.enhanced-cta-section {
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.05) 0%, rgba(25, 118, 210, 0.1) 100%);
    border-radius: 12px;
    padding: 40px;
    margin: 60px 0;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.06);
    position: relative;
    overflow: hidden;
}

.dark-theme .enhanced-cta-section {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.08) 0%, rgba(33, 150, 243, 0.15) 100%);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.enhanced-cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(25, 118, 210, 0.1) 0%, rgba(25, 118, 210, 0) 70%);
    border-radius: 50%;
    transform: translate(30%, -30%);
    z-index: 0;
}

.enhanced-cta-content {
    position: relative;
    z-index: 1;
}

.enhanced-cta-heading {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 25px;
    color: #1a202c;
    line-height: 1.3;
}

.dark-theme .enhanced-cta-heading {
    color: #f8fafc;
}

.enhanced-cta-benefits {
    list-style: none;
    padding: 0;
    margin: 0 0 30px 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.benefit-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background-color: rgba(25, 118, 210, 0.1);
    border-radius: 50%;
    margin-right: 15px;
    flex-shrink: 0;
}

.dark-theme .benefit-icon {
    background-color: rgba(33, 150, 243, 0.2);
}

.benefit-icon i {
    color: #1976d2;
    font-size: 14px;
}

.dark-theme .benefit-icon i {
    color: #64b5f6;
}

.benefit-text {
    font-size: 1.05rem;
    color: #4a5568;
    line-height: 1.5;
}

.dark-theme .benefit-text {
    color: #cbd5e0;
}

.benefit-highlight {
    font-weight: 600;
    color: #1976d2;
}

.dark-theme .benefit-highlight {
    color: #64b5f6;
}

.enhanced-cta-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
}

.enhanced-cta-btn {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    border-radius: 6px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    text-decoration: none;
}

.enhanced-cta-btn-primary {
    background-color: #1976d2;
    color: white;
    box-shadow: 0 4px 12px rgba(25, 118, 210, 0.2);
}

.enhanced-cta-btn-primary:hover {
    background-color: #1565c0;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(25, 118, 210, 0.3);
}

.enhanced-cta-btn-secondary {
    background-color: transparent;
    color: #1976d2;
    border: 2px solid #1976d2;
}

.enhanced-cta-btn-secondary:hover {
    background-color: rgba(25, 118, 210, 0.08);
    transform: translateY(-2px);
}

.dark-theme .enhanced-cta-btn-primary {
    background-color: #2196f3;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.dark-theme .enhanced-cta-btn-primary:hover {
    background-color: #1e88e5;
}

.dark-theme .enhanced-cta-btn-secondary {
    color: #64b5f6;
    border-color: #64b5f6;
}

.dark-theme .enhanced-cta-btn-secondary:hover {
    background-color: rgba(100, 181, 246, 0.1);
}

.enhanced-cta-btn i {
    margin-left: 8px;
}

@media (max-width: 768px) {
    .enhanced-cta-section {
        padding: 30px 20px;
    }
    
    .enhanced-cta-heading {
        font-size: 1.8rem;
    }
    
    .enhanced-cta-benefits {
        grid-template-columns: 1fr;
    }
    
    .enhanced-cta-buttons {
        flex-direction: column;
    }
    
    .enhanced-cta-btn {
        width: 100%;
        justify-content: center;
    }
}
