{% extends "base.html" %}

{% block title %}Ziantrix | Request a Demo{% endblock %}

{% block meta_description %}Request a demo of Ziantrix's AI-powered chatbot solutions. Experience how our voice and text AI can transform your customer support.{% endblock %}

{% block extra_head %}
<style>
    .demo-section {
        background-color: var(--color-background-alt);
        padding: 4rem 2rem;
    }

    .demo-container {
        max-width: 1200px;
        margin: 0 auto;
        display: flex;
        flex-wrap: wrap;
        gap: 3rem;
        align-items: center;
        justify-content: space-between;
    }

    .demo-content {
        flex: 1;
        min-width: 300px;
    }

    .demo-image {
        flex: 1;
        min-width: 300px;
        text-align: center;
    }

    .demo-image img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }

    .demo-steps {
        margin-top: 2rem;
    }

    .step-item {
        display: flex;
        margin-bottom: 1.5rem;
        align-items: flex-start;
    }

    .step-number {
        background-color: var(--color-primary);
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        flex-shrink: 0;
    }

    .step-content h4 {
        margin-top: 0;
        margin-bottom: 0.5rem;
    }

    .demo-form-container {
        background-color: var(--color-background);
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        max-width: 500px;
        margin: 0 auto;
    }

    .demo-form-header {
        text-align: center;
        margin-bottom: 1.5rem;
    }

    .demo-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }

    .demo-modal.active {
        display: flex !important;
    }

    .modal-content {
        background-color: var(--color-background);
        padding: 2rem;
        border-radius: 8px;
        max-width: 500px;
        width: 90%;
        position: relative;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        animation: modalFadeIn 0.3s ease-out;
    }

    @keyframes modalFadeIn {
        from { opacity: 0; transform: translateY(-20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .dark-theme .demo-modal {
        background-color: #000000;
    }

    .dark-theme .modal-content {
        background-color: #181818;
        border: 1px solid rgba(100, 181, 246, 0.2);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 20px rgba(100, 181, 246, 0.2);
    }

    .close-modal {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--color-text);
    }
</style>
{% endblock %}

{% block header_content %}
<div class="hero-content" style="text-align: center;">
    <h1 style="text-align: center;">Revolutionize Customer Support</h1>
    <p class="hero-subtitle" style="text-align: center;">Voice + Text AI Chatbot solutions for SMBs & Enterprises. Instant, smart & scalable.</p>
    <div class="cta-buttons" style="display: flex; justify-content: center; align-items: center; gap: 1rem; flex-wrap: wrap;">
        <button onclick="openDemoModal()" class="cta-btn primary" style="transition: all 0.3s ease; transform: scale(1);" onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 8px 25px rgba(79, 70, 229, 0.3)';" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">📞 Book a Free Demo — Limited Slots Available</button>
        <a class="cta-btn secondary optimized-calendar-link" href="{{ calendly_link or 'https://calendly.com/vijay-kodam98/demo-call-with-ziantrix' }}" target="_blank" rel="noopener noreferrer preconnect" style="transition: all 0.3s ease; transform: scale(1);" onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 8px 25px rgba(168, 85, 247, 0.3)';" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='none';">
            📞 Book a Free Demo — Limited Slots Available
        </a>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- Demo Section -->
<section class="demo-section">
    <div class="demo-container">
        <div class="demo-content">
            <h2>How Our Demo Works</h2>
            <p>Experience firsthand how Ziantrix can transform your customer support operations with our AI-powered chatbot solutions.</p>

            <div class="demo-steps">
                <div class="step-item">
                    <div class="step-number">1</div>
                    <div class="step-content">
                        <h4>Request a Demo</h4>
                        <p>Fill out our simple form to tell us about your business needs.</p>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">2</div>
                    <div class="step-content">
                        <h4>Schedule a Call</h4>
                        <p>Our team will reach out to schedule a personalized demo session.</p>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">3</div>
                    <div class="step-content">
                        <h4>Experience Ziantrix</h4>
                        <p>See our AI chatbot in action with scenarios tailored to your business.</p>
                    </div>
                </div>

                <div class="step-item">
                    <div class="step-number">4</div>
                    <div class="step-content">
                        <h4>Implementation Plan</h4>
                        <p>Receive a customized implementation plan and pricing proposal.</p>
                    </div>
                </div>
            </div>

            <button onclick="openDemoModal()" class="cta-btn primary" style="margin-top: 2rem;">Transform Customer Experience Today</button>
        </div>

        <div class="demo-image">
            <img src="{{ url_for('static', filename='img/demo-illustration.svg') }}" alt="Ziantrix Demo Process" width="500" height="400">
        </div>
    </div>
</section>

<!-- Features Section -->
<section id="features" class="features-section">
    <div class="section-header">
        <h2>Key Features</h2>
        <p>Discover what makes Ziantrix the leading AI chatbot solution</p>
    </div>

    <div class="features-grid">
        <div class="feature-card">
            <div class="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48"><path fill="none" d="M0 0h24v24H0z"/><path d="M17 3.34a10 10 0 1 1-14.995 8.984L2 12l.005-.324A10 10 0 0 1 17 3.34zm-1.293 5.953a1 1 0 0 0-1.32-.083l-.094.083L11 12.585l-1.293-1.292a1 1 0 0 0-1.497 1.32l.083.094 2 2a1 1 0 0 0 1.32.083l.094-.083 4-4a1 1 0 0 0 0-1.414z" fill="currentColor"/></svg>
            </div>
            <h3>24/7 Availability</h3>
            <p>Always on, always smart. Support your clients anytime, anywhere.</p>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48"><path fill="none" d="M0 0h24v24H0z"/><path d="M4.929 2.929l1.414 1.414A7.975 7.975 0 0 0 4 10c0 2.21.895 4.21 2.343 5.657L4.93 17.07A9.969 9.969 0 0 1 2 10a9.969 9.969 0 0 1 2.929-7.071zm14.142 0A9.969 9.969 0 0 1 22 10a9.969 9.969 0 0 1-2.929 7.071l-1.414-1.414A7.975 7.975 0 0 0 20 10c0-2.21-.895-4.21-2.343-5.657L19.07 2.93zM7.757 5.757l1.415 1.415A3.987 3.987 0 0 0 8 10c0 1.105.448 2.105 1.172 2.828l-1.415 1.415A5.981 5.981 0 0 1 6 10c0-1.657.672-3.157 1.757-4.243zm8.486 0A5.981 5.981 0 0 1 18 10a5.981 5.981 0 0 1-1.757 4.243l-1.415-1.415A3.987 3.987 0 0 0 16 10a3.987 3.987 0 0 0-1.172-2.828l1.415-1.415zM12 12a2 2 0 1 1 0-4 2 2 0 0 1 0 4z" fill="currentColor"/></svg>
            </div>
            <h3>Text + Voice Chat</h3>
            <p>Engage users naturally via voice or text-based AI.</p>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48"><path fill="none" d="M0 0h24v24H0z"/><path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm0-2a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm-1-5h2v2h-2v-2zm2-1.645V14h-2v-1.5a1 1 0 0 1 1-1 1.5 1.5 0 1 0-1.471-1.794l-1.962-.393A3.501 3.501 0 1 1 13 13.355z" fill="currentColor"/></svg>
            </div>
            <h3>Seamless Integration</h3>
            <p>Plug & play into your business. Minimal dev effort required.</p>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="48" height="48"><path fill="none" d="M0 0h24v24H0z"/><path d="M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10zm4.5-14.5L10 10l-2.5 6.5L14 14l2.5-6.5zM12 13a1 1 0 1 1 0-2 1 1 0 0 1 0 2z" fill="currentColor"/></svg>
            </div>
            <h3>Smart Routing</h3>
            <p>AI intelligently routes complex queries to human agents when needed.</p>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section id="contact" class="contact-section">
    <div class="section-header">
        <h2>Contact Us</h2>
        <p>Get in touch with our team to learn more about our solutions</p>
    </div>

    <div class="contact-container">
        <div class="contact-form-container">
            <form id="contactForm" action="/submit_form" method="POST" class="contact-form">
                <input type="hidden" name="source" value="landing">
                <input type="hidden" name="message" value="Contact request from landing page">

                <div class="form-group">
                    <label for="name">Full Name *</label>
                    <input type="text" id="name" name="name" placeholder="Enter your full name" required>
                    <span class="error-message" id="nameError"></span>
                </div>

                <div class="form-group">
                    <label for="email">Email Address *</label>
                    <input type="email" id="email" name="email" placeholder="Enter your email address" required>
                    <span class="error-message" id="emailError"></span>
                </div>

                <div class="form-group">
                    <label for="contactCompany">Company Name</label>
                    <input type="text" id="contactCompany" name="company" placeholder="Enter your company name">
                </div>

                <button type="submit" class="cta-btn submit-btn">Get Expert Support Now</button>
            </form>
        </div>

        <div class="contact-info">
            <div class="contact-method clickable-contact" data-contact-type="email" data-contact-value="<EMAIL>">
                <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M3 3h18a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H3a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1zm17 4.238l-7.928 7.1L4 7.216V19h16V7.238zM4.511 5l7.55 6.662L19.502 5H4.511z" fill="currentColor"/></svg>
                </div>
                <div class="contact-details">
                    <h3>Email Us</h3>
                    <p><EMAIL></p>
                </div>
            </div>

            <div class="contact-method clickable-contact" data-contact-type="phone" data-contact-value="+917093388672">
                <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M9.366 10.682a10.556 10.556 0 0 0 3.952 3.952l.884-1.238a1 1 0 0 1 1.294-.296 11.422 11.422 0 0 0 4.583 1.364 1 1 0 0 1 .921.997v4.462a1 1 0 0 1-.898.995c-.53.055-1.064.082-1.602.082C9.94 21 3 14.06 3 5.5c0-.538.027-1.072.082-1.602A1 1 0 0 1 4.077 3h4.462a1 1 0 0 1 .997.921A11.422 11.422 0 0 0 10.9 8.504a1 1 0 0 1-.296 1.294l-1.238.884zm-2.522-.657l1.9-1.357A13.41 13.41 0 0 1 7.647 5H5.01c-.006.166-.009.333-.009.5C5 12.956 11.044 19 18.5 19c.167 0 .334-.003.5-.01v-2.637a13.41 13.41 0 0 1-3.668-1.097l-1.357 1.9a12.442 12.442 0 0 1-1.588-.75l-.058-.033a12.556 12.556 0 0 1-4.702-4.702l-.033-.058a12.442 12.442 0 0 1-.75-1.588z" fill="currentColor"/></svg>
                </div>
                <div class="contact-details">
                    <h3>Call Us</h3>
                    <p>+91 - 7093388672</p>
                </div>
            </div>

            <div class="contact-method clickable-contact" data-contact-type="linkedin" data-contact-value="https://www.linkedin.com/company/ziantrixai/">
                <div class="contact-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path fill="none" d="M0 0h24v24H0z"/><path d="M6.94 5a2 2 0 1 1-4-.002 2 2 0 0 1 4 .002zM7 8.48H3V21h4V8.48zm6.32 0H9.34V21h3.94v-6.57c0-3.66 4.77-4 4.77 0V21H22v-7.93c0-6.17-7.06-5.94-8.72-2.91l.05-1.68z" fill="currentColor"/></svg>
                </div>
                <div class="contact-details">
                    <h3>LinkedIn</h3>
                    <p>Connect with us</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Demo Request Modal -->
<div id="demoModal" class="demo-modal">
    <div class="modal-content">
        <button class="close-modal" onclick="closeDemoModal()">&times;</button>

        <div class="demo-form-container">
            <div class="demo-form-header">
                <h3>Contact Us for Demo</h3>
                <p>Quick form to get started</p>
            </div>

            <form id="demoForm" action="/submit_form" method="POST" class="contact-form">
                <input type="hidden" name="source" value="demo">
                <input type="hidden" name="message" value="Demo request - interested in AI chatbot solutions">

                <div class="form-group">
                    <label for="demoName">Full Name *</label>
                    <input type="text" id="demoName" name="name" placeholder="Enter your full name" required>
                    <span class="error-message" id="demoNameError"></span>
                </div>

                <div class="form-group">
                    <label for="demoEmail">Email Address *</label>
                    <input type="email" id="demoEmail" name="email" placeholder="Enter your email address" required>
                    <span class="error-message" id="demoEmailError"></span>
                </div>

                <div class="form-group">
                    <label for="demoCompany">Company Name *</label>
                    <input type="text" id="demoCompany" name="company" placeholder="Enter your company name" required>
                    <span class="error-message" id="demoCompanyError"></span>
                </div>

                <div class="form-group">
                    <label for="demoPhone">Phone Number</label>
                    <input type="tel" id="demoPhone" name="phone" placeholder="Enter your phone number">
                </div>

                <button type="submit" class="cta-btn submit-btn">Start Cutting Support Costs Today</button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation for contact form
        const contactForm = document.getElementById('contactForm');
        const nameInput = document.getElementById('name');
        const emailInput = document.getElementById('email');

        const nameError = document.getElementById('nameError');
        const emailError = document.getElementById('emailError');

        // Form validation for demo form
        const demoForm = document.getElementById('demoForm');
        const demoNameInput = document.getElementById('demoName');
        const demoEmailInput = document.getElementById('demoEmail');
        const demoCompanyInput = document.getElementById('demoCompany');

        const demoNameError = document.getElementById('demoNameError');
        const demoEmailError = document.getElementById('demoEmailError');
        const demoCompanyError = document.getElementById('demoCompanyError');

        function validateEmail(email) {
            const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return re.test(String(email).toLowerCase());
        }

        function validateContactForm(e) {
            let isValid = true;

            // Reset errors
            nameError.textContent = '';
            emailError.textContent = '';

            // Validate name
            if (nameInput.value.trim().length < 2) {
                nameError.textContent = 'Name must be at least 2 characters';
                isValid = false;
            }

            // Validate email
            if (!validateEmail(emailInput.value.trim())) {
                emailError.textContent = 'Please enter a valid email address';
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
            }
        }

        function validateDemoForm(e) {
            let isValid = true;

            // Reset errors
            demoNameError.textContent = '';
            demoEmailError.textContent = '';
            demoCompanyError.textContent = '';

            // Validate name
            if (demoNameInput.value.trim().length < 2) {
                demoNameError.textContent = 'Name must be at least 2 characters';
                isValid = false;
            }

            // Validate email
            if (!validateEmail(demoEmailInput.value.trim())) {
                demoEmailError.textContent = 'Please enter a valid email address';
                isValid = false;
            }

            // Validate company
            if (demoCompanyInput.value.trim().length < 2) {
                demoCompanyError.textContent = 'Company name is required';
                isValid = false;
            }

            if (!isValid) {
                e.preventDefault();
            }
        }

        if (contactForm) {
            contactForm.addEventListener('submit', validateContactForm);
        }

        if (demoForm) {
            demoForm.addEventListener('submit', validateDemoForm);
        }

        // Contact method click functionality
        const clickableContacts = document.querySelectorAll('.clickable-contact');

        clickableContacts.forEach(contact => {
            contact.addEventListener('click', function() {
                const contactType = this.getAttribute('data-contact-type');
                const contactValue = this.getAttribute('data-contact-value');

                switch(contactType) {
                    case 'email':
                        window.location.href = `mailto:${contactValue}`;
                        break;
                    case 'phone':
                        window.location.href = `tel:${contactValue}`;
                        break;
                    case 'linkedin':
                        window.open(contactValue, '_blank', 'noopener,noreferrer');
                        break;
                }
            });

            // Add cursor pointer style
            contact.style.cursor = 'pointer';
        });
    });

    // Demo Modal Functions
    function openDemoModal() {
        const modal = document.getElementById('demoModal');
        if (modal) {
            modal.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
        }
    }

    function closeDemoModal() {
        const modal = document.getElementById('demoModal');
        if (modal) {
            modal.classList.remove('active');
            document.body.style.overflow = ''; // Restore scrolling
        }
    }

    // Make sure the openDemoModal function is available globally
    window.openDemoModal = openDemoModal;
    window.closeDemoModal = closeDemoModal;

    // Check if URL has #demoModal hash and open modal if it does
    document.addEventListener('DOMContentLoaded', function() {
        if (window.location.hash === '#demoModal') {
            setTimeout(function() {
                openDemoModal();
            }, 500); // Slight delay to ensure page is loaded
        }
    });

    // Close modal when clicking outside of it
    window.addEventListener('click', function(event) {
        const modal = document.getElementById('demoModal');
        if (event.target === modal) {
            closeDemoModal();
        }
    });
</script>
{% endblock %}
