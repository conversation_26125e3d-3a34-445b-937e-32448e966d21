// Chatbot functions
function openChatbot() {
    // Show chatbot widget
    document.getElementById('chatbot-widget').classList.add('active');

    // Hide launcher
    const launcher = document.getElementById('chatbot-launcher');
    if (launcher) {
        launcher.style.display = 'none';
        launcher.classList.add('hidden');
    }

    // Scroll chat to bottom when opened
    setTimeout(scrollChatToBottom, 100);

    // Focus on input field
    setTimeout(() => {
        document.getElementById('chatbot-input-field').focus();
    }, 300);
}

function closeChatbot() {
    // Hide chatbot widget
    document.getElementById('chatbot-widget').classList.remove('active');

    // Show launcher
    const launcher = document.getElementById('chatbot-launcher');
    if (launcher) {
        launcher.style.display = 'flex';
        launcher.classList.remove('hidden');
    }
}

function minimizeChatbot() {
    // Hide chatbot widget
    document.getElementById('chatbot-widget').classList.remove('active');

    // Show launcher
    const launcher = document.getElementById('chatbot-launcher');
    if (launcher) {
        launcher.style.display = 'flex';
        launcher.classList.remove('hidden');
    }
}

function sendMessage() {
    const inputField = document.getElementById('chatbot-input-field');
    const message = inputField.value.trim();

    if (message) {
        // Add user message
        const messagesContainer = document.querySelector('.chatbot-messages');
        messagesContainer.innerHTML += `
            <div class="message user-message">
                <div class="message-content">
                    <p>${message}</p>
                </div>
            </div>
        `;

        // Clear input
        inputField.value = '';

        // Scroll to bottom immediately after adding user message
        scrollChatToBottom();

        // Show typing indicator
        const typingIndicator = document.querySelector('.typing-indicator-container');
        if (typingIndicator) {
            typingIndicator.style.display = 'block';
            // Scroll to show typing indicator
            scrollChatToBottom();
        }

        // Call the chatbot API
        fetch('/api/chatbot', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ message: message })
        })
        .then(response => response.json())
        .then(data => {
            // Hide typing indicator
            if (typingIndicator) {
                typingIndicator.style.display = 'none';
            }

            // Format the response text with line breaks
            const formattedResponse = data.response.replace(/\n/g, '<br>');

            // Add bot response
            messagesContainer.innerHTML += `
                <div class="message bot-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>${formattedResponse}</p>
                    </div>
                </div>
            `;

            // If this is a pricing query or requires contact, add a contact prompt
            // But only if we haven't already shown buttons in the last message
            if (data.contact_prompt && !messagesContainer.querySelector('.message:last-child .chatbot-buttons')) {
                setTimeout(() => {
                    messagesContainer.innerHTML += `
                        <div class="message bot-message">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <p>Would you like to schedule a call with our team?</p>
                                <div class="chatbot-buttons">
                                    <button onclick="openDemoModal()" class="chatbot-button">Schedule a Call</button>
                                    <button onclick="addMessage('No thanks, I have another question')" class="chatbot-button">Ask Another Question</button>
                                </div>
                            </div>
                        </div>
                    `;
                    scrollChatToBottom();
                }, 1000);
            }

            // Scroll to bottom after adding bot response
            scrollChatToBottom();

            // Add animation class to the latest message
            const latestMessage = messagesContainer.querySelector('.message:last-child');
            if (latestMessage) {
                latestMessage.classList.add('message-appear');

                // Remove the animation class after animation completes to allow re-animation if needed
                setTimeout(() => {
                    latestMessage.classList.remove('message-appear');
                }, 1000);
            }

            // Save chat history
            saveChatHistory();
        })
        .catch(error => {
            console.error('Error:', error);

            // Hide typing indicator
            if (typingIndicator) {
                typingIndicator.style.display = 'none';
            }

            // Add error message
            messagesContainer.innerHTML += `
                <div class="message bot-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>I'm sorry, I encountered an error. Please try again or contact our support team.</p>
                    </div>
                </div>
            `;

            // Scroll to bottom after adding bot response
            scrollChatToBottom();
        });
    }
}

// Function to add a message programmatically (for button responses)
function addMessage(text) {
    const messagesContainer = document.querySelector('.chatbot-messages');

    // Add user message
    messagesContainer.innerHTML += `
        <div class="message user-message">
            <div class="message-content">
                <p>${text}</p>
            </div>
        </div>
    `;

    // Scroll to bottom
    scrollChatToBottom();

    // Process the message as if the user typed it
    const typingIndicator = document.querySelector('.typing-indicator-container');
    if (typingIndicator) {
        typingIndicator.style.display = 'block';
        scrollChatToBottom();
    }

    // Call the chatbot API
    fetch('/api/chatbot', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ message: text })
    })
    .then(response => response.json())
    .then(data => {
        // Hide typing indicator
        if (typingIndicator) {
            typingIndicator.style.display = 'none';
        }

        // Format the response text with line breaks
        const formattedResponse = data.response.replace(/\n/g, '<br>');

        // Add bot response
        messagesContainer.innerHTML += `
            <div class="message bot-message">
                <div class="message-avatar">
                    <span style="font-size: 14px; color: #4f46e5; font-weight: bold;">Z</span>
                </div>
                <div class="message-content">
                    <p>${formattedResponse}</p>
                </div>
            </div>
        `;

        // Scroll to bottom after adding bot response
        scrollChatToBottom();

        // Add animation class to the latest message
        const latestMessage = messagesContainer.querySelector('.message:last-child');
        if (latestMessage) {
            latestMessage.classList.add('message-appear');

            // Remove the animation class after animation completes to allow re-animation if needed
            setTimeout(() => {
                latestMessage.classList.remove('message-appear');
            }, 1000);
        }

        // Save chat history
        saveChatHistory();
    })
    .catch(error => {
        console.error('Error:', error);

        // Hide typing indicator
        if (typingIndicator) {
            typingIndicator.style.display = 'none';
        }

        // Add error message
        messagesContainer.innerHTML += `
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <p>I'm sorry, I encountered an error. Please try again or contact our support team.</p>
                </div>
            </div>
        `;

        // Scroll to bottom after adding bot response
        scrollChatToBottom();

        // Add animation class to the latest message
        const latestMessage = messagesContainer.querySelector('.message:last-child');
        if (latestMessage) {
            latestMessage.classList.add('message-appear');

            // Remove the animation class after animation completes to allow re-animation if needed
            setTimeout(() => {
                latestMessage.classList.remove('message-appear');
            }, 1000);
        }

        // Save chat history
        saveChatHistory();
    });
}

// Helper function to scroll chat to bottom
function scrollChatToBottom() {
    const messagesContainer = document.querySelector('.chatbot-messages');
    if (messagesContainer) {
        // Smooth scroll to bottom
        messagesContainer.scrollTo({
            top: messagesContainer.scrollHeight,
            behavior: 'smooth'
        });
    }
}

// Save chat history to local storage
function saveChatHistory() {
    const messagesContainer = document.querySelector('.chatbot-messages');
    if (messagesContainer) {
        // Don't save typing indicator
        const typingIndicator = messagesContainer.querySelector('.typing-indicator-container');
        if (typingIndicator) {
            typingIndicator.style.display = 'none';
        }

        // Save HTML content
        localStorage.setItem('ziantrix_chat_history', messagesContainer.innerHTML);

        // Keep typing indicator hidden - don't restore display state
        if (typingIndicator) {
            typingIndicator.style.display = 'none';
        }
    }
}

// Load chat history from local storage
function loadChatHistory() {
    const messagesContainer = document.querySelector('.chatbot-messages');
    const savedHistory = localStorage.getItem('ziantrix_chat_history');

    if (messagesContainer && savedHistory) {
        // Keep only the first welcome message from the current HTML
        const firstMessage = messagesContainer.querySelector('.message.bot-message');
        if (firstMessage) {
            // Replace the rest with saved history
            messagesContainer.innerHTML = savedHistory;

            // Make sure typing indicator is at the end and hidden
            const typingIndicator = messagesContainer.querySelector('.typing-indicator-container');
            if (typingIndicator) {
                messagesContainer.appendChild(typingIndicator);
                typingIndicator.style.display = 'none';
            }

            // Scroll to bottom
            scrollChatToBottom();
        }
    }
}

// Save theme preference to local storage
function saveThemePreference() {
    const isDarkMode = document.body.classList.contains('dark-theme');
    localStorage.setItem('ziantrix_theme', isDarkMode ? 'dark' : 'light');
}

// Load theme preference from local storage
function loadThemePreference() {
    // Use the same key as enhanced-theme-toggle.js
    const storedTheme = localStorage.getItem('ziantrix_theme') || localStorage.getItem('ziantrix_dark_mode');
    if (storedTheme === 'dark' || storedTheme === 'true') {
        document.body.classList.add('dark-theme');
    } else {
        document.body.classList.remove('dark-theme');
    }
}

// Initialize chatbot
document.addEventListener('DOMContentLoaded', function() {
    // Load theme preference
    loadThemePreference();

    // Load chat history
    setTimeout(loadChatHistory, 100);

    // Ensure typing indicator is hidden on load
    const typingIndicator = document.querySelector('.typing-indicator-container');
    if (typingIndicator) {
        typingIndicator.style.display = 'none';
    }

    // Allow sending message with Enter key
    const inputField = document.getElementById('chatbot-input-field');
    if (inputField) {
        inputField.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
                e.preventDefault(); // Prevent form submission if inside a form
            }
        });
    }

    // Ensure close button works properly
    const closeBtn = document.querySelector('.chatbot-controls .close-btn');
    if (closeBtn) {
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeChatbot();
        });
    }

    // Ensure minimize button works properly
    const minimizeBtn = document.querySelector('.chatbot-controls .minimize-btn');
    if (minimizeBtn) {
        minimizeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            minimizeChatbot();
        });
    }

    // Add scroll event listener to show/hide scroll indicator
    const messagesContainer = document.querySelector('.chatbot-messages');
    if (messagesContainer) {
        messagesContainer.addEventListener('scroll', function() {
            // No need to change opacity since we want it always visible
            const isScrolledToBottom = messagesContainer.scrollHeight - messagesContainer.clientHeight <= messagesContainer.scrollTop + 50;
            // We can still animate it differently when at bottom
            const scrollIndicator = document.querySelector('.scroll-indicator');
            if (scrollIndicator && isScrolledToBottom) {
                scrollIndicator.style.animation = 'none';
            } else if (scrollIndicator) {
                scrollIndicator.style.animation = 'pulse-scroll 2s infinite';
            }
        });
    }

    // Theme toggle is now handled by enhanced-theme-toggle.js
    // We'll just update our local storage when theme changes
    document.addEventListener('themeChanged', function(e) {
        saveThemePreference();
    });
});
