/**
 * Complete Navigation Fix
 * A fresh implementation to fix all navigation highlighting issues
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get all navigation links
    const navLinks = document.querySelectorAll('.navbar-link');

    // Function to clear all active classes
    function clearAllActive() {
        navLinks.forEach(link => {
            link.classList.remove('active');
        });
    }

    // Function to highlight a specific link
    function highlightLink(link) {
        clearAllActive();
        if (link) {
            link.classList.add('active');
        }
    }

    // Function to get the current section on scroll
    function getCurrentSection() {
        // Only apply on homepage
        if (window.location.pathname !== '/' && window.location.pathname !== '') {
            return null;
        }

        const scrollPosition = window.scrollY + 100;
        const sections = document.querySelectorAll('section[id]');
        let currentSection = null;

        sections.forEach(section => {
            // Skip about and contact sections
            const sectionId = section.getAttribute('id');
            if (sectionId === 'about' || sectionId === 'contact') {
                return;
            }

            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;

            if (scrollPosition >= sectionTop && scrollPosition < sectionTop + sectionHeight) {
                currentSection = sectionId;
            }
        });

        return currentSection;
    }

    // Set initial active link based on URL
    function setInitialActive() {
        const currentPath = window.location.pathname;
        const hash = window.location.hash;

        clearAllActive();

        // Handle specific pages - ONLY highlight About and Support when on their specific pages
        if (currentPath === '/about') {
            const aboutLink = document.getElementById('about-nav-link');
            highlightLink(aboutLink);
        }
        else if (currentPath === '/support') {
            const supportLink = document.getElementById('support-nav-link');
            highlightLink(supportLink);
        }
        // Handle hash navigation
        else if (hash) {
            const targetId = hash.substring(1);
            // Skip highlighting for about and contact sections when accessed via hash
            if (targetId !== 'about' && targetId !== 'contact') {
                const link = document.querySelector(`.navbar-link[href="/#${targetId}"]`);
                highlightLink(link);
            }
        }
        // Default to features on homepage
        else if (currentPath === '/' || currentPath === '') {
            const featuresLink = document.querySelector('.navbar-link[href="/#features"]');
            highlightLink(featuresLink);
        }
    }

    // Add click handlers to all navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');

            // Handle hash navigation
            if (href && href.startsWith('/#')) {
                e.preventDefault();

                // Highlight this link
                highlightLink(this);

                // Scroll to section
                const targetId = href.replace('/#', '');
                const targetSection = document.getElementById(targetId);

                if (targetSection) {
                    const headerOffset = 60;
                    const elementPosition = targetSection.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });

                    // Update URL
                    history.pushState(null, null, href);
                }
            }
            // For About and Support links, just highlight them
            else if (href === '/about' || href === '/support') {
                highlightLink(this);
                // Let the default navigation happen
            }
        });
    });

    // Handle scroll events with throttling
    let scrollTimeout;
    window.addEventListener('scroll', function() {
        if (scrollTimeout) {
            clearTimeout(scrollTimeout);
        }

        scrollTimeout = setTimeout(function() {
            const currentSection = getCurrentSection();

            if (currentSection) {
                const link = document.querySelector(`.navbar-link[href="/#${currentSection}"]`);
                if (link) {
                    highlightLink(link);
                }
            }
        }, 100);
    });

    // Set initial active link
    setInitialActive();
});
