/**
 * Interactive FAQ Functionality
 * Handles accordion behavior and search functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get all FAQ items
    const faqItems = document.querySelectorAll('.faq-item');
    const faqQuestions = document.querySelectorAll('.faq-question');
    const faqSearch = document.getElementById('faqSearch');
    const clearSearch = document.getElementById('clearSearch');
    const faqAccordion = document.querySelector('.faq-accordion');

    // Add no results message
    const noResultsElement = document.createElement('div');
    noResultsElement.className = 'no-results';
    noResultsElement.innerHTML = `
        <div class="no-results-icon">
            <i class="fas fa-search"></i>
        </div>
        <h3>No matching questions found</h3>
        <p>Try different keywords or browse all questions</p>
        <button type="button" id="resetSearch" class="reset-button">View all questions</button>
    `;
    faqAccordion.appendChild(noResultsElement);

    // Add event listener for reset button
    const resetSearch = document.getElementById('resetSearch');
    if (resetSearch) {
        resetSearch.addEventListener('click', () => {
            if (faqSearch) {
                faqSearch.value = '';
                // Trigger search reset
                const inputEvent = new Event('input');
                faqSearch.dispatchEvent(inputEvent);
            }
        });
    }

    // Toggle FAQ answers
    faqQuestions.forEach(question => {
        question.addEventListener('click', () => {
            const expanded = question.getAttribute('aria-expanded') === 'true';

            // Close all other questions
            faqQuestions.forEach(q => {
                if (q !== question) {
                    q.setAttribute('aria-expanded', 'false');
                    const answer = q.nextElementSibling;
                    answer.setAttribute('aria-hidden', 'true');
                    answer.style.maxHeight = '0';
                }
            });

            // Toggle current question
            question.setAttribute('aria-expanded', !expanded);
            const answer = question.nextElementSibling;
            answer.setAttribute('aria-hidden', expanded);

            // Toggle the icon between down and up arrow
            const toggleIcon = question.querySelector('.faq-toggle i');
            if (toggleIcon) {
                if (!expanded) {
                    toggleIcon.classList.remove('fa-angle-down');
                    toggleIcon.classList.add('fa-angle-up');
                    answer.style.maxHeight = answer.scrollHeight + 'px';
                } else {
                    toggleIcon.classList.remove('fa-angle-up');
                    toggleIcon.classList.add('fa-angle-down');
                    answer.style.maxHeight = '0';
                }
            } else {
                // If no toggle icon, just toggle the max height
                if (!expanded) {
                    answer.style.maxHeight = answer.scrollHeight + 'px';
                } else {
                    answer.style.maxHeight = '0';
                }
            }
        });
    });

    // Search functionality
    if (faqSearch) {
        // Function to perform search
        function performSearch() {
            const searchTerm = faqSearch.value.toLowerCase().trim();
            let matchFound = false;

            // Show/hide clear button
            if (searchTerm.length > 0) {
                clearSearch.style.display = 'block';
            } else {
                clearSearch.style.display = 'none';
            }

            // Split search term into keywords for better matching
            const keywords = searchTerm.split(/\s+/);

            faqItems.forEach(item => {
                const question = item.querySelector('.question-text').textContent.toLowerCase();
                const answer = item.querySelector('.answer-content').textContent.toLowerCase();
                const itemKeywords = item.getAttribute('data-keywords')?.toLowerCase() || '';

                if (searchTerm === '') {
                    // Show all items when search is empty
                    item.style.display = 'block';
                    matchFound = true;
                    removeHighlights(item);
                } else {
                    // Only check if keywords match in question (not in answer or data-keywords)
                    const matches = keywords.some(keyword =>
                        keyword.length > 2 && question.includes(keyword)
                    );

                    // Also check for exact phrase match in question only
                    const exactMatch = question.includes(searchTerm);

                    if (matches || exactMatch) {
                        item.style.display = 'block';
                        matchFound = true;
                        highlightMatches(item, keywords);
                    } else {
                        item.style.display = 'none';
                        removeHighlights(item);
                    }
                }
            });

            // Show/hide no results message
            if (!matchFound && searchTerm !== '') {
                noResultsElement.classList.add('visible');
            } else {
                noResultsElement.classList.remove('visible');
            }

            // Auto-expand first matching item if search term exists
            if (searchTerm !== '' && matchFound) {
                const firstVisibleItem = Array.from(faqItems).find(item => item.style.display !== 'none');
                if (firstVisibleItem) {
                    const question = firstVisibleItem.querySelector('.faq-question');
                    const answer = firstVisibleItem.querySelector('.faq-answer');

                    question.setAttribute('aria-expanded', 'true');
                    answer.setAttribute('aria-hidden', 'false');
                    answer.style.maxHeight = answer.scrollHeight + 'px';

                    // Update the toggle icon
                    const toggleIcon = question.querySelector('.faq-toggle i');
                    if (toggleIcon) {
                        toggleIcon.classList.remove('fa-angle-down');
                        toggleIcon.classList.add('fa-angle-up');
                    }
                }
            }
        }

        // Debounce function to limit how often a function can run
        function debounce(func, wait) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), wait);
            };
        }

        // Input event for real-time search (debounced)
        faqSearch.addEventListener('input', debounce(() => {
            performSearch();
        }, 300));

        // Search button click
        const searchButton = document.getElementById('searchButton');
        if (searchButton) {
            searchButton.addEventListener('click', (e) => {
                e.preventDefault(); // Prevent any default form submission
                performSearch();

                // Ensure content is visible by expanding all matching items
                const searchTerm = faqSearch.value.toLowerCase().trim();
                if (searchTerm !== '') {
                    const visibleItems = Array.from(faqItems).filter(item => item.style.display !== 'none');
                    visibleItems.forEach(item => {
                        const question = item.querySelector('.faq-question');
                        const answer = item.querySelector('.faq-answer');

                        question.setAttribute('aria-expanded', 'true');
                        answer.setAttribute('aria-hidden', 'false');
                        answer.style.maxHeight = answer.scrollHeight + 'px';

                        // Update the toggle icon
                        const toggleIcon = question.querySelector('.faq-toggle i');
                        if (toggleIcon) {
                            toggleIcon.classList.remove('fa-angle-down');
                            toggleIcon.classList.add('fa-angle-up');
                        }
                    });
                }

                console.log('Search button clicked and results expanded'); // Debug log
            });
        }

        // Enter key in search box
        faqSearch.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                // Simulate clicking the search button to ensure consistent behavior
                if (searchButton) {
                    searchButton.click();
                }
            }
        });

        // Clear search
        clearSearch.addEventListener('click', () => {
            faqSearch.value = '';
            faqSearch.focus();

            // Trigger search reset
            performSearch();
        });
    }

    // Function to highlight matching text - only in questions, not answers
    function highlightMatches(item, keywords) {
        // Remove existing highlights first
        removeHighlights(item);

        // Elements to search in - only question text, not answer content
        const questionText = item.querySelector('.question-text');

        // Store original content if not already stored
        if (!questionText.hasAttribute('data-original')) {
            questionText.setAttribute('data-original', questionText.innerHTML);
        }

        // Highlight in question only
        questionText.innerHTML = highlightText(questionText.getAttribute('data-original'), keywords);
    }

    // Function to remove highlights - simplified to match our highlighting approach
    function removeHighlights(item) {
        const questionText = item.querySelector('.question-text');

        // Reset question if original content exists
        if (questionText.hasAttribute('data-original')) {
            questionText.innerHTML = questionText.getAttribute('data-original');
        }
    }

    // Function to highlight text with multiple keywords
    function highlightText(text, keywords) {
        if (!keywords || (Array.isArray(keywords) && keywords.length === 0)) return text;

        let result = text;

        // If keywords is a string, convert it to an array
        if (typeof keywords === 'string') {
            keywords = [keywords];
        }

        // Process each keyword
        keywords.forEach(keyword => {
            if (keyword.length <= 2) return; // Skip very short keywords

            const regex = new RegExp(`(${escapeRegExp(keyword)})`, 'gi');
            result = result.replace(regex, '<span class="highlight">$1</span>');
        });

        return result;
    }

    // Helper function to escape special characters in regex
    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // Track FAQ interactions for analytics
    faqQuestions.forEach(question => {
        question.addEventListener('click', () => {
            const questionText = question.querySelector('.question-text').textContent;
            // If analytics is available, track the interaction
            if (typeof gtag === 'function') {
                gtag('event', 'faq_click', {
                    'event_category': 'FAQ',
                    'event_label': questionText
                });
            }
        });
    });
});
