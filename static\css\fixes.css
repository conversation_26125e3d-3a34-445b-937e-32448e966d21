/**
 * Direct fixes for specific issues
 */

/* Fix for feature card metrics transparency */
.feature-card .feature-metric,
.feature-card .feature-metric-label,
.feature-card .metric-wrapper {
  background-color: #ffffff !important;
  opacity: 1 !important;
}

.dark-theme .feature-card .feature-metric,
.dark-theme .feature-card .feature-metric-label,
.dark-theme .feature-card .metric-wrapper {
  background-color: #1e1e1e !important;
  opacity: 1 !important;
}

/* Fix for header highlighting */
.navbar-link {
  position: relative;
}

.navbar-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: #1976d2;
  transition: all 0.3s ease;
  transform: translateX(-50%);
  opacity: 0;
}

.navbar-link:hover::after {
  width: 80%;
  opacity: 1;
}

.navbar-link.active {
  color: #1976d2 !important;
  font-weight: 600 !important;
}

.navbar-link.active::after {
  width: 80%;
  opacity: 1;
}

.dark-theme .navbar-link::after {
  background-color: #64b5f6;
}

.dark-theme .navbar-link.active {
  color: #64b5f6 !important;
}

/* Override any other styles */
.navbar-link[href="/#features"] {
  position: relative;
}

.navbar-link[href="/#features"]::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  width: 0;
  height: 2px;
  background-color: #1976d2;
  transition: all 0.3s ease;
  transform: translateX(-50%);
  opacity: 0;
}

.navbar-link[href="/#features"]:hover::after,
.navbar-link[href="/#features"].active::after {
  width: 80%;
  opacity: 1;
}

.navbar-link[href="/#features"].active {
  color: #1976d2 !important;
  font-weight: 600 !important;
}

.dark-theme .navbar-link[href="/#features"]::after {
  background-color: #64b5f6;
}

.dark-theme .navbar-link[href="/#features"].active {
  color: #64b5f6 !important;
}
