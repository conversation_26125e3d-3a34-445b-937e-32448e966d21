/**
 * Service Feature Text Fix
 * This script ensures the feature text doesn't overlap with the buttons
 */

document.addEventListener('DOMContentLoaded', function() {
    // Apply fixes immediately and after delays to ensure they take effect
    fixServiceFeatureText();
    setTimeout(fixServiceFeatureText, 100);
    setTimeout(fixServiceFeatureText, 500);
    setTimeout(fixServiceFeatureText, 1000);
});

function fixServiceFeatureText() {
    // Get all service cards
    const serviceCards = document.querySelectorAll('.service-card');
    
    // Process each service card
    serviceCards.forEach(function(card) {
        // Get the card body and footer
        const cardBody = card.querySelector('.service-card-body');
        const cardFooter = card.querySelector('.service-card-footer');
        
        if (cardBody && cardFooter) {
            // Ensure the card body has enough padding at the bottom
            cardBody.style.paddingBottom = '100px';
            
            // Ensure the footer is positioned correctly
            cardFooter.style.position = 'absolute';
            cardFooter.style.bottom = '24px';
            cardFooter.style.left = '0';
            cardFooter.style.right = '0';
            cardFooter.style.width = '100%';
            cardFooter.style.boxSizing = 'border-box';
            cardFooter.style.zIndex = '10';
            
            // Get the button in the footer
            const button = cardFooter.querySelector('.service-cta-button');
            if (button) {
                // Add a background to the button to hide any text behind it
                button.style.position = 'relative';
                button.style.zIndex = '20';
                
                // Create a background element if it doesn't exist
                let background = cardFooter.querySelector('.button-background');
                if (!background) {
                    background = document.createElement('div');
                    background.className = 'button-background';
                    cardFooter.insertBefore(background, button);
                }
                
                // Style the background element
                background.style.position = 'absolute';
                background.style.top = '-10px';
                background.style.left = '24px';
                background.style.right = '24px';
                background.style.bottom = '0';
                background.style.backgroundColor = getComputedStyle(card).backgroundColor;
                background.style.zIndex = '5';
            }
        }
        
        // Get the feature list
        const featureList = card.querySelector('.service-features-list');
        if (featureList) {
            // Get all feature items
            const featureItems = featureList.querySelectorAll('li');
            
            // Process each feature item
            featureItems.forEach(function(item, index) {
                // Get the feature icon and text
                const icon = item.querySelector('.feature-icon');
                const text = item.querySelector('.feature-text');
                
                if (icon && text) {
                    // Style the icon
                    icon.style.display = 'inline-flex';
                    icon.style.alignItems = 'center';
                    icon.style.justifyContent = 'center';
                    icon.style.marginRight = '16px';
                    icon.style.width = '28px';
                    icon.style.height = '28px';
                    icon.style.minWidth = '28px';
                    icon.style.flexShrink = '0';
                    icon.style.backgroundColor = 'transparent';
                    icon.style.verticalAlign = 'middle';
                    
                    // Style the text
                    text.style.display = 'inline-block';
                    text.style.flex = '1';
                    text.style.textAlign = 'left';
                    text.style.paddingTop = '2px';
                    text.style.paddingBottom = '2px';
                    text.style.lineHeight = '1.5';
                    text.style.verticalAlign = 'middle';
                    
                    // If this is the last item, ensure it doesn't overlap with the button
                    if (index === featureItems.length - 1) {
                        item.style.marginBottom = '80px';
                    }
                }
            });
        }
    });
}
