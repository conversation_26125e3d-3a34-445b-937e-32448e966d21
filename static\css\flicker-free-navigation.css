/**
 * FLICKER-FREE NAVIGATION CSS
 * Optimized for smooth, flicker-free navigation highlighting
 * Eliminates all transition conflicts and provides consistent styling
 */

/* ===== RESET AND BASE STYLES ===== */

/* Reset all navigation elements to prevent conflicts */
.navbar-link,
.dropdown-item,
a[data-target],
#about-nav-link,
#support-nav-link,
#services-nav-link,
.navbar-dropdown .navbar-link,
.navbar-dropdown .dropdown-toggle {
    /* Remove all conflicting properties */
    transition: none !important;
    animation: none !important;
    transform: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
    border: none !important;
    border-bottom: none !important;
    background-color: transparent !important;

    /* Set consistent base styles */
    position: relative !important;
    display: inline-block !important;
    text-decoration: none !important;
    font-weight: normal !important;
    padding: 8px 12px !important;
    margin: 0 1px !important;
    border-radius: 0 !important;

    /* Color inheritance */
    color: var(--color-text, #333) !important;
}

/* Dark theme base styles */
.dark-theme .navbar-link,
.dark-theme .dropdown-item,
.dark-theme a[data-target],
.dark-theme #about-nav-link,
.dark-theme #support-nav-link,
.dark-theme #services-nav-link,
.dark-theme .navbar-dropdown .navbar-link,
.dark-theme .navbar-dropdown .dropdown-toggle {
    color: var(--color-text-dark, #f8f9fa) !important;
}

/* ===== ACTIVE STATE STYLING ===== */





/* ===== HOVER EFFECTS (MINIMAL) ===== */

/* Subtle hover effect - only color change */
.navbar-link:hover:not(.active),
.dropdown-item:hover:not(.active),
a[data-target]:hover:not(.active),
#about-nav-link:hover:not(.active),
#support-nav-link:hover:not(.active),
#services-nav-link:hover:not(.active),
.navbar-dropdown .navbar-link:hover:not(.active),
.navbar-dropdown .dropdown-toggle:hover:not(.active) {
    color: #1976d2 !important;

    /* Ensure no other effects */
    transition: none !important;
    animation: none !important;
    transform: none !important;
    box-shadow: none !important;
    text-shadow: none !important;
    background-color: transparent !important;
    border: none !important;
    border-bottom: none !important;
}

/* Dark theme hover */
.dark-theme .navbar-link:hover:not(.active),
.dark-theme .dropdown-item:hover:not(.active),
.dark-theme a[data-target]:hover:not(.active),
.dark-theme #about-nav-link:hover:not(.active),
.dark-theme #support-nav-link:hover:not(.active),
.dark-theme #services-nav-link:hover:not(.active),
.dark-theme .navbar-dropdown .navbar-link:hover:not(.active),
.dark-theme .navbar-dropdown .dropdown-toggle:hover:not(.active) {
    color: #64b5f6 !important;
}

/* ===== PSEUDO-ELEMENT CLEANUP ===== */

/* Remove all pseudo-elements that might cause flickering */
.navbar-link::before,
.navbar-link::after,
.dropdown-item::before,
.dropdown-item::after,
a[data-target]::before,
a[data-target]::after,
#about-nav-link::before,
#about-nav-link::after,
#support-nav-link::before,
#support-nav-link::after,
#services-nav-link::before,
#services-nav-link::after,
.navbar-dropdown .navbar-link::before,
.navbar-dropdown .navbar-link::after,
.navbar-dropdown .dropdown-toggle::before,
.navbar-dropdown .dropdown-toggle::after {
    display: none !important;
    content: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
    width: 0 !important;
    height: 0 !important;
    background: transparent !important;
    border: none !important;
    transition: none !important;
    animation: none !important;
}

/* ===== DROPDOWN SPECIFIC STYLES ===== */

/* Ensure dropdown functionality works */
.navbar-dropdown:hover .dropdown-menu,
.dropdown-toggle:hover + .dropdown-menu,
.dropdown-menu:hover {
    display: block !important;
}

/* Dropdown menu items */
.dropdown-menu {
    transition: none !important;
    animation: none !important;
}

.dropdown-menu .dropdown-item {
    padding: 8px 16px !important;
    margin: 0 !important;
    border-radius: 0 !important;
    background-color: transparent !important;
}

.dropdown-menu .dropdown-item:hover {
    background-color: rgba(25, 118, 210, 0.1) !important;
    color: #1976d2 !important;
}

.dark-theme .dropdown-menu .dropdown-item:hover {
    background-color: rgba(100, 181, 246, 0.1) !important;
    color: #64b5f6 !important;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */

/* Use GPU acceleration for smooth rendering */
.navbar-link,
.dropdown-item,
a[data-target],
#about-nav-link,
#support-nav-link,
#services-nav-link {
    will-change: auto !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
}

/* Prevent layout thrashing */
.navbar-link.active,
.dropdown-item.active,
a[data-target].active,
#about-nav-link.active,
#support-nav-link.active,
#services-nav-link.active {
    contain: layout style !important;
}

/* ===== MOBILE RESPONSIVENESS ===== */

@media (max-width: 768px) {
    .navbar-link,
    .dropdown-item,
    a[data-target],
    #about-nav-link,
    #support-nav-link,
    #services-nav-link {
        padding: 12px 16px !important;
        margin: 2px 0 !important;
        display: block !important;
        text-align: center !important;
    }

    .navbar-link.active,
    .dropdown-item.active,
    a[data-target].active,
    #about-nav-link.active,
    #support-nav-link.active,
    #services-nav-link.active {
        border-bottom: none !important;
        border-left: 3px solid #1976d2 !important;
    }

    .dark-theme .navbar-link.active,
    .dark-theme .dropdown-item.active,
    .dark-theme a[data-target].active,
    .dark-theme #about-nav-link.active,
    .dark-theme #support-nav-link.active,
    .dark-theme #services-nav-link.active {
        border-left-color: #64b5f6 !important;
    }
}

/* ===== ACCESSIBILITY ===== */

/* Focus states for keyboard navigation */
.navbar-link:focus,
.dropdown-item:focus,
a[data-target]:focus,
#about-nav-link:focus,
#support-nav-link:focus,
#services-nav-link:focus {
    outline: 2px solid #1976d2 !important;
    outline-offset: 2px !important;
    color: #1976d2 !important;
}

.dark-theme .navbar-link:focus,
.dark-theme .dropdown-item:focus,
.dark-theme a[data-target]:focus,
.dark-theme #about-nav-link:focus,
.dark-theme #support-nav-link:focus,
.dark-theme #services-nav-link:focus {
    outline-color: #64b5f6 !important;
    color: #64b5f6 !important;
}

/* ===== OVERRIDE CONFLICTING STYLES ===== */

/* Force override any conflicting styles from other CSS files */
html body .navbar-link,
html body .dropdown-item,
html body a[data-target],
html body #about-nav-link,
html body #support-nav-link,
html body #services-nav-link {
    transition: none !important;
    animation: none !important;
}

/* Ensure active states always work */
html body .navbar-link.active,
html body .dropdown-item.active,
html body a[data-target].active,
html body #about-nav-link.active,
html body #support-nav-link.active,
html body #services-nav-link.active {
    color: #1976d2 !important;
    font-weight: 600 !important;
    border-bottom: 2px solid #1976d2 !important;
}

html body .dark-theme .navbar-link.active,
html body .dark-theme .dropdown-item.active,
html body .dark-theme a[data-target].active,
html body .dark-theme #about-nav-link.active,
html body .dark-theme #support-nav-link.active,
html body .dark-theme #services-nav-link.active {
    color: #64b5f6 !important;
    border-bottom-color: #64b5f6 !important;
}
