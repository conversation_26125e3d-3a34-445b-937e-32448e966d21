/**
 * Search Box and Button Alignment Fix
 * Ensures proper alignment and consistent sizing between search input and button
 */

/* Search container styling */
.search-wrapper, .faq-search-wrapper {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    height: 44px; /* Reduced height for container */
}

/* Search input styling */
#faqSearch, .faq-search {
    width: 100%;
    height: 44px; /* Reduced height for input */
    padding: 0 120px 0 40px; /* Increased right padding for button */
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    font-size: 1rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    box-sizing: border-box;
}

/* Search icon positioning */
.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    z-index: 1;
    pointer-events: none;
}

/* Search button styling */
.search-button {
    position: absolute !important;
    right: 8px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    height: 36px !important; /* Fixed height for button */
    width: auto !important;
    min-width: 80px !important;
    background-color: #4299e1 !important;
    color: white !important;
    border: none !important;
    border-radius: 6px !important;
    padding: 0 15px !important;
    font-weight: 500 !important;
    font-size: 0.95rem !important;
    line-height: 36px !important;
    cursor: pointer !important;
    z-index: 2 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* Clear button positioning */
.clear-button, .search-clear {
    position: absolute;
    right: 100px; /* Adjusted to account for search button width */
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    font-size: 14px;
}

/* Dark mode adjustments */
.dark-theme #faqSearch, .dark-theme .faq-search {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

.dark-theme .search-button {
    background-color: #4f46e5 !important;
}

.dark-theme .search-button:hover {
    background-color: #4338ca !important;
}

/* Hover effects */
.search-button:hover {
    background-color: #3182ce !important;
}

/* Responsive adjustments */
@media (max-width: 480px) {
    .search-wrapper, .faq-search-wrapper {
        height: 40px;
    }

    #faqSearch, .faq-search {
        height: 40px;
        font-size: 0.9rem;
        padding-right: 100px;
    }

    .search-button {
        height: 32px !important;
        min-width: 70px !important;
        padding: 0 10px !important;
        font-size: 0.85rem !important;
        line-height: 32px !important;
    }

    .clear-button, .search-clear {
        right: 80px;
    }
}
