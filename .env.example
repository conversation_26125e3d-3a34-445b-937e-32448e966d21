# Ziantrix Dynamic App - Environment Variables Template
# Copy this file to .env and update with your actual values

# ===== FLASK CONFIGURATION =====
SECRET_KEY=your_very_secure_secret_key_minimum_32_characters_change_in_production
FLASK_ENV=production
FLASK_DEBUG=False

# ===== DATABASE CONFIGURATION =====
# For Railway PostgreSQL (recommended for production)
DATABASE_URL=${{ Postgres.DATABASE_URL }}

# For local development (SQLite)
# DATABASE_URL=sqlite:///ziantrix_app.db

# For custom PostgreSQL
# DATABASE_URL=postgresql://username:password@hostname:port/database_name

# ===== EMAIL CONFIGURATION =====
# Update these with your actual email service credentials
EMAIL_SENDER=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_RECIPIENT=<EMAIL>
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=465

# ===== CALENDAR INTEGRATION =====
# Your Calendly or Google Calendar booking link
CALENDAR_LINK=https://calendar.app.google/uy4Szgfn4mdyZPLA6
CALENDLY_LINK=https://calendly.com/vijay-kodam98/demo-call-with-ziantrix

# ===== API KEYS (Optional) =====
# Add these if you're using external AI services
GROQ_API_KEY=your_groq_api_key_here
HF_TOKEN=your_huggingface_token_here

# ===== ADMIN CONFIGURATION (Optional) =====
# These are used if you re-enable authentication in the future
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure_admin_password_123

# ===== PRODUCTION SETTINGS =====
# Port (Railway sets this automatically)
PORT=5000

# Session settings
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
