/**
 * Modern Feature Cards
 * Elegant, professional feature cards with hover effects
 */

/* Features Section */
.features-section {
  padding: var(--spacing-20) 0;
  position: relative;
  overflow: hidden;
}

.features-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}

.features-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-bg-secondary);
  opacity: 0.7;
}

.dark-theme .features-bg::before {
  background-color: var(--color-bg-secondary);
  opacity: 0.5;
}

/* Section Header */
.section-header {
  text-align: center;
  max-width: 800px;
  margin: 0 auto var(--spacing-16) auto;
}

.section-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-extrabold);
  margin-bottom: var(--spacing-4);
  color: var(--color-text-primary);
  position: relative;
  display: inline-block;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--color-primary-600), var(--color-accent-600));
  border-radius: var(--radius-full);
}

.dark-theme .section-title::after {
  background: linear-gradient(to right, var(--color-primary-500), var(--color-accent-500));
}

.section-description {
  font-size: var(--text-lg);
  color: var(--color-text-secondary);
  max-width: 700px;
  margin: var(--spacing-6) auto 0;
  line-height: var(--leading-relaxed);
}

/* Features Grid */
.features-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-6);
  max-width: var(--container-xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
  margin-bottom: 30px;
}

/* Feature Card */
.feature-card {
  background-color: transparent;
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-md);
  transition: var(--transition-all);
  position: relative;
  overflow: visible;
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 280px;
  width: 100%;
  border: 1px solid var(--color-border);
  z-index: 1;
  isolation: isolate;
  -webkit-transform-style: preserve-3d;
  transform-style: preserve-3d;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-200);
  background-color: transparent;
}

.feature-card .feature-title,
.feature-card .feature-description,
.feature-card .feature-link {
  background-color: transparent !important;
}

/* Metrics styling in light mode */
.feature-card .feature-metric,
.feature-card .feature-metric-label,
.feature-card .metric-wrapper {
  background-color: transparent !important;
  background: transparent !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 10 !important;
}

.feature-card .feature-metric {
  color: #1976d2 !important;
}

.feature-card .feature-metric-label {
  color: #666 !important;
}

.dark-theme .feature-card {
  background-color: transparent;
  border-color: var(--color-border);
}

.dark-theme .feature-card .feature-title,
.dark-theme .feature-card .feature-description,
.dark-theme .feature-card .feature-link {
  background-color: transparent !important;
}

/* Metrics styling in dark mode */
.dark-theme .feature-card .feature-metric,
.dark-theme .feature-card .feature-metric-label,
.dark-theme .feature-card .metric-wrapper {
  background-color: transparent !important;
  background: transparent !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 10 !important;
}

.dark-theme .feature-card .feature-metric {
  color: #64b5f6 !important;
  text-shadow: 0 0 10px rgba(100, 181, 246, 0.4) !important;
}

.dark-theme .feature-card .feature-metric-label {
  color: #aaa !important;
}

.dark-theme .feature-card:hover {
  border-color: var(--color-primary-700);
  background-color: transparent;
}

/* Card Background Gradient */
.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(to right, var(--color-primary-600), var(--color-accent-600));
  transition: var(--transition-all);
  z-index: 1;
  pointer-events: none;
}

.feature-card:hover::before {
  height: 6px;
}

.dark-theme .feature-card::before {
  background: linear-gradient(to right, var(--color-primary-500), var(--color-accent-500));
}

/* Feature Icon */
.feature-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--color-primary-100), var(--color-primary-50));
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-4);
  transition: var(--transition-all);
  position: relative;
  z-index: 2;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1);
  background: linear-gradient(135deg, var(--color-primary-200), var(--color-primary-100));
}

.dark-theme .feature-icon {
  background: linear-gradient(135deg, var(--color-primary-900), var(--color-primary-800));
}

.dark-theme .feature-card:hover .feature-icon {
  background: linear-gradient(135deg, var(--color-primary-800), var(--color-primary-700));
}

.feature-icon i {
  font-size: 24px;
  color: var(--color-primary-600);
  transition: var(--transition-all);
}

.feature-card:hover .feature-icon i {
  color: var(--color-primary-700);
}

.dark-theme .feature-icon i {
  color: var(--color-primary-400);
}

.dark-theme .feature-card:hover .feature-icon i {
  color: var(--color-primary-300);
}

/* Feature Content */
.feature-title {
  font-size: var(--text-xl);
  font-weight: var(--font-bold);
  margin-bottom: var(--spacing-3);
  color: var(--color-text-primary);
  transition: var(--transition-colors);
  position: relative;
  z-index: 2;
}

.feature-card:hover .feature-title {
  color: var(--color-primary-600);
}

.dark-theme .feature-card:hover .feature-title {
  color: var(--color-primary-400);
}

.feature-description {
  color: var(--color-text-secondary);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--spacing-4);
  flex-grow: 1;
  position: relative;
  z-index: 2;
}

.feature-card:hover .feature-description {
  color: var(--color-text-secondary);
}

.dark-theme .feature-card:hover .feature-description {
  color: var(--color-text-secondary);
}

/* Feature Link */
.feature-link {
  display: inline-flex;
  align-items: center;
  color: var(--color-primary-600);
  font-weight: var(--font-medium);
  margin-top: auto;
  transition: var(--transition-all);
  position: relative;
  z-index: 2;
}

.feature-link i {
  margin-left: var(--spacing-2);
  transition: var(--transition-transform);
}

.feature-card:hover .feature-link {
  color: var(--color-primary-700);
}

.feature-card:hover .feature-link i {
  transform: translateX(4px);
}

.dark-theme .feature-link {
  color: var(--color-primary-400);
}

.dark-theme .feature-card:hover .feature-link {
  color: var(--color-primary-300);
}

/* Responsive Styles */
@media (max-width: 1023px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 767px) {
  .features-grid {
    grid-template-columns: 1fr;
  }

  .section-title {
    font-size: var(--text-3xl);
  }

  .section-description {
    font-size: var(--text-base);
  }

  .feature-card {
    padding: var(--spacing-6);
    min-height: 250px;
    margin-bottom: 20px;
  }

  .feature-icon {
    width: 50px;
    height: 50px;
  }

  .feature-icon i {
    font-size: 20px;
  }

  .feature-title {
    font-size: var(--text-lg);
  }
}
