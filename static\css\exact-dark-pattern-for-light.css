/**
 * EXACT DARK MODE PATTERN FOR LIGHT MODE
 * This applies the EXACT SAME complex pattern from dark mode to light mode
 * with appropriate light mode colors
 */

/* OVERRIDE ALL OTHER LIGHT MODE PATTERNS - HIGHEST PRIORITY */
html:not(.dark-theme),
html:not(.dark-theme) body,
body:not(.dark-theme),
:root:not(.dark-theme),
:root:not(.dark-theme) body {
    background-color: #ffffff !important;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(25, 118, 210, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(25, 118, 210, 0.08) 0%, transparent 50%),
        linear-gradient(to bottom right, rgba(25, 118, 210, 0.05) 0%, transparent 70%),
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='400' viewBox='0 0 800 800'%3E%3Cg fill='none' stroke='%231976d2' stroke-opacity='0.08' stroke-width='1'%3E%3Cpath d='M769 229L1037 260.9M927 880L731 737 520 660 309 538 40 599 295 764 126.5 879.5 40 599-197 493 102 382-31 229 126.5 79.5-69-63'/%3E%3Cpath d='M-31 229L237 261 390 382 603 493 308.5 537.5 101.5 381.5M370 905L295 764'/%3E%3Cpath d='M520 660L578 842 731 737 840 599 603 493 520 660 295 764 309 538 390 382 539 269 769 229 577.5 41.5 370 105 295 -36 126.5 79.5 237 261 102 382 40 599 -69 737 127 880'/%3E%3Cpath d='M520-140L578.5 42.5 731-63M603 493L539 269 237 261 370 105M902 382L539 269M390 382L102 382'/%3E%3Cpath d='M-222 42L126.5 79.5 370 105 539 269 577.5 41.5 927 80 769 229 902 382 603 493 731 737M295-36L577.5 41.5M578 842L295 764M40-201L127 80M102 382L-261 269'/%3E%3C/g%3E%3Cg fill='%232196f3' fill-opacity='0.06'%3E%3Ccircle cx='769' cy='229' r='8'/%3E%3Ccircle cx='539' cy='269' r='8'/%3E%3Ccircle cx='603' cy='493' r='8'/%3E%3Ccircle cx='731' cy='737' r='8'/%3E%3Ccircle cx='520' cy='660' r='8'/%3E%3Ccircle cx='309' cy='538' r='8'/%3E%3Ccircle cx='295' cy='764' r='8'/%3E%3Ccircle cx='40' cy='599' r='8'/%3E%3Ccircle cx='102' cy='382' r='8'/%3E%3Ccircle cx='127' cy='80' r='8'/%3E%3Ccircle cx='370' cy='105' r='8'/%3E%3Ccircle cx='578' cy='42' r='8'/%3E%3Ccircle cx='237' cy='261' r='8'/%3E%3Ccircle cx='390' cy='382' r='8'/%3E%3C/g%3E%3C/svg%3E") !important;
    background-attachment: fixed !important;
    background-size: cover !important;
}

/* DISABLE ALL OTHER LIGHT MODE BACKGROUND PATTERNS */
body:not(.dark-theme) {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(25, 118, 210, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(25, 118, 210, 0.08) 0%, transparent 50%),
        linear-gradient(to bottom right, rgba(25, 118, 210, 0.05) 0%, transparent 70%),
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='400' viewBox='0 0 800 800'%3E%3Cg fill='none' stroke='%231976d2' stroke-opacity='0.08' stroke-width='1'%3E%3Cpath d='M769 229L1037 260.9M927 880L731 737 520 660 309 538 40 599 295 764 126.5 879.5 40 599-197 493 102 382-31 229 126.5 79.5-69-63'/%3E%3Cpath d='M-31 229L237 261 390 382 603 493 308.5 537.5 101.5 381.5M370 905L295 764'/%3E%3Cpath d='M520 660L578 842 731 737 840 599 603 493 520 660 295 764 309 538 390 382 539 269 769 229 577.5 41.5 370 105 295 -36 126.5 79.5 237 261 102 382 40 599 -69 737 127 880'/%3E%3Cpath d='M520-140L578.5 42.5 731-63M603 493L539 269 237 261 370 105M902 382L539 269M390 382L102 382'/%3E%3Cpath d='M-222 42L126.5 79.5 370 105 539 269 577.5 41.5 927 80 769 229 902 382 603 493 731 737M295-36L577.5 41.5M578 842L295 764M40-201L127 80M102 382L-261 269'/%3E%3C/g%3E%3Cg fill='%232196f3' fill-opacity='0.06'%3E%3Ccircle cx='769' cy='229' r='8'/%3E%3Ccircle cx='539' cy='269' r='8'/%3E%3Ccircle cx='603' cy='493' r='8'/%3E%3Ccircle cx='731' cy='737' r='8'/%3E%3Ccircle cx='520' cy='660' r='8'/%3E%3Ccircle cx='309' cy='538' r='8'/%3E%3Ccircle cx='295' cy='764' r='8'/%3E%3Ccircle cx='40' cy='599' r='8'/%3E%3Ccircle cx='102' cy='382' r='8'/%3E%3Ccircle cx='127' cy='80' r='8'/%3E%3Ccircle cx='370' cy='105' r='8'/%3E%3Ccircle cx='578' cy='42' r='8'/%3E%3Ccircle cx='237' cy='261' r='8'/%3E%3Ccircle cx='390' cy='382' r='8'/%3E%3C/g%3E%3C/svg%3E") !important;
    background-attachment: fixed !important;
    background-size: cover !important;
}

/* LIGHT MODE: Apply exact same animated overlay as dark mode */
body:not(.dark-theme)::before {
    content: '' !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none !important;
    background:
        radial-gradient(circle at 10% 10%, rgba(25, 118, 210, 0.05) 0%, transparent 30%),
        radial-gradient(circle at 90% 90%, rgba(25, 118, 210, 0.05) 0%, transparent 30%) !important;
    z-index: -1 !important;
    animation: pulseBackground 10s ease-in-out infinite alternate !important;
}

/* Add the pulse animation keyframe for light mode */
@keyframes pulseBackground {
    0% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* DARK MODE: Keep existing dark mode pattern */
html.dark-theme,
html.dark-theme body,
body.dark-theme,
:root.dark-theme,
:root.dark-theme body {
    background-color: #121212 !important;
    background-image:
        radial-gradient(circle at 25% 25%, rgba(100, 181, 246, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(100, 181, 246, 0.15) 0%, transparent 50%),
        linear-gradient(to bottom right, rgba(100, 181, 246, 0.1) 0%, transparent 70%),
        url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='400' height='400' viewBox='0 0 800 800'%3E%3Cg fill='none' stroke='%2364b5f6' stroke-opacity='0.12' stroke-width='1'%3E%3Cpath d='M769 229L1037 260.9M927 880L731 737 520 660 309 538 40 599 295 764 126.5 879.5 40 599-197 493 102 382-31 229 126.5 79.5-69-63'/%3E%3Cpath d='M-31 229L237 261 390 382 603 493 308.5 537.5 101.5 381.5M370 905L295 764'/%3E%3Cpath d='M520 660L578 842 731 737 840 599 603 493 520 660 295 764 309 538 390 382 539 269 769 229 577.5 41.5 370 105 295 -36 126.5 79.5 237 261 102 382 40 599 -69 737 127 880'/%3E%3Cpath d='M520-140L578.5 42.5 731-63M603 493L539 269 237 261 370 105M902 382L539 269M390 382L102 382'/%3E%3Cpath d='M-222 42L126.5 79.5 370 105 539 269 577.5 41.5 927 80 769 229 902 382 603 493 731 737M295-36L577.5 41.5M578 842L295 764M40-201L127 80M102 382L-261 269'/%3E%3C/g%3E%3Cg fill='%2390caf9' fill-opacity='0.15'%3E%3Ccircle cx='769' cy='229' r='8'/%3E%3Ccircle cx='539' cy='269' r='8'/%3E%3Ccircle cx='603' cy='493' r='8'/%3E%3Ccircle cx='731' cy='737' r='8'/%3E%3Ccircle cx='520' cy='660' r='8'/%3E%3Ccircle cx='309' cy='538' r='8'/%3E%3Ccircle cx='295' cy='764' r='8'/%3E%3Ccircle cx='40' cy='599' r='8'/%3E%3Ccircle cx='102' cy='382' r='8'/%3E%3Ccircle cx='127' cy='80' r='8'/%3E%3Ccircle cx='370' cy='105' r='8'/%3E%3Ccircle cx='578' cy='42' r='8'/%3E%3Ccircle cx='237' cy='261' r='8'/%3E%3Ccircle cx='390' cy='382' r='8'/%3E%3C/g%3E%3C/svg%3E") !important;
    background-attachment: fixed !important;
    background-size: cover !important;
}

.dark-theme body::before {
    content: '' !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    pointer-events: none !important;
    background:
        radial-gradient(circle at 10% 10%, rgba(25, 118, 210, 0.1) 0%, transparent 30%),
        radial-gradient(circle at 90% 90%, rgba(25, 118, 210, 0.1) 0%, transparent 30%) !important;
    z-index: -1 !important;
    animation: pulseBackground 10s ease-in-out infinite alternate !important;
}

/* ENSURE SECTIONS ARE TRANSPARENT TO SHOW BACKGROUND PATTERN */
body:not(.dark-theme) .hero-section,
body:not(.dark-theme) .about-section,
body:not(.dark-theme) .features-section,
body:not(.dark-theme) .solutions-section,
body:not(.dark-theme) .services-section,
body:not(.dark-theme) .faq-section,
body:not(.dark-theme) .contact-section,
body:not(.dark-theme) .cta-section,
body:not(.dark-theme) main,
body:not(.dark-theme) .main-content,
body:not(.dark-theme) .container,
body:not(.dark-theme) .container-fluid,
body:not(.dark-theme) .wrapper,
body:not(.dark-theme) section,
body:not(.dark-theme) header {
    background-color: transparent !important;
    background-image: none !important;
    background: none !important;
}

/* NAVBAR STYLING */
body:not(.dark-theme) .navbar,
body:not(.dark-theme) .navbar-sticky {
    background-color: rgba(248, 249, 251, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
}

/* CARD BACKGROUNDS */
body:not(.dark-theme) .feature-card,
body:not(.dark-theme) .service-card,
body:not(.dark-theme) .solution-card,
body:not(.dark-theme) .testimonial-card,
body:not(.dark-theme) .faq-item {
    background-color: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(5px) !important;
    -webkit-backdrop-filter: blur(5px) !important;
    background-image: none !important;
}

/* FOOTER BACKGROUND */
body:not(.dark-theme) footer {
    background-color: #f0f2f5 !important;
    background-image: none !important;
}

/* OVERRIDE CSS VARIABLES */
:root:not(.dark-theme) {
    --color-bg-primary: #ffffff !important;
    --color-bg-secondary: #ffffff !important;
    --color-background: #ffffff !important;
    --color-background-alt: #ffffff !important;
    --color-bg-light: #ffffff !important;
    --color-bg-dark: #ffffff !important;
}


