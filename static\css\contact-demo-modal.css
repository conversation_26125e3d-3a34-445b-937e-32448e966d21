/**
 * Contact Demo Modal Styles
 * Styles for the contact form modal triggered by "Contact Us for Demo" button
 */

/* Contact Demo Modal */
.contact-demo-modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    animation: fadeIn 0.3s ease-out;
}

.contact-demo-modal.active {
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.contact-demo-modal-content {
    background-color: var(--color-background, #ffffff);
    margin: auto;
    padding: 2rem;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    position: relative;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    animation: slideInUp 0.3s ease-out;
    max-height: 90vh;
    overflow-y: auto;
}

.dark-theme .contact-demo-modal-content {
    background-color: var(--color-background-dark, #1a202c);
    border: 1px solid rgba(100, 181, 246, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5), 0 0 20px rgba(100, 181, 246, 0.2);
}

.contact-demo-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--color-text, #333);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.contact-demo-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
    transform: rotate(90deg);
}

.dark-theme .contact-demo-close {
    color: var(--color-text-dark, #f8fafc);
}

.dark-theme .contact-demo-close:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.contact-demo-modal h2 {
    margin-bottom: 1.5rem;
    color: var(--color-text, #333);
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
}

.dark-theme .contact-demo-modal h2 {
    color: var(--color-text-dark, #f8fafc);
}

.contact-demo-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-demo-form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.contact-demo-form-group label {
    font-weight: 500;
    color: var(--color-text, #333);
    font-size: 0.9rem;
}

.dark-theme .contact-demo-form-group label {
    color: var(--color-text-dark, #f8fafc);
}

.contact-demo-form-group input,
.contact-demo-form-group textarea,
.contact-demo-form-group select {
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: var(--color-background, #ffffff);
    color: var(--color-text, #333);
}

.contact-demo-form-group input:focus,
.contact-demo-form-group textarea:focus,
.contact-demo-form-group select:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.dark-theme .contact-demo-form-group input,
.dark-theme .contact-demo-form-group textarea,
.dark-theme .contact-demo-form-group select {
    background-color: var(--color-background-alt-dark, #2d3748);
    border-color: #4a5568;
    color: var(--color-text-dark, #f8fafc);
}

.dark-theme .contact-demo-form-group input:focus,
.dark-theme .contact-demo-form-group textarea:focus,
.dark-theme .contact-demo-form-group select:focus {
    border-color: #818cf8;
    box-shadow: 0 0 0 3px rgba(129, 140, 248, 0.1);
}

.contact-demo-form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.contact-demo-submit-btn {
    background: linear-gradient(135deg, #7c3aed, #5b21b6);
    color: white;
    border: none;
    padding: 14px 20px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
    box-shadow: 0 4px 14px rgba(124, 58, 237, 0.4);
}

.contact-demo-submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(124, 58, 237, 0.5);
}

.contact-demo-submit-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Loading state */
.contact-demo-submit-btn.loading {
    position: relative;
    color: transparent;
}

.contact-demo-submit-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error messages */
.contact-demo-error {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    display: none;
}

.contact-demo-form-group.error input,
.contact-demo-form-group.error textarea,
.contact-demo-form-group.error select {
    border-color: #ef4444;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.contact-demo-form-group.error .contact-demo-error {
    display: block;
}

/* Success message */
.contact-demo-success {
    background-color: #10b981;
    color: white;
    padding: 1rem;
    border-radius: 8px;
    text-align: center;
    margin-bottom: 1rem;
    display: none;
}

.contact-demo-success.show {
    display: block;
}

/* Responsive design */
@media (max-width: 768px) {
    .contact-demo-modal-content {
        padding: 1.5rem;
        margin: 1rem;
        width: calc(100% - 2rem);
    }
    
    .contact-demo-modal h2 {
        font-size: 1.25rem;
    }
    
    .contact-demo-form-group input,
    .contact-demo-form-group textarea,
    .contact-demo-form-group select {
        padding: 10px;
        font-size: 0.95rem;
    }
}

@media (max-width: 480px) {
    .contact-demo-modal-content {
        padding: 1rem;
        margin: 0.5rem;
        width: calc(100% - 1rem);
    }
}
