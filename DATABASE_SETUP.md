# Ziantrix Dynamic App - Database Setup Guide

This guide explains how to set up and configure PostgreSQL for your Ziantrix Dynamic App.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Set Up PostgreSQL Database

#### Option A: Using Railway (Recommended for Production)

1. Create a Railway account at [railway.app](https://railway.app)
2. Create a new project
3. Add a PostgreSQL service
4. Railway will automatically provide a `DATABASE_URL` environment variable
5. In your service settings, add a new variable:
   - **Variable Name**: `DATABASE_URL`
   - **Variable Value**: `${{ Postgres.DATABASE_URL }}`

#### Option B: Local PostgreSQL

1. Install PostgreSQL on your system
2. Create a database:
   ```sql
   CREATE DATABASE ziantrix_app;
   CREATE USER ziantrix_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE ziantrix_app TO ziantrix_user;
   ```
3. Set the DATABASE_URL in your `.env` file:
   ```
   DATABASE_URL=postgresql://ziantrix_user:your_password@localhost:5432/ziantrix_app
   ```

#### Option C: SQLite (Development Only)

For local development, you can use SQLite:
```
DATABASE_URL=sqlite:///ziantrix_app.db
```

### 3. Initialize Database

```bash
python init_db.py --init
```

This will:
- Create all necessary tables
- Migrate existing JSON data (if any)
- Create a sample admin user

## 📊 Database Schema

### Users Table
- **id**: Primary key
- **email**: Unique user email
- **password_hash**: Bcrypt hashed password
- **name**: User's full name
- **company**: User's company (optional)
- **phone**: User's phone number (optional)
- **is_active**: Account status
- **is_admin**: Admin privileges
- **created_at**: Registration timestamp
- **updated_at**: Last update timestamp
- **last_login**: Last login timestamp

### Form Submissions Table
- **id**: Primary key
- **user_id**: Foreign key to users (nullable for anonymous)
- **form_type**: Type of form (contact, demo, etc.)
- **name**: Submitter's name
- **email**: Submitter's email
- **company**: Company name (optional)
- **phone**: Phone number (optional)
- **message**: Form message
- **inquiry_type**: Type of inquiry
- **source_page**: Page where form was submitted
- **ip_address**: Submitter's IP
- **user_agent**: Browser information
- **additional_data**: JSON field for extra data
- **status**: Processing status
- **created_at**: Submission timestamp
- **processed_at**: Processing timestamp

### Chat Conversations Table
- **id**: Primary key
- **user_id**: Foreign key to users (nullable for anonymous)
- **session_id**: Unique session identifier
- **device_id**: Device identifier
- **created_at**: Conversation start time
- **updated_at**: Last message time

### Chat Messages Table
- **id**: Primary key
- **conversation_id**: Foreign key to conversations
- **message_type**: 'user' or 'bot'
- **content**: Message content
- **metadata**: JSON field for additional data
- **created_at**: Message timestamp

### Analytics Table
- **id**: Primary key
- **event_type**: Type of event tracked
- **event_data**: JSON field for event details
- **user_id**: Foreign key to users (optional)
- **session_id**: Session identifier
- **ip_address**: User's IP
- **user_agent**: Browser information
- **page_url**: Page URL
- **referrer**: Referrer URL
- **created_at**: Event timestamp

## 🛠️ Database Management Commands

### Initialize Database
```bash
python init_db.py --init
```

### Check Database Status
```bash
python init_db.py --status
```

### Reset Database (⚠️ Deletes all data)
```bash
python init_db.py --reset
```

### Migrate JSON Data Only
```bash
python init_db.py --migrate
```

## 🔧 Configuration

### Environment Variables

Add these to your `.env` file or environment:

```bash
# Database URL (choose one)
DATABASE_URL=postgresql://username:password@hostname:port/database_name
# OR for Railway/Heroku
DATABASE_URL=${{ Postgres.DATABASE_URL }}
# OR for local development
DATABASE_URL=sqlite:///ziantrix_app.db

# Admin user (for initial setup)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Flask configuration
SECRET_KEY=your_secure_secret_key_change_in_production
FLASK_ENV=production
FLASK_DEBUG=False
```

### Railway Deployment

1. Connect your GitHub repository to Railway
2. Add a PostgreSQL service to your project
3. Set the environment variable:
   - **Name**: `DATABASE_URL`
   - **Value**: `${{ Postgres.DATABASE_URL }}`
4. Deploy your application

## 🔄 Data Migration

The app automatically migrates existing JSON data on first startup:

1. **users.json** → **users** table
2. **user_profiles.json** → **users** table (merged)

Original files are backed up with `.backup` extension.

## 🔐 Security Features

- **Password Hashing**: Uses bcrypt for secure password storage
- **SQL Injection Protection**: SQLAlchemy ORM prevents SQL injection
- **Session Management**: Secure session handling
- **Input Validation**: Server-side validation for all inputs
- **HTTPS Ready**: Configured for secure connections

## 📈 Performance Optimization

- **Connection Pooling**: Automatic connection pool management
- **Indexes**: Optimized database indexes for fast queries
- **Query Optimization**: Efficient database queries
- **Caching**: Session-based caching where appropriate

## 🐛 Troubleshooting

### Database Connection Issues

1. **Check DATABASE_URL format**:
   ```bash
   # Correct format
   postgresql://username:password@hostname:port/database_name
   ```

2. **Test connection**:
   ```bash
   python init_db.py --status
   ```

3. **Check logs**:
   ```bash
   python app.py
   # Look for database connection messages
   ```

### Migration Issues

1. **Manual migration**:
   ```bash
   python init_db.py --migrate
   ```

2. **Reset and reinitialize**:
   ```bash
   python init_db.py --reset
   python init_db.py --init
   ```

### Performance Issues

1. **Check connection pool settings** in `database.py`
2. **Monitor query performance** in application logs
3. **Consider database indexing** for large datasets

## 📞 Support

If you encounter issues:

1. Check the application logs
2. Verify environment variables
3. Test database connection
4. Review this documentation
5. Contact support with specific error messages

## 🔄 Backup and Recovery

### Backup
```bash
# PostgreSQL
pg_dump DATABASE_URL > backup.sql

# SQLite
cp ziantrix_app.db backup.db
```

### Restore
```bash
# PostgreSQL
psql DATABASE_URL < backup.sql

# SQLite
cp backup.db ziantrix_app.db
```

---

**Note**: Always test database changes in a development environment before applying to production.
