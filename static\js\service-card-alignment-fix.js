/**
 * Service Card Alignment Fix
 * This script ensures proper alignment of service cards and removes white boxes
 */

document.addEventListener('DOMContentLoaded', function() {
    // Apply fixes immediately and after a short delay to ensure they take effect
    applyServiceCardAlignmentFixes();
    setTimeout(applyServiceCardAlignmentFixes, 100);
    setTimeout(applyServiceCardAlignmentFixes, 500);
    
    // Also apply fixes when theme changes
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('change', function() {
            setTimeout(applyServiceCardAlignmentFixes, 100);
        });
    }
});

function applyServiceCardAlignmentFixes() {
    // Fix service feature icons and text alignment
    const featureItems = document.querySelectorAll('.service-features-list li');
    featureItems.forEach(function(item) {
        item.style.display = 'flex';
        item.style.flexDirection = 'row';
        item.style.alignItems = 'center';
        item.style.justifyContent = 'flex-start';
        item.style.paddingLeft = '24px';
        item.style.marginLeft = '0';
        item.style.textAlign = 'left';
        item.style.width = '100%';
        item.style.backgroundColor = 'transparent';
        item.style.background = 'transparent';
        item.style.boxShadow = 'none';
    });

    // Fix feature icons
    const featureIcons = document.querySelectorAll('.service-features-list .feature-icon');
    featureIcons.forEach(function(icon) {
        icon.style.display = 'flex';
        icon.style.alignItems = 'center';
        icon.style.justifyContent = 'center';
        icon.style.marginRight = '16px';
        icon.style.width = '28px';
        icon.style.height = '28px';
        icon.style.minWidth = '28px';
        icon.style.flexShrink = '0';
        icon.style.backgroundColor = 'transparent';
        icon.style.background = 'transparent';
        icon.style.boxShadow = 'none';
    });

    // Fix feature text
    const featureTexts = document.querySelectorAll('.service-features-list .feature-text');
    featureTexts.forEach(function(text) {
        text.style.display = 'inline-block';
        text.style.verticalAlign = 'middle';
        text.style.lineHeight = '1.4';
        text.style.textAlign = 'left';
        text.style.padding = '0';
        text.style.margin = '0';
        text.style.backgroundColor = 'transparent';
        text.style.background = 'transparent';
        text.style.boxShadow = 'none';
    });

    // Fix service cards
    const serviceCards = document.querySelectorAll('.service-card');
    serviceCards.forEach(function(card) {
        card.style.backgroundColor = 'transparent';
        card.style.background = 'transparent';
        card.style.boxShadow = 'none';
    });

    // Fix service card headers
    const serviceCardHeaders = document.querySelectorAll('.service-card-header');
    serviceCardHeaders.forEach(function(header) {
        header.style.backgroundColor = 'transparent';
        header.style.background = 'transparent';
        header.style.boxShadow = 'none';
    });

    // Fix service card bodies
    const serviceCardBodies = document.querySelectorAll('.service-card-body');
    serviceCardBodies.forEach(function(body) {
        body.style.backgroundColor = 'transparent';
        body.style.background = 'transparent';
        body.style.boxShadow = 'none';
    });

    // Fix service card footers
    const serviceCardFooters = document.querySelectorAll('.service-card-footer');
    serviceCardFooters.forEach(function(footer) {
        footer.style.backgroundColor = 'transparent';
        footer.style.background = 'transparent';
        footer.style.boxShadow = 'none';
    });

    // Remove all backgrounds from service card elements
    const serviceCardElements = document.querySelectorAll('.service-card *, .service-card-header *, .service-card-body *, .service-features-list *, .service-features-list li *, .service-card-footer *');
    serviceCardElements.forEach(function(element) {
        element.style.backgroundColor = 'transparent';
        element.style.background = 'transparent';
        element.style.boxShadow = 'none';
    });
}
