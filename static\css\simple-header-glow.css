/**
 * Flicker-Free Header Highlighting
 * Clean CSS for header highlighting without glow effects or transitions
 */

/* Base styles for headers */
.section-header-glow {
    position: relative;
    cursor: pointer;
    text-shadow: none !important;
    transition: none !important;
    animation: none !important;
}

/* Active state - DISABLED */
.section-header-glow.active {
    /* All active state styling disabled */
}

/* Dark mode active state - DISABLED */
.dark-theme .section-header-glow.active {
    /* All active state styling disabled */
}

/* Hover effect - DISABLED */
.section-header-glow:hover {
    /* All hover effects disabled */
}

/* Dark mode hover - DISABLED */
.dark-theme .section-header-glow:hover {
    /* All hover effects disabled */
}

/* Remove any pseudo-elements that might cause flickering */
.section-header-glow::before,
.section-header-glow::after {
    display: none !important;
    content: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* Performance optimizations */
.section-header-glow {
    will-change: auto !important;
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
}

/* Ensure consistent styling across all header levels */
h1.section-header-glow,
h2.section-header-glow,
h3.section-header-glow,
h4.section-header-glow,
h5.section-header-glow,
h6.section-header-glow {
    transition: none !important;
    animation: none !important;
}

h1.section-header-glow.active,
h2.section-header-glow.active,
h3.section-header-glow.active,
h4.section-header-glow.active,
h5.section-header-glow.active,
h6.section-header-glow.active {
    color: #1976d2 !important;
    font-weight: 700 !important;
}

.dark-theme h1.section-header-glow.active,
.dark-theme h2.section-header-glow.active,
.dark-theme h3.section-header-glow.active,
.dark-theme h4.section-header-glow.active,
.dark-theme h5.section-header-glow.active,
.dark-theme h6.section-header-glow.active {
    color: #64b5f6 !important;
}

/* Animation for click effect without glow */
@keyframes headerGlowClick {
    0% {
        text-shadow: none !important;
        transform: translateY(0);
    }
    50% {
        text-shadow: none !important;
        transform: translateY(0);
        color: #1565c0;
    }
    100% {
        text-shadow: none !important;
        transform: translateY(0);
        color: #1976d2;
    }
}

/* Dark mode click animation without glow */
@keyframes headerGlowClickDark {
    0% {
        text-shadow: none !important;
        transform: translateY(0);
    }
    50% {
        text-shadow: none !important;
        transform: translateY(0);
        color: #90caf9;
    }
    100% {
        text-shadow: none !important;
        transform: translateY(0);
        color: #64b5f6;
    }
}

/* Apply animations without glow - DISABLED */
.section-header-glow.animate-click {
    /* Animation disabled */
}

.dark-theme .section-header-glow.animate-click {
    /* Animation disabled */
}
