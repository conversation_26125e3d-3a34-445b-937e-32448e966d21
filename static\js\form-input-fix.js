/**
 * Form Input Fix
 * - Ensures form inputs are properly interactive
 * - Fixes issue with form inputs not being clickable until after submit button is clicked
 */

document.addEventListener('DOMContentLoaded', function() {
    // Fix for contact form inputs
    const contactForm = document.querySelector('.contact-form');
    const interactiveForm = document.querySelector('.interactive-form');

    if (contactForm) {
        // Get all form inputs
        const formInputs = contactForm.querySelectorAll('input, textarea');

        // Ensure inputs are interactive by setting higher z-index and position
        formInputs.forEach(input => {
            input.style.position = 'relative';
            input.style.zIndex = '100';

            // Add click event listener to ensure focus works
            input.addEventListener('click', function(e) {
                // Prevent any parent elements from capturing the click
                e.stopPropagation();

                // Focus the input
                this.focus();
            });

            // Add focus event listener
            input.addEventListener('focus', function(e) {
                // Ensure the input is visible and interactive
                this.style.position = 'relative';
                this.style.zIndex = '1000';
            });
        });

        // Add a click handler to the form itself to ensure inputs are clickable
        contactForm.addEventListener('click', function(e) {
            // Find the closest input or label to the click
            const target = e.target.closest('input, textarea, label');

            // If we clicked on a label or input, focus it
            if (target) {
                // If it's a label, find its associated input
                if (target.tagName === 'LABEL') {
                    const inputId = target.getAttribute('for');
                    if (inputId) {
                        const input = document.getElementById(inputId);
                        if (input) {
                            input.focus();
                        }
                    }
                } else {
                    // It's an input or textarea, focus it
                    target.focus();
                }

                // Prevent event bubbling
                e.stopPropagation();
            }
        });
    }

    // Special handling for interactive form
    if (interactiveForm) {
        const interactiveInputs = interactiveForm.querySelectorAll('.interactive-input');

        // Make sure interactive inputs are always clickable
        interactiveInputs.forEach(input => {
            // Force the input to be interactive
            input.style.position = 'relative';
            input.style.zIndex = '1001';
            input.style.pointerEvents = 'auto';

            // Add direct event listeners
            input.onclick = function(e) {
                e.stopPropagation();
                this.focus();
            };

            input.onfocus = function() {
                this.style.position = 'relative';
                this.style.zIndex = '1001';
            };
        });

        // Add direct event handler to the form
        interactiveForm.onclick = function(e) {
            const target = e.target;

            // If we clicked directly on the form (not on an input or label)
            if (target === this) {
                // Find the first input and focus it
                const firstInput = this.querySelector('.interactive-input');
                if (firstInput) {
                    firstInput.focus();
                }
            }
        };
    }

    // Add a global click handler to ensure form inputs are always clickable
    document.addEventListener('click', function(e) {
        // Check if we clicked on or inside a form input
        const formInput = e.target.closest('input, textarea');
        if (formInput) {
            // Make sure the input is focused
            formInput.focus();

            // Prevent event bubbling
            e.stopPropagation();
        }
    }, true); // Use capture phase to ensure this handler runs first
});
