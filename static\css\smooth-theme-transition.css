/**
 * Smooth Theme Transition
 * Provides seamless transitions between light and dark modes
 */

/* Base transition for all elements */
html {
  transition: color 0s, background-color 0s;
}

/* Apply transitions to all elements when transitioning */
.theme-transition-active,
.theme-transition-active * {
  transition: background-color 0.3s ease,
              color 0.3s ease,
              border-color 0.3s ease,
              box-shadow 0.3s ease,
              fill 0.3s ease,
              stroke 0.3s ease !important;
}

/* Preload both themes to prevent FOUC (Flash of Unstyled Content) */
:root {
  color-scheme: light dark;
}

/* Ensure smooth transitions for specific elements that might cause issues */
.navbar,
.hero-section,
.feature-card,
.service-card,
.testimonial-card,
.solution-card,
.faq-item,
.contact-form,
.footer {
  transition: inherit;
}

/* Fix for elements that might flicker during transition */
.feature-card .metric-wrapper,
.feature-card .feature-metric,
.feature-card .feature-metric-label {
  transition: background-color 0.3s ease, color 0.3s ease !important;
  will-change: background-color, color;
}

/* Ensure SVGs transition smoothly */
svg,
svg path,
svg rect,
svg circle,
svg ellipse,
svg line,
svg polyline,
svg polygon {
  transition: fill 0.3s ease, stroke 0.3s ease !important;
}

/* Prevent transition on page load */
.no-transition,
.no-transition * {
  transition: none !important;
}

/* Fix layout shifts during theme transition */
body, html {
  min-height: 100vh;
  overflow-x: hidden;
}

/* Prevent content jumps by maintaining consistent dimensions */
.feature-card,
.service-card,
.testimonial-card,
.solution-card,
.about-section,
.contact-section,
.faq-section {
  height: auto !important;
  min-height: auto !important;
  width: auto !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* Fix for image filters to ensure consistent sizing */
img {
  max-width: 100%;
  height: auto;
  transition: filter 0.3s ease !important;
}

/* Ensure consistent text sizing between modes */
h1, h2, h3, h4, h5, h6, p, span, a, button, input, textarea, select, label {
  transition: color 0.3s ease, text-shadow 0.3s ease, font-weight 0.3s ease !important;
  font-size: inherit !important;
  line-height: inherit !important;
}



/* Fix for feature metrics to prevent jumps */
.feature-metric {
  font-size: 2.5rem !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  height: auto !important;
  display: block !important;
}

/* Fix for feature metric labels */
.feature-metric-label {
  font-size: 0.9rem !important;
  line-height: 1.4 !important;
  height: auto !important;
  display: block !important;
}

/* Fix for metric wrappers */
.metric-wrapper {
  padding: 8px !important;
  margin: 10px auto !important;
  width: 90% !important;
  height: auto !important;
  display: block !important;
  box-sizing: border-box !important;
}

/* Fix for hero section to prevent content jumps */
.hero-content h1 {
  font-size: 3.5rem !important;
  line-height: 1.2 !important;
  margin-bottom: var(--spacing-24) !important;
  max-width: 900px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.hero-subtitle {
  font-size: 1.25rem !important;
  line-height: 1.6 !important;
  max-width: 800px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Fix for navbar to prevent jumps */
.navbar {
  height: 42px !important;
}

/* Fix for theme toggle to prevent jumps */
.theme-toggle {
  width: 37.4px !important;
  height: 18.7px !important;
}

.theme-toggle-slider {
  width: 15.3px !important;
  height: 15.3px !important;
}

/* Fix for buttons to maintain consistent dimensions */
.btn,
.cta-btn,
.service-cta-button,
.enhanced-cta-btn,
.bubble-cta-btn {
  transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease !important;
  padding: 10px 20px !important;
  height: auto !important;
  line-height: 1.5 !important;
}

/* Fix for section headers */
.section-header h2 {
  font-size: 2.25rem !important;
  line-height: 1.3 !important;
  margin-bottom: 0.5rem !important;
}

.section-header p {
  font-size: 1.1rem !important;
  line-height: 1.5 !important;
}

/* Fix for about section stats */
.stat-number {
  font-size: 2.5rem !important;
  line-height: 1.2 !important;
  font-weight: 700 !important;
}

.stat-label {
  font-size: 1rem !important;
  line-height: 1.4 !important;
}



/* Fix for solution cards */
.solution-card {
  padding: 20px !important;
  margin: 10px !important;
}

/* Fix for FAQ items */
.faq-question {
  padding: 15px !important;
  font-size: 1.1rem !important;
}

.faq-answer {
  padding: 0 15px 15px !important;
}

/* Fix for contact form */
.contact-form input,
.contact-form textarea {
  padding: 8px 12px !important; /* Reduced padding to decrease height */
  margin-bottom: 8px !important; /* Reduced margin to decrease height */
}

/* Fix for footer */
.footer-section {
  padding: 20px !important;
}

/* Fix for modal content */
.modal-content {
  padding: 20px !important;
  width: 80% !important;
  max-width: 400px !important;
}

/* Fix for preloader */
.preloader {
  background-color: var(--color-background) !important;
}

/* Fix for dark mode specific elements */
.dark-theme .feature-card .feature-metric,
.dark-theme .feature-card .feature-metric-label,
.dark-theme .feature-card .metric-wrapper,
body.dark-theme .feature-card .feature-metric,
body.dark-theme .feature-card .feature-metric-label,
body.dark-theme .feature-card .metric-wrapper {
  background-color: #1a1f36 !important;
}

/* Fix for light mode specific elements */
body:not(.dark-theme) .feature-card .feature-metric,
body:not(.dark-theme) .feature-card .feature-metric-label,
body:not(.dark-theme) .feature-card .metric-wrapper,
:not(.dark-theme) .feature-card .feature-metric,
:not(.dark-theme) .feature-card .feature-metric-label,
:not(.dark-theme) .feature-card .metric-wrapper {
  background-color: #ffffff !important;
  border: 1px solid rgba(37, 99, 235, 0.05);
}
