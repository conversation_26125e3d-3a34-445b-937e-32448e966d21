/**
 * Mobile Navigation
 * Handles mobile-specific navigation functionality
 */

(function() {
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Get elements
        const mobileMenuToggle = document.querySelector('.mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');
        const dropdownToggles = document.querySelectorAll('.navbar-dropdown .dropdown-toggle');
        
        // Function to check if device is mobile
        function isMobile() {
            return window.innerWidth <= 768;
        }
        
        // Function to initialize mobile navigation
        function initMobileNavigation() {
            if (isMobile()) {
                // Show mobile menu toggle
                if (mobileMenuToggle) {
                    mobileMenuToggle.style.display = 'block';
                }
                
                // Add click event to mobile menu toggle
                if (mobileMenuToggle && navLinks) {
                    mobileMenuToggle.addEventListener('click', function() {
                        // Toggle active class on mobile menu toggle
                        this.classList.toggle('active');
                        
                        // Toggle mobile-active class on nav links
                        navLinks.classList.toggle('mobile-active');
                        
                        // Toggle body scroll
                        if (navLinks.classList.contains('mobile-active')) {
                            document.body.style.overflow = 'hidden';
                        } else {
                            document.body.style.overflow = '';
                        }
                    });
                }
                
                // Handle dropdown toggles on mobile
                dropdownToggles.forEach(function(toggle) {
                    toggle.addEventListener('click', function(e) {
                        if (isMobile()) {
                            e.preventDefault();
                            e.stopPropagation();
                            
                            // Toggle active class on parent dropdown
                            const dropdown = this.closest('.navbar-dropdown');
                            if (dropdown) {
                                dropdown.classList.toggle('active');
                                
                                // Toggle dropdown menu visibility
                                const dropdownMenu = dropdown.querySelector('.dropdown-menu');
                                if (dropdownMenu) {
                                    if (dropdown.classList.contains('active')) {
                                        dropdownMenu.style.display = 'block';
                                    } else {
                                        dropdownMenu.style.display = 'none';
                                    }
                                }
                            }
                        }
                    });
                });
                
                // Close mobile menu when clicking outside
                document.addEventListener('click', function(e) {
                    if (isMobile() && navLinks && navLinks.classList.contains('mobile-active')) {
                        // Check if click is outside the mobile menu
                        if (!navLinks.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                            navLinks.classList.remove('mobile-active');
                            mobileMenuToggle.classList.remove('active');
                            document.body.style.overflow = '';
                        }
                    }
                });
                
                // Close mobile menu when clicking a nav link
                const navLinkItems = document.querySelectorAll('.nav-links .navbar-link:not(.dropdown-toggle)');
                navLinkItems.forEach(function(link) {
                    link.addEventListener('click', function() {
                        if (isMobile() && navLinks && navLinks.classList.contains('mobile-active')) {
                            navLinks.classList.remove('mobile-active');
                            mobileMenuToggle.classList.remove('active');
                            document.body.style.overflow = '';
                        }
                    });
                });
            } else {
                // Hide mobile menu toggle on desktop
                if (mobileMenuToggle) {
                    mobileMenuToggle.style.display = 'none';
                }
                
                // Reset mobile navigation state
                if (navLinks) {
                    navLinks.classList.remove('mobile-active');
                    navLinks.style.display = 'flex';
                }
                
                // Reset dropdown menus
                document.querySelectorAll('.navbar-dropdown').forEach(function(dropdown) {
                    dropdown.classList.remove('active');
                    const dropdownMenu = dropdown.querySelector('.dropdown-menu');
                    if (dropdownMenu) {
                        dropdownMenu.style.display = '';
                    }
                });
                
                // Reset body scroll
                document.body.style.overflow = '';
            }
        }
        
        // Initialize mobile navigation
        initMobileNavigation();
        
        // Update on window resize
        window.addEventListener('resize', function() {
            initMobileNavigation();
        });
        
        // Handle orientation change for mobile devices
        window.addEventListener('orientationchange', function() {
            initMobileNavigation();
        });
    });
})();
