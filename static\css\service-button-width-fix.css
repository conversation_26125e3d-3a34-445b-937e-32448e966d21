/**
 * Service Button Width Fix
 * This CSS ensures all service buttons have the same width and display properly
 */

/* Force all service CTA buttons to have consistent width and text display */
.service-cta-button {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  padding: 14px 20px !important;
  text-align: center !important;
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
  word-break: normal !important;
  word-wrap: normal !important;
  hyphens: none !important;
  line-height: 1.2 !important;
  min-height: 48px !important;
  height: auto !important;
  font-size: 1rem !important;
}

/* Target all service cards to ensure they have the same dimensions */
.service-card {
  width: 100% !important;
  max-width: none !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
  height: 520px !important;
  min-height: 520px !important;
  max-height: 520px !important;
  display: flex !important;
  flex-direction: column !important;
  position: relative !important;
  padding-bottom: 120px !important;
}

/* Ensure all service card footers have the same width and positioning */
.service-card-footer {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
  box-sizing: border-box !important;
  padding: 0 24px 24px !important;
  position: absolute !important;
  bottom: 30px !important;
  left: 0 !important;
  right: 0 !important;
}

/* Override any inline styles with !important */
.service-card[style*="width"],
.service-card-footer[style*="width"],
.service-cta-button[style*="width"] {
  width: 100% !important;
  max-width: 100% !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .service-cta-button {
    font-size: 0.95rem !important;
    padding: 12px 16px !important;
  }
}
