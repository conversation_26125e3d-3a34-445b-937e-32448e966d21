/**
 * MASTER NAVIGATION HANDLER
 * Unified solution for header highlighting and navigation
 * Eliminates flickering and provides smooth scroll-based highlighting
 */

(function() {
    'use strict';

    let isInitialized = false;
    let sections = [];
    let navLinks = [];
    let currentActiveSection = null;

    // Configuration
    const CONFIG = {
        headerOffset: 80,
        scrollThreshold: 400, // Increased to prevent early highlighting
        throttleDelay: 16, // ~60fps
        smoothScrollBehavior: 'smooth'
    };

    // Initialize the navigation system
    function init() {
        if (isInitialized) return;

        // Cache DOM elements
        sections = Array.from(document.querySelectorAll('section[id]'));
        navLinks = Array.from(document.querySelectorAll('.navbar-link, a[data-target]'));

        if (sections.length === 0 || navLinks.length === 0) {
            console.warn('Navigation: No sections or nav links found');
            return;
        }

        // Set up event listeners
        setupClickHandlers();
        setupScrollHandler();
        setupHashChangeHandler();

        // Set initial state
        setInitialHighlight();

        isInitialized = true;
        console.log('Navigation system initialized');
    }

    // Set up click event handlers for navigation links
    function setupClickHandlers() {
        navLinks.forEach(link => {
            // Remove existing listeners by cloning
            const newLink = link.cloneNode(true);
            if (link.parentNode) {
                link.parentNode.replaceChild(newLink, link);
            }

            newLink.addEventListener('click', handleNavClick);
        });

        // Update navLinks array with new elements
        navLinks = Array.from(document.querySelectorAll('.navbar-link, a[data-target]'));
    }

    // Handle navigation link clicks
    function handleNavClick(e) {
        const href = this.getAttribute('href');
        const dataTarget = this.getAttribute('data-target');

        // Determine target section ID
        let targetId = null;
        if (href && href.startsWith('/#')) {
            targetId = href.substring(2);
        } else if (href && href.startsWith('#')) {
            targetId = href.substring(1);
        } else if (dataTarget) {
            targetId = dataTarget;
        }

        if (!targetId) return;

        const targetSection = document.getElementById(targetId);
        if (!targetSection) return;

        e.preventDefault();

        // Update navigation highlighting immediately
        updateNavHighlight(targetId);

        // Smooth scroll to section
        scrollToSection(targetSection);

        // Update URL
        const newUrl = `/#${targetId}`;
        if (window.location.hash !== `#${targetId}`) {
            history.pushState(null, null, newUrl);
        }
    }

    // Scroll to a specific section
    function scrollToSection(section) {
        const elementPosition = section.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - CONFIG.headerOffset;

        window.scrollTo({
            top: offsetPosition,
            behavior: CONFIG.smoothScrollBehavior
        });
    }

    // Set up optimized scroll handler
    function setupScrollHandler() {
        let ticking = false;

        function handleScroll() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    updateHighlightOnScroll();
                    ticking = false;
                });
                ticking = true;
            }
        }

        window.addEventListener('scroll', handleScroll, { passive: true });
    }

    // Update highlighting based on scroll position
    function updateHighlightOnScroll() {
        const scrollPosition = window.scrollY + CONFIG.headerOffset;

        // Don't highlight anything if we're near the top of the page
        // This prevents automatic highlighting when page loads
        if (scrollPosition < CONFIG.scrollThreshold) {
            if (currentActiveSection !== null) {
                clearAllHighlights();
                currentActiveSection = null;
            }
            return;
        }

        // Find the current section using improved visibility detection
        let bestSection = null;
        let bestScore = 0;

        sections.forEach(section => {
            const rect = section.getBoundingClientRect();
            const sectionTop = rect.top + window.pageYOffset;
            const sectionBottom = sectionTop + rect.height;
            const viewportTop = window.pageYOffset + CONFIG.headerOffset;
            const viewportBottom = viewportTop + window.innerHeight;

            // Calculate visible area
            const visibleTop = Math.max(sectionTop, viewportTop);
            const visibleBottom = Math.min(sectionBottom, viewportBottom);
            const visibleHeight = Math.max(0, visibleBottom - visibleTop);

            // Calculate score based on visible area and position
            const totalHeight = rect.height;
            const visibilityRatio = visibleHeight / totalHeight;

            // Prefer sections that are more centered in viewport
            const sectionCenter = sectionTop + totalHeight / 2;
            const viewportCenter = viewportTop + window.innerHeight / 2;
            const centerDistance = Math.abs(sectionCenter - viewportCenter);
            const maxDistance = window.innerHeight;
            const centerScore = 1 - (centerDistance / maxDistance);

            // Combined score
            const score = visibilityRatio * 0.7 + centerScore * 0.3;

            if (score > bestScore && visibilityRatio > 0.1) {
                bestScore = score;
                bestSection = section;
            }
        });

        if (bestSection && bestSection.id !== currentActiveSection) {
            updateNavHighlight(bestSection.id);
            currentActiveSection = bestSection.id;
        }
    }

    // Update navigation highlighting
    function updateNavHighlight(activeSectionId) {
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            const dataTarget = link.getAttribute('data-target');

            let isActive = false;

            if (href === `/#${activeSectionId}` || href === `#${activeSectionId}`) {
                isActive = true;
            } else if (dataTarget === activeSectionId) {
                isActive = true;
            }

            // Update active state efficiently
            if (isActive && !link.classList.contains('active')) {
                link.classList.add('active');
            } else if (!isActive && link.classList.contains('active')) {
                link.classList.remove('active');
            }
        });
    }

    // Clear all navigation highlights
    function clearAllHighlights() {
        navLinks.forEach(link => {
            if (link.classList.contains('active')) {
                link.classList.remove('active');
            }
        });
    }

    // Set initial highlight based on URL hash
    function setInitialHighlight() {
        const hash = window.location.hash;

        if (hash && hash.startsWith('#')) {
            const targetId = hash.substring(1);
            const targetSection = document.getElementById(targetId);

            if (targetSection) {
                updateNavHighlight(targetId);
                currentActiveSection = targetId;

                // Scroll to section after a brief delay to ensure page is loaded
                setTimeout(() => {
                    scrollToSection(targetSection);
                }, 100);
            }
        } else {
            // Don't highlight anything on initial page load
            // Only start highlighting after user scrolls past the threshold
            clearAllHighlights();
            currentActiveSection = null;
        }
    }

    // Handle hash changes (browser back/forward)
    function setupHashChangeHandler() {
        window.addEventListener('hashchange', () => {
            setInitialHighlight();
        });
    }

    // Public API
    window.navigationHandler = {
        init: init,
        scrollToSection: function(sectionId) {
            const section = document.getElementById(sectionId);
            if (section) {
                updateNavHighlight(sectionId);
                scrollToSection(section);
            }
        }
    };

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // Re-initialize if needed (for dynamic content)
    window.addEventListener('load', () => {
        if (!isInitialized) {
            init();
        }
    });

})();