{% extends "base.html" %}

{% block title %}Page Not Found - Ziantrix{% endblock %}

{% block meta_description %}The page you're looking for doesn't exist or has been moved.{% endblock %}

{% block extra_head %}
<style>
    .error-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 70vh;
        text-align: center;
        padding: var(--spacing-lg);
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg, rgba(25, 118, 210, 0.03) 0%, rgba(25, 118, 210, 0.01) 100%);
    }

    .error-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 30% 30%, rgba(25, 118, 210, 0.05) 0%, transparent 25%),
            radial-gradient(circle at 70% 70%, rgba(25, 118, 210, 0.05) 0%, transparent 25%);
        z-index: -1;
        animation: pulse 8s infinite alternate;
    }

    .dark-theme .error-container {
        background: linear-gradient(135deg, rgba(100, 181, 246, 0.05) 0%, rgba(100, 181, 246, 0.02) 100%);
    }

    .dark-theme .error-container::before {
        background-image:
            radial-gradient(circle at 30% 30%, rgba(100, 181, 246, 0.08) 0%, transparent 25%),
            radial-gradient(circle at 70% 70%, rgba(100, 181, 246, 0.08) 0%, transparent 25%);
    }

    .error-animation {
        position: relative;
        margin-bottom: 2rem;
    }

    .error-circle {
        position: absolute;
        width: 280px;
        height: 280px;
        border-radius: 50%;
        background: linear-gradient(135deg, rgba(25, 118, 210, 0.1) 0%, rgba(25, 118, 210, 0.05) 100%);
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: -1;
        animation: pulse 3s infinite alternate;
        box-shadow: 0 0 30px rgba(25, 118, 210, 0.2);
    }

    .error-circle-outer {
        position: absolute;
        width: 320px;
        height: 320px;
        border-radius: 50%;
        border: 2px dashed rgba(25, 118, 210, 0.2);
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: -2;
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    .dark-theme .error-circle {
        background: linear-gradient(135deg, rgba(100, 181, 246, 0.15) 0%, rgba(100, 181, 246, 0.05) 100%);
        box-shadow: 0 0 30px rgba(100, 181, 246, 0.3);
    }

    .dark-theme .error-circle-outer {
        border: 2px dashed rgba(100, 181, 246, 0.2);
    }

    .error-code {
        font-size: 10rem;
        font-weight: bold;
        background: linear-gradient(135deg, #1976d2 0%, #64b5f6 100%);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
        margin: 0;
        line-height: 1;
        position: relative;
        z-index: 1;
        text-shadow: 0 5px 15px rgba(25, 118, 210, 0.3);
    }

    .dark-theme .error-code {
        background: linear-gradient(135deg, #64b5f6 0%, #90caf9 100%);
        -webkit-background-clip: text;
        background-clip: text;
        text-shadow: 0 5px 15px rgba(100, 181, 246, 0.5);
    }

    .error-message {
        font-size: 2.5rem;
        margin: 1rem 0 2rem;
        color: var(--color-text);
        font-weight: 600;
    }

    .error-description {
        max-width: 600px;
        margin-bottom: var(--spacing-lg);
        color: var(--color-text-light);
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .error-actions {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
    }

    .cta-btn {
        display: inline-block;
        text-decoration: none;
        text-align: center;
        min-width: 180px;
        padding: 12px 24px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 30px;
        transition: all 0.3s ease;
        border: none;
    }

    .cta-btn.primary {
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(25, 118, 210, 0.3);
    }

    .cta-btn.secondary {
        background: transparent;
        color: var(--color-text);
        border: 2px solid rgba(25, 118, 210, 0.3);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    .cta-btn.primary:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(25, 118, 210, 0.4);
    }

    .cta-btn.secondary:hover {
        transform: translateY(-3px);
        background-color: rgba(25, 118, 210, 0.05);
        border-color: rgba(25, 118, 210, 0.5);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .dark-theme .cta-btn.primary {
        background: linear-gradient(135deg, #42a5f5 0%, #1976d2 100%);
        box-shadow: 0 4px 15px rgba(100, 181, 246, 0.3);
    }

    .dark-theme .cta-btn.secondary {
        border: 2px solid rgba(100, 181, 246, 0.3);
        color: var(--color-text);
    }

    .dark-theme .cta-btn.primary:hover {
        box-shadow: 0 8px 25px rgba(100, 181, 246, 0.4);
    }

    .dark-theme .cta-btn.secondary:hover {
        background-color: rgba(100, 181, 246, 0.05);
        border-color: rgba(100, 181, 246, 0.5);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }

    @keyframes pulse {
        0% { opacity: 0.7; transform: translate(-50%, -50%) scale(0.95); }
        100% { opacity: 1; transform: translate(-50%, -50%) scale(1.05); }
    }

    @media (max-width: 768px) {
        .error-code {
            font-size: 7rem;
        }

        .error-message {
            font-size: 1.8rem;
        }

        .error-circle {
            width: 220px;
            height: 220px;
        }
    }

    @media (max-width: 480px) {
        .error-code {
            font-size: 5rem;
        }

        .error-message {
            font-size: 1.5rem;
        }

        .error-circle {
            width: 180px;
            height: 180px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="error-container">
    <div class="error-animation">
        <div class="error-circle"></div>
        <div class="error-circle-outer"></div>
        <h1 class="error-code">404</h1>
    </div>
    <h2 class="error-message">Page Not Found</h2>
    <p class="error-description">
        The page you're looking for doesn't exist or has been moved.
    </p>
    <div class="error-actions">
        <a href="/" class="cta-btn primary">Return to Home</a>
        <a href="javascript:history.back()" class="cta-btn secondary">Go Back</a>
    </div>
</div>
{% endblock %}
