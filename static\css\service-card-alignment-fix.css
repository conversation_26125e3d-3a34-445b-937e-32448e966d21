/**
 * Service Card Alignment Fix
 * This CSS ensures proper alignment of service cards and removes white boxes
 */

/* Service card base styles */
.service-card {
  background-color: transparent !important;
  border: 1px solid var(--color-border) !important;
  border-radius: 12px !important;
  box-shadow: none !important;
  overflow: hidden !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  display: flex !important;
  flex-direction: column !important;
  height: 520px !important; /* Fixed exact height for all cards */
  position: relative !important;
  min-height: 520px !important; /* Increased from 480px for more spacing */
  max-height: 520px !important; /* Ensure all cards have exactly the same height */
  padding-bottom: 120px !important; /* Increased from 100px for more space at bottom */
  box-sizing: border-box !important;
}

/* Service card header */
.service-card-header {
  display: flex !important;
  align-items: flex-start !important;
  padding: 24px 24px 20px !important;
  border-bottom: 1px solid var(--color-border) !important;
  text-align: left !important;
  background-color: transparent !important;
}

/* Service card body */
.service-card-body {
  flex: 1 !important;
  padding: 0 !important;
  padding-top: 16px !important; /* Increased from 8px to 16px for more top spacing */
  padding-bottom: 100px !important; /* Increased from 70px to 100px for more space before footer */
  position: relative !important;
  z-index: 1 !important;
  background-color: transparent !important;
}

/* Service features list */
.service-features-list {
  list-style-type: none !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 100% !important;
  background-color: transparent !important;
}

/* Service features list items */
.service-features-list li {
  display: flex !important;
  align-items: center !important;
  margin-bottom: 20px !important; /* Increased from 12px to 20px for more spacing */
  text-align: left !important;
  padding: 0 !important;
  width: 100% !important;
  justify-content: flex-start !important;
  padding-left: 24px !important;
  background-color: transparent !important;
}

/* Service features icons */
.service-features-list .feature-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-right: 16px !important;
  color: var(--color-primary) !important;
  background: none !important;
  width: 28px !important;
  height: 28px !important;
  flex-shrink: 0 !important;
  text-align: center !important;
  min-width: 28px !important;
  background-color: transparent !important;
}

/* Service features text */
.service-features-list .feature-text {
  display: inline-block !important;
  vertical-align: middle !important;
  line-height: 1.4 !important;
  text-align: left !important;
  padding: 0 !important;
  margin: 0 !important;
  background-color: transparent !important;
  color: var(--color-text) !important;
}

/* Service card footer */
.service-card-footer {
  position: absolute !important;
  bottom: 30px !important; /* Increased from 24px for more bottom spacing */
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  padding: 0 24px 24px !important;
  box-sizing: border-box !important;
  z-index: 10 !important;
  background-color: transparent !important;
  margin-top: auto !important;
  border-top: none !important;
}

/* Remove background from footer */
.service-card-footer::before {
  display: none !important;
  content: none !important;
}

/* Service CTA button */
.service-cta-button {
  display: block !important;
  width: 100% !important;
  min-width: 260px !important;
  padding: 14px 20px !important;
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-dark) 100%) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 1rem !important;
  text-align: center !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
  border: none !important;
  cursor: pointer !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
  z-index: 20 !important;
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: clip !important;
  box-sizing: border-box !important;
}

/* Remove background from button */
.service-cta-button::before {
  display: none !important;
  content: none !important;
}

/* Dark theme adjustments */
.dark-theme .service-card {
  background-color: transparent !important;
  border-color: var(--color-border-dark) !important;
}

.dark-theme .service-card-header {
  border-color: var(--color-border-dark) !important;
  background-color: transparent !important;
}

.dark-theme .service-features-list .feature-icon {
  color: var(--color-primary-light) !important;
}

.dark-theme .service-features-list .feature-text {
  color: var(--color-text-dark) !important;
}

/* Remove all backgrounds */
.service-card *,
.service-card-header *,
.service-card-body *,
.service-features-list *,
.service-features-list li *,
.service-card-footer * {
  background-color: transparent !important;
  box-shadow: none !important;
}

/* Ensure proper alignment of feature items */
.service-features-list li {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  justify-content: flex-start !important;
  padding-left: 24px !important;
  margin-left: 0 !important;
  text-align: left !important;
  width: 100% !important;
}

/* Specifically target the third service card (AI Agents) to ensure consistent spacing */
.services-grid .service-card:nth-child(3) .service-features-list li {
  margin-bottom: 20px !important; /* Increased from 12px to 20px to match other cards */
}

/* Ensure all service cards have the same height */
.services-grid .service-card {
  height: 520px !important; /* Fixed exact height for all cards */
  min-height: 520px !important; /* Increased from 480px for more spacing */
  max-height: 520px !important; /* Ensure all cards have exactly the same height */
}

/* Add extra spacing for the third card to ensure it matches the others */
.services-grid .service-card:nth-child(3) {
  height: 520px !important;
  min-height: 520px !important;
  max-height: 520px !important;
}

/* Specifically target the third service card button to ensure it has the same width */
.services-grid .service-card:nth-child(3) .service-cta-button {
  width: 100% !important;
  min-width: 260px !important;
  max-width: none !important;
  box-sizing: border-box !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
}

/* Ensure all service card footers have the same width and padding */
.services-grid .service-card-footer {
  width: 100% !important;
  padding: 0 24px 24px !important;
  box-sizing: border-box !important;
}
