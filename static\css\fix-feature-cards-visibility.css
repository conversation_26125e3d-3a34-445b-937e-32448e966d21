/**
 * Fix Feature Cards Visibility
 * Force display of feature cards in the "Why Choose Ziantrix" section
 */

/* Force display of features section */
#features,
.features-section,
#features.features-section,
section#features,
section.features-section {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  min-height: 200px !important;
  overflow: visible !important;
  position: relative !important;
  z-index: 1 !important;
  padding: 60px 0 !important;
  margin: 0 !important;
  background-color: var(--color-bg-primary, #f8fafc) !important;
}

/* Force display of features container */
#features .container,
.features-section .container,
#features.features-section .container {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  min-height: 100px !important;
  overflow: visible !important;
  position: relative !important;
  z-index: 2 !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 0 20px !important;
}

/* Force display of section header */
#features .section-header,
.features-section .section-header,
#features.features-section .section-header {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  min-height: 50px !important;
  overflow: visible !important;
  position: relative !important;
  z-index: 3 !important;
  text-align: center !important;
  margin-bottom: 40px !important;
}

/* Force display of features grid */
#features .features-grid,
.features-section .features-grid,
#features.features-section .features-grid {
  display: grid !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  min-height: 100px !important;
  overflow: visible !important;
  position: relative !important;
  z-index: 4 !important;
  grid-template-columns: repeat(3, 1fr) !important;
  gap: 30px !important;
  margin: 0 auto !important;
  padding: 0 !important;
}

/* Force display of feature cards */
#features .feature-card,
.features-section .feature-card,
#features.features-section .feature-card {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  height: auto !important;
  min-height: 280px !important;
  overflow: visible !important;
  position: relative !important;
  z-index: 5 !important;
  flex-direction: column !important;
  background-color: var(--color-card-bg, #ffffff) !important;
  border: 1px solid var(--color-border-light, #e2e8f0) !important;
  border-radius: 12px !important;
  padding: 24px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
}

/* Force display of feature card content */
#features .feature-card *,
.features-section .feature-card *,
#features.features-section .feature-card * {
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 6 !important;
}

/* Force display of feature icon */
#features .feature-icon,
.features-section .feature-icon,
#features.features-section .feature-icon {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  width: 60px !important;
  height: 60px !important;
  overflow: visible !important;
  position: relative !important;
  z-index: 7 !important;
  align-items: center !important;
  justify-content: center !important;
  background: linear-gradient(135deg, #dbeafe, #eff6ff) !important;
  border-radius: 12px !important;
  margin-bottom: 16px !important;
}

/* Force display of feature title */
#features .feature-title,
.features-section .feature-title,
#features.features-section .feature-title {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  color: var(--color-text-primary, #0f172a) !important;
  margin-bottom: 8px !important;
}

/* Force display of feature description */
#features .feature-description,
.features-section .feature-description,
#features.features-section .feature-description {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 1rem !important;
  color: var(--color-text-secondary, #334155) !important;
  margin-bottom: 16px !important;
}

/* Force display of feature metric */
#features .feature-metric,
.features-section .feature-metric,
#features.features-section .feature-metric {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 2rem !important;
  font-weight: 700 !important;
  color: var(--color-primary-600, #2563eb) !important;
  margin-bottom: 4px !important;
  background-color: transparent !important;
}

/* Force display of feature metric label */
#features .feature-metric-label,
.features-section .feature-metric-label,
#features.features-section .feature-metric-label {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  color: var(--color-text-tertiary, #64748b) !important;
  margin-bottom: 8px !important;
  text-transform: uppercase !important;
  background-color: transparent !important;
}

/* Force display of feature proof point */
#features .feature-proof-point,
.features-section .feature-proof-point,
#features.features-section .feature-proof-point {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 0.875rem !important;
  color: var(--color-text-secondary, #334155) !important;
  margin-bottom: 16px !important;
  background-color: transparent !important;
}

/* Force display of feature link */
#features .feature-link,
.features-section .feature-link,
#features.features-section .feature-link {
  display: inline-flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  color: var(--color-primary-600, #2563eb) !important;
  margin-top: auto !important;
  align-items: center !important;
  text-decoration: none !important;
}

/* Responsive styles */
@media (max-width: 1023px) {
  #features .features-grid,
  .features-section .features-grid,
  #features.features-section .features-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 767px) {
  #features .features-grid,
  .features-section .features-grid,
  #features.features-section .features-grid {
    grid-template-columns: 1fr !important;
  }
}
