/**
 * Chatbot Touch Fix
 * Improves touch interaction with the chatbot on mobile devices
 */

(function() {
    // Wait for DOM to be fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Get chatbot elements
        const chatbotWidget = document.getElementById('chatbot-widget');
        const chatbotLauncher = document.getElementById('chatbot-launcher');
        const chatbotMessages = document.querySelector('.chatbot-messages');
        const chatbotInput = document.getElementById('chatbot-input-field');
        const chatbotSendBtn = document.querySelector('.chatbot-input button');
        
        // Function to check if device is mobile
        function isMobile() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
        }
        
        // Function to initialize mobile chatbot
        function initMobileChatbot() {
            if (!chatbotWidget || !chatbotLauncher) return;
            
            if (isMobile()) {
                // Improve touch handling for chatbot launcher
                chatbotLauncher.addEventListener('touchstart', function(e) {
                    // Prevent default touch behavior
                    e.preventDefault();
                    
                    // Add active class for visual feedback
                    this.classList.add('touch-active');
                    
                    // Open chatbot
                    openChatbot();
                }, { passive: false });
                
                // Remove active class on touch end
                chatbotLauncher.addEventListener('touchend', function() {
                    this.classList.remove('touch-active');
                }, { passive: true });
                
                // Improve touch handling for chatbot close button
                const closeBtn = chatbotWidget.querySelector('.close-btn');
                if (closeBtn) {
                    closeBtn.addEventListener('touchstart', function(e) {
                        // Prevent default touch behavior
                        e.preventDefault();
                        
                        // Add active class for visual feedback
                        this.classList.add('touch-active');
                        
                        // Close chatbot
                        closeChatbot();
                    }, { passive: false });
                    
                    // Remove active class on touch end
                    closeBtn.addEventListener('touchend', function() {
                        this.classList.remove('touch-active');
                    }, { passive: true });
                }
                
                // Improve touch handling for chatbot minimize button
                const minimizeBtn = chatbotWidget.querySelector('.minimize-btn');
                if (minimizeBtn) {
                    minimizeBtn.addEventListener('touchstart', function(e) {
                        // Prevent default touch behavior
                        e.preventDefault();
                        
                        // Add active class for visual feedback
                        this.classList.add('touch-active');
                        
                        // Minimize chatbot
                        minimizeChatbot();
                    }, { passive: false });
                    
                    // Remove active class on touch end
                    minimizeBtn.addEventListener('touchend', function() {
                        this.classList.remove('touch-active');
                    }, { passive: true });
                }
                
                // Improve touch handling for chatbot send button
                if (chatbotSendBtn) {
                    chatbotSendBtn.addEventListener('touchstart', function(e) {
                        // Prevent default touch behavior
                        e.preventDefault();
                        
                        // Add active class for visual feedback
                        this.classList.add('touch-active');
                        
                        // Send message
                        sendMessage();
                    }, { passive: false });
                    
                    // Remove active class on touch end
                    chatbotSendBtn.addEventListener('touchend', function() {
                        this.classList.remove('touch-active');
                    }, { passive: true });
                }
                
                // Improve scrolling in chatbot messages
                if (chatbotMessages) {
                    // Enable momentum scrolling on iOS
                    chatbotMessages.style.webkitOverflowScrolling = 'touch';
                    
                    // Prevent body scrolling when touching chatbot messages
                    chatbotMessages.addEventListener('touchmove', function(e) {
                        // Allow scrolling within the chatbot messages
                        e.stopPropagation();
                    }, { passive: true });
                }
                
                // Fix for iOS keyboard issues
                if (chatbotInput) {
                    // Set font size to prevent zoom on focus
                    chatbotInput.style.fontSize = '16px';
                    
                    // Focus input when chatbot is opened
                    chatbotWidget.addEventListener('transitionend', function() {
                        if (chatbotWidget.classList.contains('active')) {
                            // Delay focus to ensure transition is complete
                            setTimeout(function() {
                                chatbotInput.focus();
                            }, 300);
                        }
                    });
                    
                    // Scroll to bottom when keyboard appears
                    chatbotInput.addEventListener('focus', function() {
                        // Delay scrolling to bottom to account for keyboard appearance
                        setTimeout(function() {
                            scrollChatToBottom();
                        }, 300);
                    });
                }
            }
        }
        
        // Initialize mobile chatbot
        initMobileChatbot();
        
        // Update on window resize
        window.addEventListener('resize', function() {
            initMobileChatbot();
        });
        
        // Handle orientation change for mobile devices
        window.addEventListener('orientationchange', function() {
            // Delay execution to ensure orientation change is complete
            setTimeout(function() {
                initMobileChatbot();
                
                // Scroll to bottom after orientation change
                if (chatbotMessages && chatbotWidget.classList.contains('active')) {
                    scrollChatToBottom();
                }
            }, 300);
        });
    });
})();
