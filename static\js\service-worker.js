// Service Worker for Ziantrix Web App
const CACHE_NAME = 'ziantrix-cache-v1';
const urlsToCache = [
  '/',
  '/static/css/style.css',
  '/static/css/enhancements.css',
  '/static/css/navigation.css',
  '/static/css/chatbot.css',
  '/static/css/login-enhancements.css',
  '/static/css/password-toggle.css',
  '/static/css/form-enhancements.css',
  '/static/js/navigation.js',
  '/static/js/chatbot.js',
  '/static/js/login-fix.js',
  '/static/js/network-status.js',
  '/static/js/modal-enhancements.js',
  '/static/js/form-submission.js',
  '/static/img/favicon.png',
  '/static/img/logo-icon.png',
  '/static/offline.html'
];

// Install event - cache resources
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

// Fetch event - serve from cache if available
self.addEventListener('fetch', event => {
  // Skip service worker for external domains, especially calendar links
  const url = event.request.url;
  if (url.includes('calendar.app.google') ||
      (!url.includes(self.location.origin) && url.startsWith('https'))) {
    // Let the browser handle external requests directly
    return;
  }

  event.respondWith(
    caches.match(event.request)
      .then(response => {
        // Cache hit - return response
        if (response) {
          return response;
        }

        // Clone the request
        const fetchRequest = event.request.clone();

        return fetch(fetchRequest)
          .then(response => {
            // Check if valid response
            if(!response || response.status !== 200 || response.type !== 'basic') {
              return response;
            }

            // Clone the response
            const responseToCache = response.clone();

            caches.open(CACHE_NAME)
              .then(cache => {
                // Don't cache API calls or dynamic content
                if (!event.request.url.includes('/api/') &&
                    !event.request.url.includes('/login') &&
                    !event.request.url.includes('/register') &&
                    !event.request.url.includes('/submit_form') &&
                    !event.request.url.includes('/submit_demo')) {
                  cache.put(event.request, responseToCache);
                }
              });

            return response;
          })
          .catch(error => {
            // Network error - serve offline page for HTML requests
            if (event.request.headers.get('Accept') &&
                event.request.headers.get('Accept').includes('text/html')) {
              return caches.match('/static/offline.html');
            }

            // For other resources, just show the error
            console.log('Fetch failed:', error);
            throw error;
          });
      })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  const cacheWhitelist = [CACHE_NAME];

  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});
