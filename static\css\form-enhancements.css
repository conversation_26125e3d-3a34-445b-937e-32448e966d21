/* Form Enhancements for consistent scaling */

/* Modal Improvements */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
    overflow-y: auto;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
}

.dark-theme .modal {
    background-color: #000000;
}

/* Modal Content Improvements */
.modal-content {
    width: 75%;
    max-width: 320px;
    padding: 12px;
    border-radius: 6px;
    background-color: var(--color-background);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    position: relative;
    transform: translateY(20px);
    transition: transform 0.3s ease;
    border: 1px solid var(--color-border);
}

.modal.active .modal-content {
    transform: translateY(0);
}

.dark-theme .modal-content {
    background-color: #121212;
    border-color: var(--color-border-dark);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 24px;
    color: var(--color-text-light);
    cursor: pointer;
    transition: color 0.2s ease, transform 0.2s ease;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal:hover {
    color: var(--color-primary);
    transform: scale(1.1);
}

/* Form Group Styling */
.form-group {
    margin-bottom: 8px;
}

.form-group label {
    display: block;
    margin-bottom: 3px;
    font-size: 11px;
    font-weight: 500;
    color: var(--color-text-secondary);
}

/* Input Field Styling */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
textarea,
select {
    width: 100%;
    padding: 6px 8px;
    border-radius: 3px;
    border: 1px solid var(--color-border);
    font-size: 11px;
    transition: all 0.3s ease;
    height: 28px;
    position: relative;
    z-index: 10;
    background-color: var(--color-background);
    color: var(--color-text);
}

textarea {
    height: auto;
    min-height: 40px;
    resize: vertical;
}

/* Select dropdown styling */
.select-wrapper {
    position: relative;
}

.select-wrapper select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 2.5rem;
    cursor: pointer;
}

.select-arrow {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: var(--color-text-secondary);
}

/* Textarea with tooltip */
.textarea-wrapper {
    position: relative;
}

.tooltip {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    color: var(--color-text-secondary);
    cursor: pointer;
    z-index: 11;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 250px;
    background-color: white;
    color: #333;
    text-align: center;
    border-radius: 6px;
    padding: 0.5rem;
    position: absolute;
    z-index: 12;
    bottom: 125%;
    left: 50%;
    transform: translateX(-90%);
    opacity: 0;
    transition: opacity 0.3s;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    font-size: 0.85rem;
    font-weight: normal;
    border: 1px solid var(--color-border);
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Submit Button Styling */
.submit-btn {
    width: 100%;
    height: 32px;
    font-size: 11px;
    font-weight: 600;
    margin-top: 10px;
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(25, 118, 210, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(25, 118, 210, 0.2);
}

/* Dark Theme Adjustments */
.dark-theme input[type="text"],
.dark-theme input[type="email"],
.dark-theme input[type="password"],
.dark-theme textarea,
.dark-theme select {
    background-color: #1e1e1e;
    border-color: var(--color-border);
    color: var(--color-text);
    position: relative;
    z-index: 10;
}

.dark-theme .select-arrow {
    color: rgba(255, 255, 255, 0.6);
}

.dark-theme .tooltip {
    color: rgba(255, 255, 255, 0.6);
}

.dark-theme .tooltip .tooltiptext {
    background-color: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.dark-theme .submit-btn {
    background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.dark-theme .submit-btn:hover {
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #2196f3 0%, #1565c0 100%);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .modal-content {
        width: 90%;
        padding: 20px;
    }

    input[type="text"],
    input[type="email"],
    input[type="password"],
    textarea {
        padding: 6px 8px; /* Further reduced padding for mobile to decrease height */
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 95%;
        padding: 15px;
    }
}
