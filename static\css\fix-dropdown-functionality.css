/**
 * Fix Dropdown Functionality
 * Ensures dropdown menus work correctly while preventing highlighting
 */

/* Make dropdown menus visible on hover */
.navbar-dropdown:hover .dropdown-menu,
.dropdown-toggle:hover + .dropdown-menu,
.dropdown-menu:hover {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

/* Make dropdown items clickable */
.dropdown-menu a,
.dropdown-menu .dropdown-item,
.dropdown-menu a:hover,
.dropdown-menu .dropdown-item:hover {
    display: block !important;
    pointer-events: auto !important;
    cursor: pointer !important;
    color: inherit !important;
    font-weight: normal !important;
    border: none !important;
    border-bottom: none !important;
    background-color: transparent !important;
    box-shadow: none !important;
    text-shadow: none !important;
    transform: none !important;
    text-decoration: none !important;
    transition: none !important;
}

/* Ensure dropdown menu is visible */
.dropdown-menu {
    display: none;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    min-width: 200px !important;
    background-color: white !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    border: 1px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 4px !important;
    z-index: 1000 !important;
    padding: 5px 0 !important;
}

/* Dark theme dropdown menu */
.dark-theme .dropdown-menu {
    background-color: #1e293b !important;
    border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Dropdown item styling */
.dropdown-menu a,
.dropdown-item {
    padding: 8px 16px !important;
    display: block !important;
    width: 100% !important;
    text-align: left !important;
    font-size: 14px !important;
    transition: none !important;
}

/* Dropdown item hover effect */
.dropdown-menu a:hover,
.dropdown-item:hover {
    background-color: rgba(25, 118, 210, 0.1) !important;
}

/* Dark theme dropdown item hover effect */
.dark-theme .dropdown-menu a:hover,
.dark-theme .dropdown-item:hover {
    background-color: rgba(100, 181, 246, 0.1) !important;
}

/* Specifically target Resources dropdown */
.navbar-link[data-target="faq"],
.navbar-link[href="/#faq"],
a[data-target="faq"],
a[href="/#faq"] {
    position: relative !important;
}

/* Ensure Resources dropdown menu is visible on hover */
.navbar-link[data-target="faq"]:hover + .dropdown-menu,
.navbar-link[href="/#faq"]:hover + .dropdown-menu,
a[data-target="faq"]:hover + .dropdown-menu,
a[href="/#faq"]:hover + .dropdown-menu {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}
