/* Chatbot Styles */
.chatbot-widget {
    position: fixed;
    bottom: 65px;
    right: 20px;
    width: 400px; /* Increased width for wider messages */
    height: 450px; /* Maintained height */
    background: #e6f2ff; /* Even brighter, more vibrant background */
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(33, 150, 243, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 999;
    transform: translateY(100%);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease, box-shadow 0.3s ease;
    border: none;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    padding: 0; /* Remove any padding */
    margin: 0; /* Remove any margin */
}

.chatbot-launcher {
    position: fixed;
    bottom: 65px;
    right: 20px;
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    z-index: 998;
    transition: all 0.3s ease;
    overflow: hidden;
    padding: 0;
    background-image: url('../img/chatbot-launcher-vintage.svg');
    background-size: 100% 100%; /* Ensure image covers the entire circle */
    background-position: center center; /* Center the image perfectly */
    background-repeat: no-repeat;
    background-color: transparent; /* No background color */
    transform-origin: center;
    animation: vintage-rotate 60s linear infinite;
}

.chatbot-launcher::before {
    content: none;
}

.chatbot-launcher:hover::before {
    opacity: 0;
}

.chatbot-launcher::after {
    content: none;
}

@keyframes classic-pulse {
    0% {
        transform: scale(0.9);
        opacity: 0;
    }
    40% {
        opacity: 0.4;
    }
    100% {
        transform: scale(1.2);
        opacity: 0;
    }
}

.chatbot-launcher:hover {
    transform: scale(1.05) rotate(5deg);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
    animation-play-state: paused;
}

@keyframes vintage-rotate {
    0% { transform: rotate(0deg); }
    25% { transform: rotate(1deg); }
    50% { transform: rotate(0deg); }
    75% { transform: rotate(-1deg); }
    100% { transform: rotate(0deg); }
}

.launcher-icon {
    position: relative;
    z-index: 2;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    animation: gentle-rotate 8s linear infinite;
}

.launcher-icon img {
    width: 40px;
    height: 40px;
    opacity: 0; /* Hide the image since we're using background-image */
}

@keyframes gentle-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Dark theme chatbot launcher */
.dark-theme .chatbot-launcher {
    background-image: url('../img/chatbot-launcher-dark.svg');
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    background-color: transparent; /* No background color */
    background-size: 100% 100%; /* Ensure image covers the entire circle */
    background-position: center center; /* Center the image perfectly */
    filter: none; /* Remove brightness/contrast filters */
}

.dark-theme .chatbot-launcher:hover {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
    filter: none; /* Remove brightness/contrast filters */
}

.launcher-pulse {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background-color: transparent;
    z-index: 1;
    animation: none;
}

.dark-theme .launcher-pulse {
    background-color: transparent;
    animation: none;
}

@keyframes dark-glow {
    0% { opacity: 0.3; transform: scale(0.95); background-color: rgba(147, 112, 219, 0.2); }
    100% { opacity: 0.6; transform: scale(1.05); background-color: rgba(147, 112, 219, 0.4); }
}

@keyframes classic-glow {
    0% { opacity: 0.2; transform: scale(0.95); }
    100% { opacity: 0.5; transform: scale(1.05); }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    70% {
        transform: scale(1.2);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 0;
    }
}

@keyframes pulse-glow {
    0% {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(212, 175, 55, 0.2);
    }
    100% {
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.4), 0 0 0 3px rgba(212, 175, 55, 0.3);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
    40% {transform: translateY(-5px);}
    60% {transform: translateY(-2px);}
}

/* Removed duplicate dark theme styles */

.chatbot-widget.active {
    transform: translateY(0);
    opacity: 1;
}

.chatbot-header {
    background: linear-gradient(135deg, #6200EA 0%, #3D5AFE 100%); /* Vibrant purple to blue gradient */
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.chatbot-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0) 100%);
    transform: translateX(-100%);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.dark-theme .chatbot-widget {
    background: #1e2745; /* Match the body background */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4), 0 0 0 2px rgba(66, 165, 245, 0.2);
}

.dark-theme .chatbot-header {
    background: linear-gradient(135deg, #7C4DFF 0%, #448AFF 100%); /* Brighter gradient for dark mode */
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.chatbot-title {
    display: flex;
    align-items: center;
    color: white;
    font-weight: 600;
}

.chatbot-logo {
    width: 28px;
    height: 28px;
    margin-right: 10px;
    border-radius: 50%;
    background: linear-gradient(135deg, #FF4081 0%, #FF9100 100%); /* Vibrant pink to orange gradient */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    padding: 2px;
    overflow: hidden;
    transition: transform 0.3s ease, filter 0.3s ease;
    animation: logo-pulse 3s infinite alternate;
}

.chatbot-logo:hover {
    transform: scale(1.1) rotate(5deg);
    filter: brightness(1.2);
}

@keyframes logo-pulse {
    0% { filter: brightness(1); transform: scale(1); }
    100% { filter: brightness(1.2); transform: scale(1.05); }
}

/* Dark theme logo */
.dark-theme .chatbot-logo {
    background: linear-gradient(135deg, #FF9100 0%, #FF4081 100%); /* Reversed gradient for dark mode */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 145, 0, 0.3);
}
.dark-theme .chatbot-logo img {
    content: url('../img/robot-icon-vibrant.svg');
    filter: brightness(1.2);
}

.chatbot-controls button {
    background: rgba(255, 255, 255, 0.3);
    border: none;
    color: white;
    font-size: 18px; /* Further reduced size */
    font-weight: bold;
    cursor: pointer;
    margin-left: 8px; /* Reduced spacing between buttons */
    opacity: 1;
    transition: all 0.3s ease;
    width: 30px; /* Further reduced size */
    height: 30px; /* Further reduced size */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    position: relative;
    z-index: 100; /* Higher z-index to prevent flickering */
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3); /* Stronger shadow for better visibility */
    -webkit-tap-highlight-color: transparent; /* Prevent highlight on mobile */
    -webkit-touch-callout: none; /* Prevent callout on mobile */
    user-select: none; /* Prevent text selection */
    pointer-events: auto !important; /* Ensure clicks are captured */
}

.chatbot-controls button:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.4); /* Stronger highlight effect on hover */
    transform: scale(1.1); /* Slight scale effect on hover */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.4); /* Enhanced shadow on hover */
}

.chatbot-controls button:active {
    transform: scale(0.95); /* Click effect */
    background-color: rgba(255, 255, 255, 0.5); /* Even stronger highlight on active */
}

.chatbot-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #e6f2ff; /* Even brighter, more vibrant background */
    margin: 0;
    padding: 0;
    overflow: hidden; /* Prevent any overflow */
}

.chatbot-messages {
    flex: 1;
    padding: 8px;
    padding-bottom: 0; /* Remove bottom padding */
    overflow-y: scroll !important;
    background-color: #e6f2ff; /* Even brighter, more vibrant background */
    display: flex;
    flex-direction: column;
    scrollbar-width: thin;
    scrollbar-color: rgba(33, 150, 243, 0.4) transparent;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(33, 150, 243, 0.2) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(33, 150, 243, 0.2) 0%, transparent 40%),
        linear-gradient(to bottom, rgba(230, 242, 255, 0.7) 0%, rgba(230, 242, 255, 0.9) 100%);
    background-attachment: fixed;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    max-height: 300px; /* Reduced max height to match smaller chatbot */
    margin: 0; /* Remove any margin */
}

.dark-theme .chatbot-body {
    background-color: #1e2745; /* Brighter, more vibrant dark background */
    margin: 0;
    padding: 0;
    overflow: hidden; /* Prevent any overflow */
}

.dark-theme .chatbot-messages {
    background-color: #1e2745; /* Brighter, more vibrant dark background */
    background-image:
        radial-gradient(circle at 20% 20%, rgba(66, 165, 245, 0.25) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(66, 165, 245, 0.25) 0%, transparent 40%),
        linear-gradient(to bottom, rgba(30, 39, 69, 0.7) 0%, rgba(30, 39, 69, 0.9) 100%);
}

.chatbot-messages::-webkit-scrollbar {
    width: 8px;
}

.chatbot-messages::-webkit-scrollbar-track {
    background: transparent;
    margin: 4px 0;
}

.chatbot-messages::-webkit-scrollbar-thumb {
    background-color: rgba(25, 118, 210, 0.2);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: padding-box;
}

.chatbot-messages::-webkit-scrollbar-thumb:hover {
    background-color: rgba(25, 118, 210, 0.4);
}

.dark-theme .chatbot-messages::-webkit-scrollbar-thumb {
    background-color: rgba(100, 181, 246, 0.2);
}

.dark-theme .chatbot-messages::-webkit-scrollbar-thumb:hover {
    background-color: rgba(100, 181, 246, 0.4);
}

.message {
    margin-bottom: 10px;
    display: flex;
    align-items: center; /* Changed from flex-start to center for better avatar alignment */
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.message-appear {
    animation: messageAppear 0.3s ease-out;
}

@keyframes messageAppear {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.scroll-indicator {
    position: fixed;
    bottom: 90px;
    right: 40px;
    width: 32px;
    height: 32px;
    background: rgba(66, 133, 244, 0.9);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.25);
    opacity: 1;
    transition: all 0.3s ease;
    z-index: 1000;
    animation: pulse-scroll 2s infinite;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    border: 2px solid rgba(255, 255, 255, 0.5);
    font-size: 18px; /* Smaller icon */
}

@keyframes pulse-scroll {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.7);
    }
    70% {
        transform: scale(1.1);
        box-shadow: 0 0 0 10px rgba(25, 118, 210, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(25, 118, 210, 0);
    }
}

.scroll-indicator:hover {
    transform: translateY(-3px) scale(1.1);
    background: rgba(25, 118, 210, 1);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.scroll-indicator:active {
    transform: scale(0.95);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.dark-theme .scroll-indicator {
    background: rgba(100, 181, 246, 0.8);
}

.dark-theme .scroll-indicator:hover {
    background: rgba(100, 181, 246, 1);
}

.bot-message {
    margin-right: 5px;
    align-items: flex-start; /* Align items at the start for bot messages */
}

.user-message {
    flex-direction: row-reverse;
    margin-left: 5px;
    justify-content: flex-end;
}

.message-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
    background: linear-gradient(135deg, #0D47A1 0%, #1565C0 100%); /* Darker blue gradient */
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(13, 71, 161, 0.3);
    position: relative;
    z-index: 2;
    background-image: url('../img/robot-icon-solid.svg');
    background-size: 75%;
    background-position: center;
    background-repeat: no-repeat;
    align-self: flex-start; /* Ensure avatar stays at the top of the message */
    margin-top: 5px; /* Add a small top margin for better alignment */
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Add hover effect */
.bot-message:hover .message-avatar {
    animation: none !important; /* Stop the subtle animation on hover */
    transform: scale(1.1) rotate(5deg) !important; /* Override any other transforms with rotation */
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.4), 0 0 0 2px rgba(13, 71, 161, 0.5);
}

/* Add subtle animation to robot icon */
.message.bot-message .message-avatar {
    animation: vibrant-pulse 3s ease-in-out infinite;
}

@keyframes vibrant-pulse {
    0% { transform: scale(1); filter: brightness(1); }
    50% { transform: scale(1.05); filter: brightness(1.1); }
    100% { transform: scale(1); filter: brightness(1); }
}

.message-avatar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    z-index: -1;
}

.message-avatar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.2) 0%, rgba(13, 71, 161, 0.2) 100%);
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.bot-message:hover .message-avatar::after {
    opacity: 1;
    animation: pulse-glow 1.5s infinite alternate;
}

@keyframes pulse-glow {
    0% { opacity: 0.2; }
    100% { opacity: 0.6; }
}

/* Add special animation for new messages */
.message.bot-message {
    position: relative;
}

.message.bot-message.message-appear .message-avatar {
    animation: robot-appear 0.5s ease-out;
}

@keyframes robot-appear {
    0% { transform: scale(0) rotate(-45deg); opacity: 0; }
    50% { transform: scale(1.2) rotate(10deg); }
    100% { transform: scale(1) rotate(0); opacity: 1; }
}

.message-avatar i {
    display: none; /* Hide the icon since we're using background image */
}

/* Hide avatar only for user messages */
.user-message .message-avatar {
    display: none;
}

.user-message .message-avatar {
    margin-right: 0;
    margin-left: 10px;
    background-color: var(--color-secondary);
}

.message-content {
    background-color: white;
    padding: 12px 16px; /* Reduced padding for more compact messages */
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05), 0 0 0 1px rgba(0, 0, 0, 0.02);
    position: relative;
    max-width: 85%; /* Slightly reduced max-width for better proportions */
    min-width: 50px; /* Ensure minimum width */
    transition: all 0.3s ease;
    margin-bottom: 5px; /* Add space between message and buttons */
    align-self: center; /* Center align the message content */
}

.message-content:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(0, 0, 0, 0.04);
    transform: translateY(-1px);
}

.dark-theme .message-content {
    background-color: #2a3142;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05);
    color: #e0e0e0;
}

.dark-theme .message-content:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.08);
}

/* Dark theme robot icon */
.dark-theme .message-avatar {
    background: linear-gradient(135deg, #1565C0 0%, #0D47A1 100%); /* Reversed darker gradient for dark mode */
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4), 0 0 0 2px rgba(21, 101, 192, 0.3);
    background-image: url('../img/robot-icon-solid.svg'); /* Use the same solid icon */
    background-size: 75%;
    background-position: center;
    background-repeat: no-repeat;
    filter: brightness(1.1); /* Make it slightly brighter in dark mode */
}

.dark-theme .bot-message:hover .message-avatar {
    box-shadow: 0 5px 12px rgba(0, 0, 0, 0.5), 0 0 0 2px rgba(21, 101, 192, 0.5);
    filter: brightness(1.2) drop-shadow(0 0 5px rgba(21, 101, 192, 0.5));
}

.bot-message .message-content {
    border-bottom-left-radius: 4px;
    background: linear-gradient(135deg, #0D47A1 0%, #1565C0 100%); /* Darker blue gradient */
    color: white;
    border-left: 3px solid #64B5F6; /* Light blue accent */
    width: auto;
    box-shadow: 0 4px 12px rgba(13, 71, 161, 0.3);
    animation: message-glow 3s infinite alternate;
}

@keyframes message-glow {
    0% { box-shadow: 0 4px 12px rgba(13, 71, 161, 0.3); }
    100% { box-shadow: 0 4px 15px rgba(13, 71, 161, 0.5); }
}

.bot-message .message-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: -8px;
    width: 16px;
    height: 16px;
    background-color: #0D47A1; /* Darker blue to match gradient */
    clip-path: polygon(0 0, 100% 100%, 100% 0);
    border-bottom-right-radius: 8px;
}

.dark-theme .bot-message .message-content {
    background: linear-gradient(135deg, #42a5f5 0%, #1976d2 100%);
    color: white;
    border-left: 3px solid #90caf9;
}

.dark-theme .bot-message .message-content::after {
    background-color: #1976d2;
}

.user-message .message-content {
    border-bottom-right-radius: 4px;
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    color: white;
    border-right: 3px solid #81c784;
    width: auto;
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.user-message .message-content::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: -8px;
    width: 16px;
    height: 16px;
    background-color: #1b5e20;
    clip-path: polygon(0 100%, 0 0, 100% 100%);
    border-bottom-left-radius: 8px;
}

.dark-theme .user-message .message-content {
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    color: white;
    border-right: 3px solid #81c784;
}

.dark-theme .user-message .message-content::after {
    background-color: #1b5e20;
}

/* Scroll arrow inside message bubbles - hidden as requested */
.message-content .scroll-arrow {
    display: none; /* Hide scroll arrows in message bubbles */
}

/* Main scroll indicator styles */
.scroll-indicator {
    background-color: #1976d2;
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    animation: pulse-scroll 2s infinite;
}

.scroll-indicator:hover {
    background-color: #1565c0;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

@keyframes pulse-scroll {
    0% { transform: translateY(0); box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); }
    50% { transform: translateY(-5px); box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3); }
    100% { transform: translateY(0); box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); }
}

.message-content p {
    margin: 0;
    line-height: 1.5; /* Maintained line height */
    font-size: 16px; /* Increased font size */
    letter-spacing: 0.1px; /* Maintained letter spacing */
}

.typing-indicator-container {
    display: none;
    margin-bottom: 15px;
}

.typing-indicator {
    display: flex;
    align-items: center;
    margin-right: auto;
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    padding: 8px 12px;
    border-radius: 16px;
    border-bottom-left-radius: 4px;
    width: 60px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05);
    border-left: 3px solid #64b5f6;
}

.typing-indicator .dot {
    height: 6px;
    width: 6px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    display: inline-block;
    margin-right: 4px;
    animation: typing 1.4s infinite ease-in-out;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.typing-indicator .dot:nth-child(1) {
    animation-delay: 0s;
}

.typing-indicator .dot:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
    animation-delay: 0.4s;
    margin-right: 0;
}

@keyframes typing {
    0% { transform: translateY(0px) scale(1); opacity: 0.5; }
    50% { transform: translateY(-5px) scale(1.1); opacity: 1; }
    100% { transform: translateY(0px) scale(1); opacity: 0.5; }
}

.chatbot-input {
    display: flex;
    padding: 10px 15px;
    background-color: #d4e6ff; /* Brighter blue background to complement the chat area */
    border-top: 1px solid rgba(33, 150, 243, 0.2);
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    position: relative;
    margin: 0; /* Remove any margin */
    margin-top: 0; /* Ensure no top margin */
    margin-bottom: 0; /* Ensure no bottom margin */
}

.chatbot-input::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, rgba(25, 118, 210, 0.2) 0%, rgba(25, 118, 210, 0.4) 50%, rgba(25, 118, 210, 0.2) 100%);
}

.dark-theme .chatbot-input {
    background-color: #1e2745; /* Match the body background for consistency */
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
    margin: 0; /* Remove any margin */
    margin-top: 0; /* Ensure no top margin */
    margin-bottom: 0; /* Ensure no bottom margin */
}

.dark-theme .chatbot-input::before {
    background: linear-gradient(90deg, rgba(100, 181, 246, 0.2) 0%, rgba(100, 181, 246, 0.4) 50%, rgba(100, 181, 246, 0.2) 100%);
}

.chatbot-input input {
    flex: 1;
    border: none;
    padding: 8px 14px;
    border-radius: 20px;
    background-color: white;
    color: #333;
    outline: none;
    font-size: 14px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.chatbot-input input:focus {
    background-color: #ffffff;
    color: #333333;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(100, 181, 246, 0.5);
}

.dark-theme .chatbot-input input {
    background-color: #2a2a2a;
    color: white;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

.dark-theme .chatbot-input input:focus {
    background-color: #3a3a3a;
    color: white;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3), 0 0 0 2px rgba(100, 181, 246, 0.5);
}

.chatbot-input .send-btn {
    background: linear-gradient(135deg, #2196f3 0%, #1976d2 100%);
    color: white;
    border: none;
    width: 34px;
    height: 34px;
    border-radius: 50%;
    margin-left: 12px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: relative;
    bottom: 0; /* Changed from 5px to 0 to prevent extra space */
}

.chatbot-input .send-btn:hover {
    background: linear-gradient(135deg, #1e88e5 0%, #1976d2 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.chatbot-input .send-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

.dark-theme .chatbot-input .send-btn {
    background: linear-gradient(135deg, #42a5f5 0%, #1976d2 100%);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.dark-theme .chatbot-input .send-btn:hover {
    background: linear-gradient(135deg, #64b5f6 0%, #2196f3 100%);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Typing indicator removed */
