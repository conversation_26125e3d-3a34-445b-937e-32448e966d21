/**
 * Emergency Search Button Visibility Fix
 * This script ensures the search button is always visible
 */

// Execute immediately
(function() {
    function fixSearchButton() {
        const searchButton = document.getElementById('searchButton');
        if (searchButton) {
            // Force button to be visible
            searchButton.style.display = 'inline-block';
            searchButton.style.visibility = 'visible';
            searchButton.style.opacity = '1';
            searchButton.style.position = 'absolute';
            searchButton.style.right = '8px';
            searchButton.style.top = '50%';
            searchButton.style.transform = 'translateY(-50%)';
            searchButton.style.height = '36px';
            searchButton.style.minWidth = '80px';
            searchButton.style.backgroundColor = '#4299e1';
            searchButton.style.color = 'white';
            searchButton.style.border = 'none';
            searchButton.style.borderRadius = '6px';
            searchButton.style.padding = '0 15px';
            searchButton.style.fontWeight = '500';
            searchButton.style.fontSize = '0.95rem';
            searchButton.style.lineHeight = '36px';
            searchButton.style.cursor = 'pointer';
            searchButton.style.zIndex = '999';
            searchButton.style.textAlign = 'center';

            // Make sure we don't add duplicate text
            if (searchButton.textContent.trim() === '') {
                searchButton.textContent = 'Search';
            } else if (searchButton.textContent.includes('SearchSearch')) {
                searchButton.textContent = 'Search';
            }
        }
    }

    // Try to fix immediately
    fixSearchButton();

    // Also fix when DOM is loaded
    document.addEventListener('DOMContentLoaded', fixSearchButton);

    // And fix after a short delay
    setTimeout(fixSearchButton, 500);
    setTimeout(fixSearchButton, 1000);
})();
